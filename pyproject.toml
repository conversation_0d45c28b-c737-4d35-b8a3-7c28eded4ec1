[project]
name = "flycheck-python"
version = "0.1.0"
description = "医保飞行检查系统 - Python版本"
authors = [
    {name = "FlyCheck Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "duckdb>=0.9.0",
    "duckdb-engine>=0.9.0",
    "sqlalchemy>=2.0.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-multipart>=0.0.6",
    "jinja2>=3.1.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "pandas>=2.1.0",
    "openpyxl>=3.1.0",
    "xlsxwriter>=3.1.0",
    "python-dateutil>=2.8.0",
    "loguru>=0.7.0",
    "httpx>=0.25.0",
    "asyncpg>=0.29.0",
    "alembic>=1.13.0",
    "celery>=5.3.0",
    "redis>=5.0.0",
    "websockets>=12.0",
    "aiofiles>=23.2.0",
    "typer>=0.9.0",
    "rich>=13.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["flycheck"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[project.scripts]
flycheck = "flycheck.cli:app"
