package com.taikang.fly.check.rest.ManyDataSourceResultController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.service.ManyDataSourceResultService;
import com.taikang.fly.check.dto.dataSourceResult.MDataExportDto;
import javax.servlet.http.HttpServletRequest;
import java.lang.Integer;
import com.taikang.fly.check.dto.dataSourceResult.MDataSourceResultSearchDto;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.comm.NativePage;

public class ManyDataSourceResultController	// class@00029e from classes.dex
{
    private ManyDataSourceResultService manyDataSourceResultService;
    private static final Logger log;

    static {
       ManyDataSourceResultController.log = LoggerFactory.getLogger(ManyDataSourceResultController.class);
    }
    public void ManyDataSourceResultController(){
       super();
    }
    public void downloadManyResult(String hospNames,HttpServletResponse response){
       this.manyDataSourceResultService.downloadManyResult(hospNames, response);
    }
    public void downloadResultsByName(String path,HttpServletResponse response){
       this.manyDataSourceResultService.downloadResultsByName(path, response);
    }
    public void executeBatchMDS(MDataExportDto mDataExportDto,HttpServletResponse response,HttpServletRequest request){
       this.manyDataSourceResultService.executeBatchMDS(mDataExportDto, response, request);
    }
    public CommResponse manyDataSourceResultList(Integer page,Integer size,MDataSourceResultSearchDto mDataSourceResultSearchDto){
       return CommResponse.success(this.manyDataSourceResultService.manyDataSourceResultList(page, size, mDataSourceResultSearchDto));
    }
}
