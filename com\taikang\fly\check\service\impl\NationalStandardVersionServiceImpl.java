package com.taikang.fly.check.service.impl.NationalStandardVersionServiceImpl;
import com.taikang.fly.check.service.NationalStandardVersionService;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.NationalStandardVersionDto;
import com.taikang.fly.check.comm.NativePage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.NationalStandardVersionMapper;

public class NationalStandardVersionServiceImpl implements NationalStandardVersionService	// class@00031b from classes.dex
{
    NationalStandardVersionMapper nationalStandardVersionMapper;

    public void NationalStandardVersionServiceImpl(){
       super();
    }
    public NativePage queryHerbalList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List nationalStandardVersionDtos = this.nationalStandardVersionMapper.queryHerbalList(nationalStandardVersionDto);
       NativePage pageDto = new NativePage(nationalStandardVersionDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
    public NativePage queryICD10List(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List nationalStandardVersionDtos = this.nationalStandardVersionMapper.queryICD10List(nationalStandardVersionDto);
       NativePage pageDto = new NativePage(nationalStandardVersionDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
    public NativePage queryICD9List(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List nationalStandardVersionDtos = this.nationalStandardVersionMapper.queryICD9List(nationalStandardVersionDto);
       NativePage pageDto = new NativePage(nationalStandardVersionDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
    public NativePage queryPatentList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List nationalStandardVersionDtos = this.nationalStandardVersionMapper.queryPatentList(nationalStandardVersionDto);
       NativePage pageDto = new NativePage(nationalStandardVersionDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
    public NativePage queryTreatmentList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List nationalStandardVersionDtos = this.nationalStandardVersionMapper.queryTreatmentList(nationalStandardVersionDto);
       NativePage pageDto = new NativePage(nationalStandardVersionDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
    public NativePage queryWesternList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List nationalStandardVersionDtos = this.nationalStandardVersionMapper.queryWesternList(nationalStandardVersionDto);
       NativePage pageDto = new NativePage(nationalStandardVersionDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
}
