package com.taikang.fly.check.dto.flyRule.FlyRuleTableRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class FlyRuleTableRespDto implements Serializable	// class@00010d from classes.dex
{
    private String tables;
    private Integer total;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleTableRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleTableRespDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleTableRespDto) {
             b = false;
          }else {
             FlyRuleTableRespDto uFlyRuleTabl = o;
             if (!uFlyRuleTabl.canEqual(this)) {
                b = false;
             }else {
                String tables = this.getTables();
                String tables1 = uFlyRuleTabl.getTables();
                if (tables == null) {
                   if (tables1 != null) {
                      b = false;
                   }
                }else if(tables.equals(tables1)){
                }
                Integer total = this.getTotal();
                Integer total1 = uFlyRuleTabl.getTotal();
                if (total == null) {
                   if (total1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!total.equals(total1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getTables(){
       return this.tables;
    }
    public Integer getTotal(){
       return this.total;
    }
    public int hashCode(){
       String $tables;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tables = this.getTables()) == null)? i: $tables.hashCode();
       result = i1 + 59;
       Integer $total = this.getTotal();
       i1 = result * 59;
       if ($total != null) {
          i = $total.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setTables(String tables){
       this.tables = tables;
    }
    public void setTotal(Integer total){
       this.total = total;
    }
    public String toString(){
       return "FlyRuleTableRespDto\(tables="+this.getTables()+", total="+this.getTotal()+"\)";
    }
}
