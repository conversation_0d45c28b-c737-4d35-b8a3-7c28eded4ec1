<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <header class="layout-header">
      <div class="header-left">
        <el-icon class="logo-icon" size="24">
          <DataBoard />
        </el-icon>
        <h1 class="logo-title">FlyCheck Python</h1>
        <span class="logo-subtitle">医保飞行检查系统</span>
      </div>
      <div class="header-right">
        <el-dropdown>
          <span class="user-info">
            <el-icon><User /></el-icon>
            <span>管理员</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人设置</el-dropdown-item>
              <el-dropdown-item divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 主体内容 -->
    <div class="layout-content">
      <!-- 侧边栏 -->
      <aside class="layout-sidebar">
        <el-menu
          :default-active="activeMenu"
          :collapse="false"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <!-- 单级菜单 -->
            <el-menu-item
              v-if="!route.children || route.children.length === 1"
              :index="route.children ? route.children[0].path : route.path"
            >
              <el-icon>
                <component :is="route.meta?.icon || 'Document'" />
              </el-icon>
              <span>{{ route.meta?.title || route.children?.[0]?.meta?.title }}</span>
            </el-menu-item>

            <!-- 多级菜单 -->
            <el-sub-menu v-else :index="route.path">
              <template #title>
                <el-icon>
                  <component :is="route.meta?.icon || 'Document'" />
                </el-icon>
                <span>{{ route.meta?.title }}</span>
              </template>
              <el-menu-item
                v-for="child in route.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon>
                  <component :is="child.meta?.icon || 'Document'" />
                </el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </aside>

      <!-- 主内容区 -->
      <main class="layout-main">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 菜单路由（过滤掉隐藏的路由）
const menuRoutes = computed(() => {
  return router.getRoutes()
    .filter(route => route.path !== '/' && !route.meta?.hidden)
    .filter(route => route.children && route.children.length > 0)
    .filter(route => !route.children.some(child => child.meta?.hidden))
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  color: #fff;
}

.logo-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.logo-subtitle {
  font-size: 14px;
  opacity: 0.8;
  margin-left: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.layout-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 250px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.layout-main {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f0f2f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sidebar {
    width: 200px;
  }
  
  .layout-main {
    padding: 16px;
  }
  
  .logo-subtitle {
    display: none;
  }
}
</style>
