# FlyCheck Python 环境配置示例
# 复制此文件为 .env 并修改相应配置

# 应用配置
FLYCHECK_APP_NAME=FlyCheck Python
FLYCHECK_DEBUG=true
FLYCHECK_HOST=0.0.0.0
FLYCHECK_PORT=8000

# 数据库配置
FLYCHECK_DATABASE_URL=duckdb:///./flycheck.db
FLYCHECK_DATABASE_ECHO=false

# 安全配置
FLYCHECK_SECRET_KEY=your-secret-key-change-in-production
FLYCHECK_ALGORITHM=HS256
FLYCHECK_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis配置
FLYCHECK_REDIS_URL=redis://localhost:6379/0

# 文件上传配置
FLYCHECK_UPLOAD_DIR=./uploads
FLYCHECK_MAX_FILE_SIZE=104857600

# 日志配置
FLYCHECK_LOG_LEVEL=INFO
FLYCHECK_LOG_FILE=./logs/flycheck.log

# 分页配置
FLYCHECK_DEFAULT_PAGE_SIZE=20
FLYCHECK_MAX_PAGE_SIZE=1000

# 规则引擎配置
FLYCHECK_RULE_TIMEOUT=300
FLYCHECK_MAX_CONCURRENT_RULES=10

# 数据清洗配置
FLYCHECK_BATCH_SIZE=1000
