package com.taikang.fly.check.dto.workorderpool.WorkOrderPoolAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class WorkOrderPoolAddDto implements Serializable	// class@0001c9 from classes.dex
{
    private String demandSource;
    private String diagnosisType;
    private String isSpecial;
    private String policyBasis;
    private String redField1;
    private String region;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleDimension;
    private String ruleLevel;
    private String ruleName;
    private String ruleScopeApply;
    private String ruleState;
    private String ruleSuitTimeEnd;
    private String ruleSuitTimeStart;
    private static final long serialVersionUID = 0x1;

    public void WorkOrderPoolAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof WorkOrderPoolAddDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof WorkOrderPoolAddDto){
          b = false;
       }else {
          WorkOrderPoolAddDto workOrderPoo = o;
          if (!workOrderPoo.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = workOrderPoo.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String region = this.getRegion();
             String region1 = workOrderPoo.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String ruleState = this.getRuleState();
             String ruleState1 = workOrderPoo.getRuleState();
             if (ruleState == null) {
                if (ruleState1 != null) {
                   b = false;
                }
             }else if(ruleState.equals(ruleState1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = workOrderPoo.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = workOrderPoo.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = workOrderPoo.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = workOrderPoo.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                label_00bd :
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = workOrderPoo.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String redField1 = this.getRedField1();
             String redField11 = workOrderPoo.getRedField1();
             if (redField1 == null) {
                if (redField11 != null) {
                label_00ed :
                   b = false;
                }
             }else if(redField1.equals(redField11)){
             }
             String demandSource = this.getDemandSource();
             String demandSource1 = workOrderPoo.getDemandSource();
             if (demandSource == null) {
                if (demandSource1 != null) {
                   b = false;
                }
             }else if(demandSource.equals(demandSource1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = workOrderPoo.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_011d :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String isSpecial = this.getIsSpecial();
             String isSpecial1 = workOrderPoo.getIsSpecial();
             if (isSpecial == null) {
                if (isSpecial1 != null) {
                   b = false;
                }
             }else if(isSpecial.equals(isSpecial1)){
             }
             String ruleSuitTime = this.getRuleSuitTimeStart();
             String ruleSuitTime1 = workOrderPoo.getRuleSuitTimeStart();
             if (ruleSuitTime == null) {
                if (ruleSuitTime1 != null) {
                label_014d :
                   b = false;
                }
             }else if(ruleSuitTime.equals(ruleSuitTime1)){
             }
             String ruleSuitTime2 = this.getRuleSuitTimeEnd();
             String ruleSuitTime3 = workOrderPoo.getRuleSuitTimeEnd();
             if (ruleSuitTime2 == null) {
                if (ruleSuitTime3 != null) {
                   b = false;
                }
             }else if(ruleSuitTime2.equals(ruleSuitTime3)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = workOrderPoo.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                label_0181 :
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String ruleDimensio = this.getRuleDimension();
             String ruleDimensio1 = workOrderPoo.getRuleDimension();
             if (ruleDimensio == null) {
                if (ruleDimensio1 != null) {
                   b = false;
                }
             }else if(ruleDimensio.equals(ruleDimensio1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDemandSource(){
       return this.demandSource;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getIsSpecial(){
       return this.isSpecial;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRedField1(){
       return this.redField1;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleDimension(){
       return this.ruleDimension;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public String getRuleState(){
       return this.ruleState;
    }
    public String getRuleSuitTimeEnd(){
       return this.ruleSuitTimeEnd;
    }
    public String getRuleSuitTimeStart(){
       return this.ruleSuitTimeStart;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $region = this.getRegion();
       int i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $ruleState = this.getRuleState();
       i1 = result * 59;
       i = ($ruleState == null)? 43: $ruleState.hashCode();
       result = i1 + i;
       String $ruleLevel = this.getRuleLevel();
       i1 = result * 59;
       i = ($ruleLevel == null)? 43: $ruleLevel.hashCode();
       result = i1 + i;
       String $ruleCategory1 = this.getRuleCategory1();
       i1 = result * 59;
       i = ($ruleCategory1 == null)? 43: $ruleCategory1.hashCode();
       result = i1 + i;
       String ruleCategory = this.getRuleCategory2();
       i1 = result * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String redField1 = this.getRedField1();
       i1 = (i1 + i) * 59;
       i = (redField1 == null)? 43: redField1.hashCode();
       String demandSource = this.getDemandSource();
       i1 = (i1 + i) * 59;
       i = (demandSource == null)? 43: demandSource.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String isSpecial = this.getIsSpecial();
       i1 = (i1 + i) * 59;
       i = (isSpecial == null)? 43: isSpecial.hashCode();
       String ruleSuitTime = this.getRuleSuitTimeStart();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime == null)? 43: ruleSuitTime.hashCode();
       String ruleSuitTime1 = this.getRuleSuitTimeEnd();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime1 == null)? 43: ruleSuitTime1.hashCode();
       String ruleScopeApp = this.getRuleScopeApply();
       i1 = (i1 + i) * 59;
       i = (ruleScopeApp == null)? 43: ruleScopeApp.hashCode();
       String ruleDimensio = this.getRuleDimension();
       i1 = (i1 + i) * 59;
       i = (ruleDimensio == null)? 43: ruleDimensio.hashCode();
       return (i1 + i);
    }
    public void setDemandSource(String demandSource){
       this.demandSource = demandSource;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setIsSpecial(String isSpecial){
       this.isSpecial = isSpecial;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRedField1(String redField1){
       this.redField1 = redField1;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleDimension(String ruleDimension){
       this.ruleDimension = ruleDimension;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setRuleState(String ruleState){
       this.ruleState = ruleState;
    }
    public void setRuleSuitTimeEnd(String ruleSuitTimeEnd){
       this.ruleSuitTimeEnd = ruleSuitTimeEnd;
    }
    public void setRuleSuitTimeStart(String ruleSuitTimeStart){
       this.ruleSuitTimeStart = ruleSuitTimeStart;
    }
    public String toString(){
       return "WorkOrderPoolAddDto\(ruleName="+this.getRuleName()+", region="+this.getRegion()+", ruleState="+this.getRuleState()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", redField1="+this.getRedField1()+", demandSource="+this.getDemandSource()+", policyBasis="+this.getPolicyBasis()+", isSpecial="+this.getIsSpecial()+", ruleSuitTimeStart="+this.getRuleSuitTimeStart()+", ruleSuitTimeEnd="+this.getRuleSuitTimeEnd()+", ruleScopeApply="+this.getRuleScopeApply()+", ruleDimension="+this.getRuleDimension()+"\)";
    }
}
