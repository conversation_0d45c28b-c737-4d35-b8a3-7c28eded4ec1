package org.springframework.boot.loader.jar.Bytes;
import java.lang.Object;

final class Bytes	// class@00054a from classes.dex
{

    private void Bytes(){
       super();
    }
    public static long littleEndianValue(byte[] bytes,int offset,int length){
       long value = 0;
       for (int i = length - 1; i >= 0; i--) {
          long l = value << 8;
          int ix = offset + i;
          ix = bytes[ix] & 0x00ff;
          value = l | (long)ix;
       }
       return value;
    }
}
