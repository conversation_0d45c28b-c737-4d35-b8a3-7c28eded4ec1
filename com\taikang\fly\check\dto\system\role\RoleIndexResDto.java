package com.taikang.fly.check.dto.system.role.RoleIndexResDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class RoleIndexResDto implements Serializable	// class@0001b5 from classes.dex
{
    private boolean check;
    private String createTime;
    private String creator;
    private String roleCode;
    private String roleId;
    private String roleName;
    private static final long serialVersionUID = 0x1;

    public void RoleIndexResDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleIndexResDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof RoleIndexResDto){
          b = false;
       }else {
          RoleIndexResDto roleIndexRes = o;
          if (!roleIndexRes.canEqual(this)) {
             b = false;
          }else {
             String roleId = this.getRoleId();
             String roleId1 = roleIndexRes.getRoleId();
             if (roleId == null) {
                if (roleId1 != null) {
                   b = false;
                }
             }else if(roleId.equals(roleId1)){
             }
             String roleCode = this.getRoleCode();
             String roleCode1 = roleIndexRes.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             String roleName = this.getRoleName();
             String roleName1 = roleIndexRes.getRoleName();
             if (roleName == null) {
                if (roleName1 != null) {
                   b = false;
                }
             }else if(roleName.equals(roleName1)){
             }
             String creator = this.getCreator();
             String creator1 = roleIndexRes.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = roleIndexRes.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             if (this.isCheck() != roleIndexRes.isCheck()) {
                b = false;
             }else {
                b = true;
             }
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getRoleId(){
       return this.roleId;
    }
    public String getRoleName(){
       return this.roleName;
    }
    public int hashCode(){
       String $roleId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($roleId = this.getRoleId()) == null)? i: $roleId.hashCode();
       result = i1 + 59;
       String $roleCode = this.getRoleCode();
       int i2 = result * 59;
       i1 = ($roleCode == null)? i: $roleCode.hashCode();
       result = i2 + i1;
       String $roleName = this.getRoleName();
       i2 = result * 59;
       i1 = ($roleName == null)? i: $roleName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTime = this.getCreateTime();
       i1 = result * 59;
       if ($createTime != null) {
          i = $createTime.hashCode();
       }
       result = i1 + i;
       i = result * 59;
       i1 = (this.isCheck())? 79: 97;
       return (i + i1);
    }
    public boolean isCheck(){
       return this.check;
    }
    public void setCheck(boolean check){
       this.check = check;
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setRoleId(String roleId){
       this.roleId = roleId;
    }
    public void setRoleName(String roleName){
       this.roleName = roleName;
    }
    public String toString(){
       return "RoleIndexResDto\(roleId="+this.getRoleId()+", roleCode="+this.getRoleCode()+", roleName="+this.getRoleName()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", check="+this.isCheck()+"\)";
    }
}
