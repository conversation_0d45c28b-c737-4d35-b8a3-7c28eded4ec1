package com.taikang.fly.check.dto.jsoncreate.MatchingField;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MatchingField	// class@00013a from classes.dex
{
    private String ckFieldName;
    private String oraFieldName;

    public void MatchingField(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MatchingField;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MatchingField) {
             b = false;
          }else {
             MatchingField matchingFiel = o;
             if (!matchingFiel.canEqual(this)) {
                b = false;
             }else {
                String oraFieldName = this.getOraFieldName();
                String oraFieldName1 = matchingFiel.getOraFieldName();
                if (oraFieldName == null) {
                   if (oraFieldName1 != null) {
                      b = false;
                   }
                }else if(oraFieldName.equals(oraFieldName1)){
                }
                String ckFieldName = this.getCkFieldName();
                String ckFieldName1 = matchingFiel.getCkFieldName();
                if (ckFieldName == null) {
                   if (ckFieldName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!ckFieldName.equals(ckFieldName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCkFieldName(){
       return this.ckFieldName;
    }
    public String getOraFieldName(){
       return this.oraFieldName;
    }
    public int hashCode(){
       String $oraFieldName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($oraFieldName = this.getOraFieldName()) == null)? i: $oraFieldName.hashCode();
       result = i1 + 59;
       String $ckFieldName = this.getCkFieldName();
       i1 = result * 59;
       if ($ckFieldName != null) {
          i = $ckFieldName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCkFieldName(String ckFieldName){
       this.ckFieldName = ckFieldName;
    }
    public void setOraFieldName(String oraFieldName){
       this.oraFieldName = oraFieldName;
    }
    public String toString(){
       return "MatchingField\(oraFieldName="+this.getOraFieldName()+", ckFieldName="+this.getCkFieldName()+"\)";
    }
}
