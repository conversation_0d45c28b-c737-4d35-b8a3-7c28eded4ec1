package com.taikang.fly.check.utils.StringSplitUtils;
import java.lang.Object;
import java.lang.String;
import java.util.ArrayList;
import java.lang.CharSequence;
import java.util.List;
import java.util.Arrays;
import java.util.Iterator;
import java.lang.StringBuilder;
import java.util.stream.Stream;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public class StringSplitUtils	// class@00034d from classes.dex
{

    public void StringSplitUtils(){
       super();
    }
    public static String getSqlParam(String str){
       String splitInfo = null;
       if (str != null) {
          ArrayList uArrayList = new ArrayList();
          if (str.contains(",")) {
             uArrayList = Arrays.asList(str.split(","));
          }else {
             uArrayList.add(str);
          }
          ArrayList uArrayList1 = new ArrayList();
          Iterator iterator = uArrayList.iterator();
          while (iterator.hasNext()) {
             uArrayList1.add("".append("\'").append(iterator.next()).append("\'").toString());
          }
          splitInfo = uArrayList1.stream().collect(Collectors.joining(","));
       }
       return splitInfo;
    }
}
