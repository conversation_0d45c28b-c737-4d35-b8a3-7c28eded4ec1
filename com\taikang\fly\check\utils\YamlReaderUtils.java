package com.taikang.fly.check.utils.YamlReaderUtils;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.lang.Object;
import java.lang.String;
import java.lang.System;
import java.io.PrintStream;
import org.yaml.snakeyaml.Yaml;
import java.lang.ClassLoader;
import java.io.InputStream;
import java.util.Map;
import java.lang.CharSequence;
import java.lang.Throwable;

public class YamlReaderUtils	// class@00034f from classes.dex
{
    public static final YamlReaderUtils INSTANCE;
    private static final Logger log;
    private static Map properties;

    static {
       YamlReaderUtils.log = LoggerFactory.getLogger(YamlReaderUtils.class);
       YamlReaderUtils.properties = new HashMap();
       YamlReaderUtils.INSTANCE = new YamlReaderUtils();
    }
    public void YamlReaderUtils(){
       super();
    }
    public static void main(String[] args){
       Object serverHost = YamlReaderUtils.INSTANCE.getValueByKey("spring.profiles.active", "application.yml");
       System.out.println(serverHost);
    }
    public Object getValueByKey(String key,String yamlFileName){
       Object obj;
       int ix1;
       try{
          Yaml yaml = new Yaml();
          InputStream in = YamlReaderUtils.class.getClassLoader().getResourceAsStream(yamlFileName);
          try{
             ix1 = 0;
             YamlReaderUtils.properties = yaml.loadAs(in, HashMap.class);
             if (in != null) {
                if (0) {
                   in.close();
                }else {
                   in.close();
                }
             }
          }catch(java.lang.Throwable e7){
             throw e7;
          }catch(java.lang.Exception e0){
             YamlReaderUtils.log.error("Init yaml failed!", e0);
          }catch(java.lang.Throwable e7){
             ix1.addSuppressed(e7);
          }
          String separator = ".";
          String[] separatorKeys = null;
          if (key.contains(separator)) {
             separatorKeys = key.split("\\.");
             Map finalValue = new HashMap();
             int i = 0;
             while (true) {
                int ix = separatorKeys.length - 1;
                if (i < ix) {
                   if (!i) {
                      finalValue = YamlReaderUtils.properties.get(separatorKeys[i]);
                   }else if(finalValue == null){
                   label_007b :
                      if (finalValue == null) {
                         obj = null;
                         break ;
                      }else {
                         obj = finalValue.get(separatorKeys[(separatorKeys.length - 1)]);
                         break ;
                      }
                   }else {
                      finalValue = finalValue.get(separatorKeys[i]);
                   }
                   i++;
                }else {
                   goto label_007b ;
                }
             }
          }else {
             obj = YamlReaderUtils.properties.get(key);
          }
          return obj;
       }catch(java.lang.Exception e0){
       }
    }
}
