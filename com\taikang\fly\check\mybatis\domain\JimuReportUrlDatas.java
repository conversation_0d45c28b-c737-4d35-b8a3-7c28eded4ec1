package com.taikang.fly.check.mybatis.domain.JimuReportUrlDatas;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class JimuReportUrlDatas	// class@00024e from classes.dex
{
    private String id;
    private String ruleName;
    private String tableName;
    private String type;
    private String urlData;

    public void JimuReportUrlDatas(){
       super();
    }
    public void JimuReportUrlDatas(String id,String tableName,String ruleName,String urlData,String type){
       super();
       this.id = id;
       this.tableName = tableName;
       this.ruleName = ruleName;
       this.urlData = urlData;
       this.type = type;
    }
    protected boolean canEqual(Object other){
       return other instanceof JimuReportUrlDatas;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof JimuReportUrlDatas) {
             b = false;
          }else {
             JimuReportUrlDatas jimuReportUr = o;
             if (!jimuReportUr.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = jimuReportUr.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String tableName = this.getTableName();
                String tableName1 = jimuReportUr.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = jimuReportUr.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String urlData = this.getUrlData();
                String urlData1 = jimuReportUr.getUrlData();
                if (urlData == null) {
                   if (urlData1 != null) {
                      b = false;
                   }
                }else if(urlData.equals(urlData1)){
                }
                String type = this.getType();
                String type1 = jimuReportUr.getType();
                if (type == null) {
                   if (type1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!type.equals(type1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getType(){
       return this.type;
    }
    public String getUrlData(){
       return this.urlData;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tableName = this.getTableName();
       int i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $urlData = this.getUrlData();
       i2 = result * 59;
       i1 = ($urlData == null)? i: $urlData.hashCode();
       result = i2 + i1;
       String $type = this.getType();
       i1 = result * 59;
       if ($type != null) {
          i = $type.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public void setType(String type){
       this.type = type;
    }
    public void setUrlData(String urlData){
       this.urlData = urlData;
    }
    public String toString(){
       return "JimuReportUrlDatas\(id="+this.getId()+", tableName="+this.getTableName()+", ruleName="+this.getRuleName()+", urlData="+this.getUrlData()+", type="+this.getType()+"\)";
    }
}
