package com.taikang.fly.check.dto.ybDrugInstructions.YbDrugInstructionsSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class YbDrugInstructionsSearchDto implements Serializable	// class@0001d1 from classes.dex
{
    private String applicable;
    private String genericName;
    private String tradeName;
    private static final long serialVersionUID = 0x1;

    public void YbDrugInstructionsSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbDrugInstructionsSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof YbDrugInstructionsSearchDto) {
             b = false;
          }else {
             YbDrugInstructionsSearchDto ybDrugInstru = o;
             if (!ybDrugInstru.canEqual(this)) {
                b = false;
             }else {
                String genericName = this.getGenericName();
                String genericName1 = ybDrugInstru.getGenericName();
                if (genericName == null) {
                   if (genericName1 != null) {
                      b = false;
                   }
                }else if(genericName.equals(genericName1)){
                }
                String tradeName = this.getTradeName();
                String tradeName1 = ybDrugInstru.getTradeName();
                if (tradeName == null) {
                   if (tradeName1 != null) {
                      b = false;
                   }
                }else if(tradeName.equals(tradeName1)){
                }
                String applicable = this.getApplicable();
                String applicable1 = ybDrugInstru.getApplicable();
                if (applicable == null) {
                   if (applicable1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!applicable.equals(applicable1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getApplicable(){
       return this.applicable;
    }
    public String getGenericName(){
       return this.genericName;
    }
    public String getTradeName(){
       return this.tradeName;
    }
    public int hashCode(){
       String $genericName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($genericName = this.getGenericName()) == null)? i: $genericName.hashCode();
       result = i1 + 59;
       String $tradeName = this.getTradeName();
       int i2 = result * 59;
       i1 = ($tradeName == null)? i: $tradeName.hashCode();
       result = i2 + i1;
       String $applicable = this.getApplicable();
       i1 = result * 59;
       if ($applicable != null) {
          i = $applicable.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setApplicable(String applicable){
       this.applicable = applicable;
    }
    public void setGenericName(String genericName){
       this.genericName = genericName;
    }
    public void setTradeName(String tradeName){
       this.tradeName = tradeName;
    }
    public String toString(){
       return "YbDrugInstructionsSearchDto\(genericName="+this.getGenericName()+", tradeName="+this.getTradeName()+", applicable="+this.getApplicable()+"\)";
    }
}
