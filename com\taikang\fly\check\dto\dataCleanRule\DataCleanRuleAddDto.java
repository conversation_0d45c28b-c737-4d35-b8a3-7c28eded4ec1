package com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleAddDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DataCleanRuleAddDto	// class@0000dc from classes.dex
{
    private String cleanRule;
    private String explain;
    private String fieldName;
    private String tableName;

    public void DataCleanRuleAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataCleanRuleAddDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DataCleanRuleAddDto) {
             b = false;
          }else {
             DataCleanRuleAddDto uDataCleanRu = o;
             if (!uDataCleanRu.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = uDataCleanRu.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String fieldName = this.getFieldName();
                String fieldName1 = uDataCleanRu.getFieldName();
                if (fieldName == null) {
                   if (fieldName1 != null) {
                      b = false;
                   }
                }else if(fieldName.equals(fieldName1)){
                }
                String cleanRule = this.getCleanRule();
                String cleanRule1 = uDataCleanRu.getCleanRule();
                if (cleanRule == null) {
                   if (cleanRule1 != null) {
                      b = false;
                   }
                }else if(cleanRule.equals(cleanRule1)){
                }
                String explain = this.getExplain();
                String explain1 = uDataCleanRu.getExplain();
                if (explain == null) {
                   if (explain1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!explain.equals(explain1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCleanRule(){
       return this.cleanRule;
    }
    public String getExplain(){
       return this.explain;
    }
    public String getFieldName(){
       return this.fieldName;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       String $fieldName = this.getFieldName();
       int i2 = result * 59;
       i1 = ($fieldName == null)? i: $fieldName.hashCode();
       result = i2 + i1;
       String $cleanRule = this.getCleanRule();
       i2 = result * 59;
       i1 = ($cleanRule == null)? i: $cleanRule.hashCode();
       result = i2 + i1;
       String $explain = this.getExplain();
       i1 = result * 59;
       if ($explain != null) {
          i = $explain.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCleanRule(String cleanRule){
       this.cleanRule = cleanRule;
    }
    public void setExplain(String explain){
       this.explain = explain;
    }
    public void setFieldName(String fieldName){
       this.fieldName = fieldName;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "DataCleanRuleAddDto\(tableName="+this.getTableName()+", fieldName="+this.getFieldName()+", cleanRule="+this.getCleanRule()+", explain="+this.getExplain()+"\)";
    }
}
