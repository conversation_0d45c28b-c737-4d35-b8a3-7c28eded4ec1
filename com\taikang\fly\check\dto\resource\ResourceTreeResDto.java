package com.taikang.fly.check.dto.resource.ResourceTreeResDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class ResourceTreeResDto implements Serializable	// class@0001b1 from classes.dex
{
    private List children;
    private String iclass;
    private String icon;
    private String id;
    private String isMenuORresource;
    private String text;
    private String url;
    private static final long serialVersionUID = 0x1;

    public void ResourceTreeResDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ResourceTreeResDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ResourceTreeResDto){
          b = false;
       }else {
          ResourceTreeResDto resourceTree = o;
          if (!resourceTree.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = resourceTree.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String text = this.getText();
             String text1 = resourceTree.getText();
             if (text == null) {
                if (text1 != null) {
                   b = false;
                }
             }else if(text.equals(text1)){
             }
             String url = this.getUrl();
             String url1 = resourceTree.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String icon = this.getIcon();
             String icon1 = resourceTree.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String iclass = this.getIclass();
             String iclass1 = resourceTree.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             List children = this.getChildren();
             List children1 = resourceTree.getChildren();
             if (children == null) {
                if (children1 != null) {
                label_009a :
                   b = false;
                }
             }else if(children.equals(children1)){
             }
             String isMenuORreso = this.getIsMenuORresource();
             String isMenuORreso1 = resourceTree.getIsMenuORresource();
             if (isMenuORreso == null) {
                if (isMenuORreso1 != null) {
                   b = false;
                }
             }else if(isMenuORreso.equals(isMenuORreso1)){
             }
             b = true;
          }
       }
       return b;
    }
    public List getChildren(){
       return this.children;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getId(){
       return this.id;
    }
    public String getIsMenuORresource(){
       return this.isMenuORresource;
    }
    public String getText(){
       return this.text;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $text = this.getText();
       int i2 = result * 59;
       i1 = ($text == null)? i: $text.hashCode();
       result = i2 + i1;
       String $url = this.getUrl();
       i2 = result * 59;
       i1 = ($url == null)? i: $url.hashCode();
       result = i2 + i1;
       String $icon = this.getIcon();
       i2 = result * 59;
       i1 = ($icon == null)? i: $icon.hashCode();
       result = i2 + i1;
       String $iclass = this.getIclass();
       i2 = result * 59;
       i1 = ($iclass == null)? i: $iclass.hashCode();
       result = i2 + i1;
       List children = this.getChildren();
       i2 = result * 59;
       i1 = (children == null)? i: children.hashCode();
       String isMenuORreso = this.getIsMenuORresource();
       i1 = (i2 + i1) * 59;
       if (isMenuORreso != null) {
          i = isMenuORreso.hashCode();
       }
       return (i1 + i);
    }
    public void setChildren(List children){
       this.children = children;
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setIcon(String icon){
       this.icon = icon;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsMenuORresource(String isMenuORresource){
       this.isMenuORresource = isMenuORresource;
    }
    public void setText(String text){
       this.text = text;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ResourceTreeResDto\(id="+this.getId()+", text="+this.getText()+", url="+this.getUrl()+", icon="+this.getIcon()+", iclass="+this.getIclass()+", children="+this.getChildren()+", isMenuORresource="+this.getIsMenuORresource()+"\)";
    }
}
