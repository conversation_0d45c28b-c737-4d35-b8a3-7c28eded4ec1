package com.taikang.fly.check.dto.ybconsumablesList.YbConsumablesListSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class YbConsumablesListSearchDto implements Serializable	// class@0001d3 from classes.dex
{
    private String consumCode;
    private String consumName;
    private static final long serialVersionUID = 0x1;

    public void YbConsumablesListSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbConsumablesListSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof YbConsumablesListSearchDto) {
             b = false;
          }else {
             YbConsumablesListSearchDto ybConsumable = o;
             if (!ybConsumable.canEqual(this)) {
                b = false;
             }else {
                String consumCode = this.getConsumCode();
                String consumCode1 = ybConsumable.getConsumCode();
                if (consumCode == null) {
                   if (consumCode1 != null) {
                      b = false;
                   }
                }else if(consumCode.equals(consumCode1)){
                }
                String consumName = this.getConsumName();
                String consumName1 = ybConsumable.getConsumName();
                if (consumName == null) {
                   if (consumName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!consumName.equals(consumName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getConsumCode(){
       return this.consumCode;
    }
    public String getConsumName(){
       return this.consumName;
    }
    public int hashCode(){
       String $consumCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($consumCode = this.getConsumCode()) == null)? i: $consumCode.hashCode();
       result = i1 + 59;
       String $consumName = this.getConsumName();
       i1 = result * 59;
       if ($consumName != null) {
          i = $consumName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setConsumCode(String consumCode){
       this.consumCode = consumCode;
    }
    public void setConsumName(String consumName){
       this.consumName = consumName;
    }
    public String toString(){
       return "YbConsumablesListSearchDto\(consumCode="+this.getConsumCode()+", consumName="+this.getConsumName()+"\)";
    }
}
