"""
检查计划相关模型
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Text, DateTime, Integer, ForeignKey, JSON
from sqlalchemy.orm import relationship

from .base import BaseModel


class Plan(BaseModel):
    """检查计划"""
    
    __tablename__ = "plan"
    
    plan_name = Column(String(200), nullable=False, comment="计划名称")
    plan_description = Column(Text, comment="计划描述")
    plan_type = Column(String(50), comment="计划类型")
    
    # 执行时间
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    
    # 数据范围
    data_source = Column(String(100), comment="数据源")
    data_range = Column(Text, comment="数据范围")
    
    # 状态
    status = Column(String(20), default="draft", comment="状态 draft-草稿 active-激活 completed-完成")
    
    # 执行配置
    execution_config = Column(JSON, comment="执行配置")
    
    # 统计信息
    total_rules = Column(Integer, default=0, comment="总规则数")
    completed_rules = Column(Integer, default=0, comment="已完成规则数")
    error_rules = Column(Integer, default=0, comment="错误规则数")


class PlanRule(BaseModel):
    """计划规则关联"""
    
    __tablename__ = "plan_rule"
    
    plan_id = Column(String, ForeignKey("plan.id"), nullable=False, comment="计划ID")
    rule_id = Column(String, ForeignKey("fly_rule.id"), nullable=False, comment="规则ID")
    
    # 执行顺序和配置
    execution_order = Column(Integer, comment="执行顺序")
    rule_params = Column(JSON, comment="规则参数")
    
    # 状态
    status = Column(String(20), default="pending", comment="状态 pending-待执行 running-执行中 completed-完成 error-错误")
    
    # 关联关系
    plan = relationship("Plan", backref="plan_rules")
    rule = relationship("FlyRule", backref="plan_rules")


class PlanLog(BaseModel):
    """计划执行日志"""
    
    __tablename__ = "plan_log"
    
    plan_id = Column(String, ForeignKey("plan.id"), nullable=False, comment="计划ID")
    rule_id = Column(String, ForeignKey("fly_rule.id"), nullable=True, comment="规则ID")
    
    # 执行信息
    execution_start = Column(DateTime, comment="执行开始时间")
    execution_end = Column(DateTime, comment="执行结束时间")
    execution_status = Column(String(20), comment="执行状态")
    
    # 结果统计
    total_records = Column(Integer, default=0, comment="总记录数")
    error_records = Column(Integer, default=0, comment="错误记录数")
    warning_records = Column(Integer, default=0, comment="警告记录数")
    
    # 详细信息
    execution_detail = Column(Text, comment="执行详情")
    error_message = Column(Text, comment="错误信息")
    result_data = Column(JSON, comment="结果数据")
    
    # 文件路径
    result_file_path = Column(String(500), comment="结果文件路径")
    
    # 关联关系
    plan = relationship("Plan", backref="logs")
    rule = relationship("FlyRule", backref="execution_logs")
