package org.springframework.boot.loader.jar.JarFileEntries$1;
import java.util.LinkedHashMap;
import org.springframework.boot.loader.jar.JarFileEntries;
import java.util.Map$Entry;
import org.springframework.boot.loader.jar.JarFile;

class JarFileEntries$1 extends LinkedHashMap	// class@000557 from classes.dex
{
    final JarFileEntries this$0;

    void JarFileEntries$1(JarFileEntries this$0,int x0,float x1,boolean x2){
       this.this$0 = this$0;
       super(x0, x1, x2);
    }
    protected boolean removeEldestEntry(Map$Entry eldest){
       boolean b = false;
       if (!JarFileEntries.access$000(this.this$0).isSigned() && this.size() >= 25) {
          b = true;
       }
       return b;
    }
}
