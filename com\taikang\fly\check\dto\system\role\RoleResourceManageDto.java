package com.taikang.fly.check.dto.system.role.RoleResourceManageDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class RoleResourceManageDto implements Serializable	// class@0001b7 from classes.dex
{
    private String resourceIds;
    private String roleCode;
    private static final long serialVersionUID = 0x1;

    public void RoleResourceManageDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleResourceManageDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof RoleResourceManageDto) {
             b = false;
          }else {
             RoleResourceManageDto roleResource = o;
             if (!roleResource.canEqual(this)) {
                b = false;
             }else {
                String roleCode = this.getRoleCode();
                String roleCode1 = roleResource.getRoleCode();
                if (roleCode == null) {
                   if (roleCode1 != null) {
                      b = false;
                   }
                }else if(roleCode.equals(roleCode1)){
                }
                String resourceIds = this.getResourceIds();
                String resourceIds1 = roleResource.getResourceIds();
                if (resourceIds == null) {
                   if (resourceIds1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!resourceIds.equals(resourceIds1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getResourceIds(){
       return this.resourceIds;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public int hashCode(){
       String $roleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($roleCode = this.getRoleCode()) == null)? i: $roleCode.hashCode();
       result = i1 + 59;
       String $resourceIds = this.getResourceIds();
       i1 = result * 59;
       if ($resourceIds != null) {
          i = $resourceIds.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setResourceIds(String resourceIds){
       this.resourceIds = resourceIds;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public String toString(){
       return "RoleResourceManageDto\(roleCode="+this.getRoleCode()+", resourceIds="+this.getResourceIds()+"\)";
    }
}
