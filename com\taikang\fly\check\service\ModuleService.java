package com.taikang.fly.check.service.ModuleService;
import java.lang.Object;
import com.taikang.fly.check.dto.module.ModuleQueryDto;
import java.util.Map;
import java.util.HashMap;
import java.lang.Integer;
import java.lang.String;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taikang.fly.check.mybatis.dao.ModuleMapper;
import com.taikang.fly.check.mybatis.domain.Module;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.util.List;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.dto.mapstruct.ModuleMapping;
import com.taikang.fly.check.comm.Page;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.taikang.fly.check.dto.module.ModuleRespAddDto;
import java.util.Collection;
import org.springframework.util.CollectionUtils;
import com.taikang.fly.check.dto.module.ModuleRespEditDto;

public class ModuleService	// class@0002f0 from classes.dex
{
    private ModuleMapper moduleMapper;
    private ModuleMapping moduleMapping;

    public void ModuleService(){
       super();
    }
    private Map getModuleQueryDtoParamMap(int pageNum,int pageSize,ModuleQueryDto moduleQueryDto){
       int start;
       Map paramMap = new HashMap(4);
       if ((start = pageSize * (pageNum - 1)) < 0) {
          start = 0;
       }
       paramMap.put("start", Integer.valueOf(start));
       paramMap.put("pageSize", Integer.valueOf(pageSize));
       if (moduleQueryDto != null) {
          if (StringUtils.isNotBlank(moduleQueryDto.getModuleCode())) {
             paramMap.put("moduleCode", moduleQueryDto.getModuleCode());
          }
          if (StringUtils.isNotBlank(moduleQueryDto.getModuleName())) {
             paramMap.put("moduleName", moduleQueryDto.getModuleName());
          }
       }
       return paramMap;
    }
    public void deleteModule(String moduleCode){
       Module modulex;
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("module_code", moduleCode);
       queryWrapper.eq("is_valid", "1");
       if ((modulex = this.moduleMapper.selectOne(queryWrapper)) == null) {
          throw new BizException(ResponseCodeEnum.MODULE_NOT_EXISTS);
       }
       modulex.setIsValid("0");
       this.moduleMapper.updateByCode(modulex);
       return;
    }
    public List queryModuleList(){
       UserDto userInfo = ThreadLocalContextHolder.getContext().getUserInfo();
       Map params = new HashMap(1);
       params.put("userCode", userInfo.getUserCode());
       List modules = this.moduleMapper.queryListByUserCode(params);
       return this.moduleMapping.moduleToModuleIgnoreFieldRespDtos(modules);
    }
    public Page queryModuleListPage(ModuleQueryDto moduleQueryDto,Integer pageNum,Integer pageSize){
       Map paramMap = this.getModuleQueryDtoParamMap(pageNum.intValue(), pageSize.intValue(), moduleQueryDto);
       PageMethod.startPage(pageNum.intValue(), pageSize.intValue());
       List modules = this.moduleMapper.queryListByParams(paramMap);
       List moduleDto = this.moduleMapping.usersToUserDtos(modules);
       Integer count = this.moduleMapper.queryListPageCountByParams(paramMap);
       return new Page(moduleDto, count, pageSize, pageNum);
    }
    public void saveModule(ModuleRespAddDto moduleRespAddDto){
       String moduleCode = moduleRespAddDto.getModuleCode();
       List list = this.moduleMapper.selectByCode(moduleCode);
       if (!CollectionUtils.isEmpty(list)) {
          throw new BizException(ResponseCodeEnum.DATA_CODE_EXISTS);
       }
       this.moduleMapper.insert(this.moduleMapping.moduleRespAddDtoToModule(moduleRespAddDto));
       return;
    }
    public void updateModule(ModuleRespEditDto moduleRespEditDto){
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("module_code", moduleRespEditDto.getModuleCode());
       queryWrapper.eq("is_valid", "1");
       if (this.moduleMapper.selectOne(queryWrapper) == null) {
          throw new BizException(ResponseCodeEnum.MODULE_NOT_EXISTS);
       }
       this.moduleMapper.updateByCode(this.moduleMapping.moduleRespEditDtoToModule(moduleRespEditDto));
       return;
    }
}
