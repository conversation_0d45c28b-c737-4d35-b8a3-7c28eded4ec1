package com.taikang.fly.check.dto.FlySoftInfoDTO;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlySoftInfoDTO implements Serializable	// class@0000a2 from classes.dex
{
    private String fileName;
    private String id;
    private String tabDescription;
    private String tabName;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void FlySoftInfoDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlySoftInfoDTO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlySoftInfoDTO) {
             b = false;
          }else {
             FlySoftInfoDTO uFlySoftInfo = o;
             if (!uFlySoftInfo.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uFlySoftInfo.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String tabName = this.getTabName();
                String tabName1 = uFlySoftInfo.getTabName();
                if (tabName == null) {
                   if (tabName1 != null) {
                      b = false;
                   }
                }else if(tabName.equals(tabName1)){
                }
                String tabDescripti = this.getTabDescription();
                String tabDescripti1 = uFlySoftInfo.getTabDescription();
                if (tabDescripti == null) {
                   if (tabDescripti1 != null) {
                      b = false;
                   }
                }else if(tabDescripti.equals(tabDescripti1)){
                }
                String fileName = this.getFileName();
                String fileName1 = uFlySoftInfo.getFileName();
                if (fileName == null) {
                   if (fileName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!fileName.equals(fileName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFileName(){
       return this.fileName;
    }
    public String getId(){
       return this.id;
    }
    public String getTabDescription(){
       return this.tabDescription;
    }
    public String getTabName(){
       return this.tabName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tabName = this.getTabName();
       int i2 = result * 59;
       i1 = ($tabName == null)? i: $tabName.hashCode();
       result = i2 + i1;
       String $tabDescription = this.getTabDescription();
       i2 = result * 59;
       i1 = ($tabDescription == null)? i: $tabDescription.hashCode();
       result = i2 + i1;
       String $fileName = this.getFileName();
       i1 = result * 59;
       if ($fileName != null) {
          i = $fileName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFileName(String fileName){
       this.fileName = fileName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setTabDescription(String tabDescription){
       this.tabDescription = tabDescription;
    }
    public void setTabName(String tabName){
       this.tabName = tabName;
    }
    public String toString(){
       return "FlySoftInfoDTO\(id="+this.getId()+", tabName="+this.getTabName()+", tabDescription="+this.getTabDescription()+", fileName="+this.getFileName()+"\)";
    }
}
