package com.taikang.fly.check.mybatis.domain.ClickhouseFlyRule;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.time.LocalDate;
import java.lang.StringBuilder;

public class ClickhouseFlyRule implements Serializable	// class@000233 from classes.dex
{
    private Date createdTime;
    private String creater;
    private String dataSources;
    private String diagnosisType;
    private LocalDate executionDate;
    private String id;
    private String newSqlName;
    private Date operateTime;
    private String operator;
    private String ps;
    private String redField1;
    private String redField2;
    private String redField3;
    private String region;
    private String removed;
    private String resultFlag;
    private String resultsEnforcement;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleClassify;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleLogic;
    private String ruleName;
    private String ruleParameter;
    private String ruleType;
    private String sourceOfRule;
    private String sqlName;
    private String state;
    private String submitState;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseFlyRule(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseFlyRule;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseFlyRule){
          b = false;
       }else {
          ClickhouseFlyRule uClickhouseF = o;
          if (!uClickhouseF.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uClickhouseF.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uClickhouseF.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String operator = this.getOperator();
             String operator1 = uClickhouseF.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             String region = this.getRegion();
             String region1 = uClickhouseF.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uClickhouseF.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String resultFlag = this.getResultFlag();
             String resultFlag1 = uClickhouseF.getResultFlag();
             if (resultFlag == null) {
                if (resultFlag1 != null) {
                label_00a9 :
                   b = false;
                }
             }else if(resultFlag.equals(resultFlag1)){
             }
             String removed = this.getRemoved();
             String removed1 = uClickhouseF.getRemoved();
             if (removed == null) {
                if (removed1 != null) {
                   b = false;
                }
             }else if(removed.equals(removed1)){
             }
             Date operateTime = this.getOperateTime();
             Date operateTime1 = uClickhouseF.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                label_00dd :
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             Date createdTime = this.getCreatedTime();
             Date createdTime1 = uClickhouseF.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String ps = this.getPs();
             String ps1 = uClickhouseF.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_010d :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uClickhouseF.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = uClickhouseF.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                label_013f :
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             String state = this.getState();
             String state1 = uClickhouseF.getState();
             if (state == null) {
                if (state1 != null) {
                   b = false;
                }
             }else if(state.equals(state1)){
             }
             String creater = this.getCreater();
             String creater1 = uClickhouseF.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                label_0171 :
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String dataSources = this.getDataSources();
             String dataSources1 = uClickhouseF.getDataSources();
             if (dataSources == null) {
                if (dataSources1 != null) {
                   b = false;
                }
             }else if(dataSources.equals(dataSources1)){
             }
             String submitState = this.getSubmitState();
             String submitState1 = uClickhouseF.getSubmitState();
             if (submitState == null) {
                if (submitState1 != null) {
                   b = false;
                }
             }else if(submitState.equals(submitState1)){
             }
             String resultsEnfor = this.getResultsEnforcement();
             String resultsEnfor1 = uClickhouseF.getResultsEnforcement();
             if (resultsEnfor == null) {
                if (resultsEnfor1 != null) {
                label_01bb :
                   b = false;
                }
             }else if(resultsEnfor.equals(resultsEnfor1)){
             }
             LocalDate executionDat = this.getExecutionDate();
             LocalDate executionDat1 = uClickhouseF.getExecutionDate();
             if (executionDat == null) {
                if (executionDat1 != null) {
                   b = false;
                }
             }else if(executionDat.equals(executionDat1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uClickhouseF.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                label_01ed :
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uClickhouseF.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uClickhouseF.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                label_0221 :
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uClickhouseF.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uClickhouseF.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String sourceOfRule = this.getSourceOfRule();
             String sourceOfRule1 = uClickhouseF.getSourceOfRule();
             if (sourceOfRule == null) {
                if (sourceOfRule1 != null) {
                label_026d :
                   b = false;
                }
             }else if(sourceOfRule.equals(sourceOfRule1)){
             }
             String ruleLogic = this.getRuleLogic();
             String ruleLogic1 = uClickhouseF.getRuleLogic();
             if (ruleLogic == null) {
                if (ruleLogic1 != null) {
                   b = false;
                }
             }else if(ruleLogic.equals(ruleLogic1)){
             }
             String ruleParamete = this.getRuleParameter();
             String ruleParamete1 = uClickhouseF.getRuleParameter();
             if (ruleParamete == null) {
                if (ruleParamete1 != null) {
                label_02a1 :
                   b = false;
                }
             }else if(ruleParamete.equals(ruleParamete1)){
             }
             String redField1 = this.getRedField1();
             String redField11 = uClickhouseF.getRedField1();
             if (redField1 == null) {
                if (redField11 != null) {
                   b = false;
                }
             }else if(redField1.equals(redField11)){
             }
             String redField2 = this.getRedField2();
             String redField21 = uClickhouseF.getRedField2();
             if (redField2 == null) {
                if (redField21 != null) {
                label_02d3 :
                   b = false;
                }
             }else if(redField2.equals(redField21)){
             }
             String redField3 = this.getRedField3();
             String redField31 = uClickhouseF.getRedField3();
             if (redField3 == null) {
                if (redField31 != null) {
                   b = false;
                }
             }else if(redField3.equals(redField31)){
             }
             String ruleClassify = this.getRuleClassify();
             String ruleClassify1 = uClickhouseF.getRuleClassify();
             if (ruleClassify == null) {
                if (ruleClassify1 != null) {
                label_0303 :
                   b = false;
                }
             }else if(ruleClassify.equals(ruleClassify1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreatedTime(){
       return this.createdTime;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDataSources(){
       return this.dataSources;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public LocalDate getExecutionDate(){
       return this.executionDate;
    }
    public String getId(){
       return this.id;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public Date getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRedField1(){
       return this.redField1;
    }
    public String getRedField2(){
       return this.redField2;
    }
    public String getRedField3(){
       return this.redField3;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getResultFlag(){
       return this.resultFlag;
    }
    public String getResultsEnforcement(){
       return this.resultsEnforcement;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleClassify(){
       return this.ruleClassify;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleLogic(){
       return this.ruleLogic;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleParameter(){
       return this.ruleParameter;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSourceOfRule(){
       return this.sourceOfRule;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public String getState(){
       return this.state;
    }
    public String getSubmitState(){
       return this.submitState;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $operator = this.getOperator();
       i1 = result * 59;
       i = ($operator == null)? 43: $operator.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $ruleType = this.getRuleType();
       i1 = result * 59;
       i = ($ruleType == null)? 43: $ruleType.hashCode();
       result = i1 + i;
       String resultFlag = this.getResultFlag();
       i1 = result * 59;
       i = (resultFlag == null)? 43: resultFlag.hashCode();
       String removed = this.getRemoved();
       i1 = (i1 + i) * 59;
       i = (removed == null)? 43: removed.hashCode();
       Date operateTime = this.getOperateTime();
       i1 = (i1 + i) * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       Date createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String sqlName = this.getSqlName();
       i1 = (i1 + i) * 59;
       i = (sqlName == null)? 43: sqlName.hashCode();
       String newSqlName = this.getNewSqlName();
       i1 = (i1 + i) * 59;
       i = (newSqlName == null)? 43: newSqlName.hashCode();
       String state = this.getState();
       i1 = (i1 + i) * 59;
       i = (state == null)? 43: state.hashCode();
       String creater = this.getCreater();
       i1 = (i1 + i) * 59;
       i = (creater == null)? 43: creater.hashCode();
       String dataSources = this.getDataSources();
       i1 = (i1 + i) * 59;
       i = (dataSources == null)? 43: dataSources.hashCode();
       String submitState = this.getSubmitState();
       i1 = (i1 + i) * 59;
       i = (submitState == null)? 43: submitState.hashCode();
       String resultsEnfor = this.getResultsEnforcement();
       i1 = (i1 + i) * 59;
       i = (resultsEnfor == null)? 43: resultsEnfor.hashCode();
       LocalDate executionDat = this.getExecutionDate();
       i1 = (i1 + i) * 59;
       i = (executionDat == null)? 43: executionDat.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       String sourceOfRule = this.getSourceOfRule();
       i1 = (i1 + i) * 59;
       i = (sourceOfRule == null)? 43: sourceOfRule.hashCode();
       String ruleLogic = this.getRuleLogic();
       i1 = (i1 + i) * 59;
       i = (ruleLogic == null)? 43: ruleLogic.hashCode();
       String ruleParamete = this.getRuleParameter();
       i1 = (i1 + i) * 59;
       i = (ruleParamete == null)? 43: ruleParamete.hashCode();
       String redField1 = this.getRedField1();
       i1 = (i1 + i) * 59;
       i = (redField1 == null)? 43: redField1.hashCode();
       String redField2 = this.getRedField2();
       i1 = (i1 + i) * 59;
       i = (redField2 == null)? 43: redField2.hashCode();
       String redField3 = this.getRedField3();
       i1 = (i1 + i) * 59;
       i = (redField3 == null)? 43: redField3.hashCode();
       String ruleClassify = this.getRuleClassify();
       i1 = (i1 + i) * 59;
       i = (ruleClassify == null)? 43: ruleClassify.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(Date createdTime){
       this.createdTime = createdTime;
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDataSources(String dataSources){
       this.dataSources = dataSources;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setExecutionDate(LocalDate executionDate){
       this.executionDate = executionDate;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setOperateTime(Date operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRedField1(String redField1){
       this.redField1 = redField1;
    }
    public void setRedField2(String redField2){
       this.redField2 = redField2;
    }
    public void setRedField3(String redField3){
       this.redField3 = redField3;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRemoved(String removed){
       this.removed = removed;
    }
    public void setResultFlag(String resultFlag){
       this.resultFlag = resultFlag;
    }
    public void setResultsEnforcement(String resultsEnforcement){
       this.resultsEnforcement = resultsEnforcement;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleClassify(String ruleClassify){
       this.ruleClassify = ruleClassify;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleLogic(String ruleLogic){
       this.ruleLogic = ruleLogic;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleParameter(String ruleParameter){
       this.ruleParameter = ruleParameter;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSourceOfRule(String sourceOfRule){
       this.sourceOfRule = sourceOfRule;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public void setState(String state){
       this.state = state;
    }
    public void setSubmitState(String submitState){
       this.submitState = submitState;
    }
    public String toString(){
       return "ClickhouseFlyRule\(id="+this.getId()+", ruleName="+this.getRuleName()+", operator="+this.getOperator()+", region="+this.getRegion()+", ruleType="+this.getRuleType()+", resultFlag="+this.getResultFlag()+", removed="+this.getRemoved()+", operateTime="+this.getOperateTime()+", createdTime="+this.getCreatedTime()+", ps="+this.getPs()+", sqlName="+this.getSqlName()+", newSqlName="+this.getNewSqlName()+", state="+this.getState()+", creater="+this.getCreater()+", dataSources="+this.getDataSources()+", submitState="+this.getSubmitState()+", resultsEnforcement="+this.getResultsEnforcement()+", executionDate="+this.getExecutionDate()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ruleLevel="+this.getRuleLevel()+", sourceOfRule="+this.getSourceOfRule()+", ruleLogic="+this.getRuleLogic()+", ruleParameter="+this.getRuleParameter()+", redField1="+this.getRedField1()+", redField2="+this.getRedField2()+", redField3="+this.getRedField3()+", ruleClassify="+this.getRuleClassify()+"\)";
    }
}
