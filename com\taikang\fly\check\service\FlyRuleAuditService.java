package com.taikang.fly.check.service.FlyRuleAuditService;
import com.taikang.fly.check.dto.flyRule.FlyRuleEditDto;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import java.lang.Integer;
import com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditQueryDto;
import com.taikang.fly.check.comm.NativePage;

public interface abstract FlyRuleAuditService	// class@0002dd from classes.dex
{

    void delFlyRuleAudit(FlyRuleEditDto p0);
    void delMoreFlyRule(List p0);
    void editFlyRuleAudit(FlyRuleEditDto p0);
    String importFlyRuleAuditInfo(MultipartFile p0);
    NativePage queryPage(Integer p0,Integer p1,FlyRuleAuditQueryDto p2);
}
