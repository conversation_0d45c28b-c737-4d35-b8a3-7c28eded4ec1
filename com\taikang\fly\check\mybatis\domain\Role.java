package com.taikang.fly.check.mybatis.domain.Role;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class Role	// class@000268 from classes.dex
{
    private Date createTime;
    private String creator;
    private String id;
    private String isValid;
    private String modby;
    private Date modifyTime;
    private String name;
    private String roleCode;
    private String signature;

    public void Role(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof Role;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof Role){
          b = false;
       }else {
          Role role = o;
          if (!role.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = role.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String roleCode = this.getRoleCode();
             String roleCode1 = role.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             String name = this.getName();
             String name1 = role.getName();
             if (name == null) {
                if (name1 != null) {
                   b = false;
                }
             }else if(name.equals(name1)){
             }
             String creator = this.getCreator();
             String creator1 = role.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = role.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_0087 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = role.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = role.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00b5 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = role.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = role.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                label_00e5 :
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getName(){
       return this.name;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $roleCode = this.getRoleCode();
       int i2 = result * 59;
       i1 = ($roleCode == null)? i: $roleCode.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       Date $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i2 = (i2 + i1) * 59;
       i1 = (signature == null)? i: signature.hashCode();
       String isValid = this.getIsValid();
       i1 = (i2 + i1) * 59;
       if (isValid != null) {
          i = isValid.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public String toString(){
       return "Role\(id="+this.getId()+", roleCode="+this.getRoleCode()+", name="+this.getName()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", isValid="+this.getIsValid()+"\)";
    }
}
