package com.taikang.fly.check.dto.businesscolconfig.ColConfigAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class ColConfigAddDto implements Serializable	// class@0000b8 from classes.dex
{
    private String businessKey;
    private String businessName;
    private List colConfigSaveDtoList;
    private String operateType;
    private static final long serialVersionUID = 0x1;

    public void ColConfigAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ColConfigAddDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ColConfigAddDto) {
             b = false;
          }else {
             ColConfigAddDto uColConfigAd = o;
             if (!uColConfigAd.canEqual(this)) {
                b = false;
             }else {
                String businessKey = this.getBusinessKey();
                String businessKey1 = uColConfigAd.getBusinessKey();
                if (businessKey == null) {
                   if (businessKey1 != null) {
                      b = false;
                   }
                }else if(businessKey.equals(businessKey1)){
                }
                String businessName = this.getBusinessName();
                String businessName1 = uColConfigAd.getBusinessName();
                if (businessName == null) {
                   if (businessName1 != null) {
                      b = false;
                   }
                }else if(businessName.equals(businessName1)){
                }
                List colConfigSav = this.getColConfigSaveDtoList();
                List colConfigSav1 = uColConfigAd.getColConfigSaveDtoList();
                if (colConfigSav == null) {
                   if (colConfigSav1 != null) {
                      b = false;
                   }
                }else if(colConfigSav.equals(colConfigSav1)){
                }
                String operateType = this.getOperateType();
                String operateType1 = uColConfigAd.getOperateType();
                if (operateType == null) {
                   if (operateType1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!operateType.equals(operateType1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getBusinessKey(){
       return this.businessKey;
    }
    public String getBusinessName(){
       return this.businessName;
    }
    public List getColConfigSaveDtoList(){
       return this.colConfigSaveDtoList;
    }
    public String getOperateType(){
       return this.operateType;
    }
    public int hashCode(){
       String $businessKey;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($businessKey = this.getBusinessKey()) == null)? i: $businessKey.hashCode();
       result = i1 + 59;
       String $businessName = this.getBusinessName();
       int i2 = result * 59;
       i1 = ($businessName == null)? i: $businessName.hashCode();
       result = i2 + i1;
       List $colConfigSaveDtoList = this.getColConfigSaveDtoList();
       i2 = result * 59;
       i1 = ($colConfigSaveDtoList == null)? i: $colConfigSaveDtoList.hashCode();
       result = i2 + i1;
       String $operateType = this.getOperateType();
       i1 = result * 59;
       if ($operateType != null) {
          i = $operateType.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setBusinessKey(String businessKey){
       this.businessKey = businessKey;
    }
    public void setBusinessName(String businessName){
       this.businessName = businessName;
    }
    public void setColConfigSaveDtoList(List colConfigSaveDtoList){
       this.colConfigSaveDtoList = colConfigSaveDtoList;
    }
    public void setOperateType(String operateType){
       this.operateType = operateType;
    }
    public String toString(){
       return "ColConfigAddDto\(businessKey="+this.getBusinessKey()+", businessName="+this.getBusinessName()+", colConfigSaveDtoList="+this.getColConfigSaveDtoList()+", operateType="+this.getOperateType()+"\)";
    }
}
