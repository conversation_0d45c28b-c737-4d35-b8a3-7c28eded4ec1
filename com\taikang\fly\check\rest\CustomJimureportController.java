package com.taikang.fly.check.rest.CustomJimureportController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import com.taikang.fly.check.service.CustomJimuReportServiceImpl;

public class CustomJimureportController	// class@000287 from classes.dex
{
    CustomJimuReportServiceImpl jimuReportService;
    private static final Logger log;

    static {
       CustomJimureportController.log = LoggerFactory.getLogger(CustomJimureportController.class);
    }
    public void CustomJimureportController(){
       super();
    }
    public String jimureportChartHtml(String ruleName,String year,ServletRequest request,ServletResponse response){
       String url = this.jimuReportService.getUrlData(ruleName, year);
       return url;
    }
}
