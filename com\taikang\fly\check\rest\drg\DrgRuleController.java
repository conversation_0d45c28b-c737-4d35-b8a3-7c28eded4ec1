package com.taikang.fly.check.rest.drg.DrgRuleController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import com.taikang.fly.check.vo.drg.DrgCheckTaskDateVo;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.mybatis.domain.drg.DrgCheckTasks;
import com.taikang.fly.check.service.drg.DrgRuleService;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.dto.drg.DrgResultQueryDTO;
import java.lang.String;
import java.lang.Exception;
import java.lang.Integer;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.vo.drg.DrgSerachVo;
import com.taikang.fly.check.vo.drg.DrgQueryFeeVarianceOfDepartmentVo;
import com.taikang.fly.check.dto.drg.DrgProfitAndLossDownloadDto;
import java.util.List;
import com.taikang.fly.check.vo.drg.DrgCheckAuditTaskResultVo;
import com.taikang.fly.check.dto.drg.DrgGroupOrDepartmentInfoDto;
import java.lang.CharSequence;
import cn.hutool.core.util.StrUtil;
import com.taikang.fly.check.dto.drg.DepartmentAndDrgGroupNamesDto;
import com.taikang.fly.check.vo.drg.DrgFeeOfDepartmentVo;
import com.taikang.fly.check.dto.drg.DrgProfitAndLossDto;

public class DrgRuleController	// class@0002c1 from classes.dex
{
    DrgRuleService drgRuleService;
    private static final Logger log;

    static {
       DrgRuleController.log = LoggerFactory.getLogger(DrgRuleController.class);
    }
    public void DrgRuleController(){
       super();
    }
    public CommResponse checkAuditTasks(DrgCheckTaskDateVo checkTaskDateVo){
       return CommResponse.success(this.drgRuleService.checkAuditTasks(checkTaskDateVo));
    }
    public CommResponse delDrgResult(){
       CommResponse uCommRespons = (this.drgRuleService.delDrgResult())? CommResponse.success(): CommResponse.error(ResponseCodeEnum.SAVE_ERROR);
       return uCommRespons;
    }
    public void downInspectionResults(HttpServletResponse response,DrgResultQueryDTO drgResultQueryDTO){
       this.drgRuleService.downInspectionResults(response, drgResultQueryDTO);
    }
    public CommResponse drgRegroup(){
       CommResponse uCommRespons;
       try{
          DrgRuleController.log.info("DrgRuleController.drgRegroup start");
          this.drgRuleService.drgRegroup();
          DrgRuleController.log.info("DrgRuleController.drgRegroup end");
          uCommRespons = CommResponse.success();
       }catch(java.lang.Exception e0){
          uCommRespons = CommResponse.error("0", e0.getMessage(), null);
       }
       return uCommRespons;
    }
    public CommResponse drgRegroupCompare(Integer page,Integer size){
       return CommResponse.success(this.drgRuleService.drgRegroupCompare(page, size));
    }
    public void export(DrgSerachVo searchVo,HttpServletResponse response){
       this.drgRuleService.export(searchVo, response);
    }
    public CommResponse exportDrgDateOfDepartment(HttpServletResponse response){
       this.drgRuleService.exportDrgDateOfDepartment(response);
       return CommResponse.success();
    }
    public CommResponse exportDrgDateOfDepartmentNum(HttpServletResponse response){
       this.drgRuleService.exportDrgDateOfDepartmentNum(response);
       return CommResponse.success();
    }
    public CommResponse exportDrgDateOfDepartmentRW(HttpServletResponse response){
       this.drgRuleService.exportDrgDateOfDepartmentRW(response);
       return CommResponse.success();
    }
    public CommResponse exportDrgFeeVarianceOfDepartment(HttpServletResponse response,DrgQueryFeeVarianceOfDepartmentVo drgQueryFeeVarianceOfDepartmentVo){
       this.drgRuleService.exportDrgFeeVarianceOfDepartment(response, drgQueryFeeVarianceOfDepartmentVo);
       return CommResponse.success();
    }
    public void exportProfitAndLoss(DrgProfitAndLossDownloadDto drgProfitAndLossDownloadDto,HttpServletResponse response){
       this.drgRuleService.exportProfitAndLoss(drgProfitAndLossDownloadDto, response);
    }
    public CommResponse getAllDepartmentNames(){
       return CommResponse.success(this.drgRuleService.getAllDepartmentNames());
    }
    public CommResponse getAllDrgGroupsNames(){
       return CommResponse.success(this.drgRuleService.getAllDrgGroupsNames());
    }
    public CommResponse getCheckAuditTaskResults(Integer page,Integer size,DrgCheckAuditTaskResultVo drgCheckAuditTaskResultVo){
       return CommResponse.success(this.drgRuleService.getCheckAuditTaskResults(page, size, drgCheckAuditTaskResultVo));
    }
    public CommResponse getCheckAuditTasks(Integer page,Integer size){
       return CommResponse.success(this.drgRuleService.getCheckAuditTasks(page, size));
    }
    public CommResponse getDRGProportion(Integer pageNum,Integer pageSize){
       return CommResponse.success(this.drgRuleService.getDRGProportion(pageNum, pageSize));
    }
    public CommResponse getDepartmentProfitOrLossByDrgGroup(Integer pageNum,Integer pageSize,DrgGroupOrDepartmentInfoDto drgGroupOrDepartmentInfoDto){
       CommResponse uCommRespons = (!StrUtil.isBlank(drgGroupOrDepartmentInfoDto.getProfitOrLoss()) && !StrUtil.isBlank(drgGroupOrDepartmentInfoDto.getName()))? CommResponse.success(this.drgRuleService.getDepartmentProfitOrLossByDrgGroup(pageNum, pageSize, drgGroupOrDepartmentInfoDto)): CommResponse.error(ResponseCodeEnum.DATA_VERIFICATION_ERROR);
       return uCommRespons;
    }
    public CommResponse getDepartmentsProfitAndLoss(Integer pageNum,Integer pageSize,DepartmentAndDrgGroupNamesDto departmentAndDrgGroupNamesDto){
       return CommResponse.success(this.drgRuleService.getDrgDepartmentsProfitAndLoss(pageNum, pageSize, departmentAndDrgGroupNamesDto));
    }
    public CommResponse getDischargeDepartment(){
       return CommResponse.success(this.drgRuleService.getDischargeDepartment());
    }
    public CommResponse getDrgCodes(){
       return CommResponse.success(this.drgRuleService.getDrgCodes());
    }
    public CommResponse getDrgDateOfDepartment(Integer page,Integer size){
       return CommResponse.success(this.drgRuleService.getDrgDateOfDepartment(page, size));
    }
    public CommResponse getDrgDateOfDepartmentNum(Integer page,Integer size){
       return CommResponse.success(this.drgRuleService.getDrgDateOfDepartmentNum(page, size));
    }
    public CommResponse getDrgDateOfDepartmentRW(Integer page,Integer size){
       return CommResponse.success(this.drgRuleService.getDrgDateOfDepartmentRW(page, size));
    }
    public CommResponse getDrgFeeOfDepartmentDetail(Integer page,Integer size,DrgFeeOfDepartmentVo detailVo){
       return CommResponse.success(this.drgRuleService.getDrgFeeOfDepartmentDetail(page, size, detailVo));
    }
    public CommResponse getDrgFeeVarianceOfDepartment(Integer page,Integer size,DrgQueryFeeVarianceOfDepartmentVo drgQueryFeeVarianceOfDepartmentVo){
       return CommResponse.success(this.drgRuleService.drgFeeVarianceOfDepartment(page, size, drgQueryFeeVarianceOfDepartmentVo));
    }
    public CommResponse getDrgGroupProfitAndLoss(Integer pageNum,Integer pageSize,DepartmentAndDrgGroupNamesDto departmentAndDrgGroupNamesDto){
       return CommResponse.success(this.drgRuleService.getDrgGroupsProfitAndLoss(pageNum, pageSize, departmentAndDrgGroupNamesDto));
    }
    public CommResponse getDrgGroupProfitOrLossByDepartment(Integer pageNum,Integer pageSize,DrgGroupOrDepartmentInfoDto drgGroupOrDepartmentInfoDto){
       CommResponse uCommRespons = (!StrUtil.isBlank(drgGroupOrDepartmentInfoDto.getProfitOrLoss()) && !StrUtil.isBlank(drgGroupOrDepartmentInfoDto.getName()))? CommResponse.success(this.drgRuleService.getDrgGroupProfitOrLossByDepartment(pageNum, pageSize, drgGroupOrDepartmentInfoDto)): CommResponse.error(ResponseCodeEnum.DATA_VERIFICATION_ERROR);
       return uCommRespons;
    }
    public CommResponse getDrgGroupsList(Integer page,Integer size){
       return CommResponse.success(this.drgRuleService.getDrgGroupsList(page, size));
    }
    public CommResponse getDrgYear(){
       return CommResponse.success(this.drgRuleService.getDrgYear());
    }
    public void getGroupsData(){
       this.drgRuleService.getGroupsData();
    }
    public CommResponse getLossDepartmentOrDrg(Integer pageNum,Integer pageSize,DrgProfitAndLossDto drgProfitAndLossDto){
       return CommResponse.success(this.drgRuleService.getLossDepartmentOrDrg(pageNum, pageSize, drgProfitAndLossDto));
    }
    public CommResponse getProfitDepartmentOrDrg(Integer pageNum,Integer pageSize,DrgProfitAndLossDto drgProfitAndLossDto){
       CommResponse uCommRespons = (!StrUtil.isBlank(drgProfitAndLossDto.getDrgType()) && !StrUtil.isBlank(drgProfitAndLossDto.getName()))? CommResponse.success(this.drgRuleService.getProfitDepartmentOrDrg(pageNum, pageSize, drgProfitAndLossDto)): CommResponse.error(ResponseCodeEnum.DATA_VERIFICATION_ERROR);
       return uCommRespons;
    }
    public CommResponse getQueryConditionRuleName(){
       return CommResponse.success(this.drgRuleService.getQueryConditionRuleName());
    }
    public CommResponse getRuleListResult(){
       return CommResponse.success(this.drgRuleService.getRuleListResult());
    }
    public CommResponse getYear(){
       return CommResponse.success(this.drgRuleService.getYear());
    }
    public CommResponse queryList(Integer page,Integer size,DrgResultQueryDTO drgResultQueryDTO){
       return CommResponse.success(this.drgRuleService.queryList(page, size, drgResultQueryDTO));
    }
}
