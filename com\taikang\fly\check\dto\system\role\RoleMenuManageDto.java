package com.taikang.fly.check.dto.system.role.RoleMenuManageDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class RoleMenuManageDto implements Serializable	// class@0001b6 from classes.dex
{
    private String menus;
    private String moduleCode;
    private String roleCode;
    private static final long serialVersionUID = 0x1;

    public void RoleMenuManageDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleMenuManageDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof RoleMenuManageDto) {
             b = false;
          }else {
             RoleMenuManageDto roleMenuMana = o;
             if (!roleMenuMana.canEqual(this)) {
                b = false;
             }else {
                String roleCode = this.getRoleCode();
                String roleCode1 = roleMenuMana.getRoleCode();
                if (roleCode == null) {
                   if (roleCode1 != null) {
                      b = false;
                   }
                }else if(roleCode.equals(roleCode1)){
                }
                String menus = this.getMenus();
                String menus1 = roleMenuMana.getMenus();
                if (menus == null) {
                   if (menus1 != null) {
                      b = false;
                   }
                }else if(menus.equals(menus1)){
                }
                String moduleCode = this.getModuleCode();
                String moduleCode1 = roleMenuMana.getModuleCode();
                if (moduleCode == null) {
                   if (moduleCode1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!moduleCode.equals(moduleCode1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getMenus(){
       return this.menus;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public int hashCode(){
       String $roleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($roleCode = this.getRoleCode()) == null)? i: $roleCode.hashCode();
       result = i1 + 59;
       String $menus = this.getMenus();
       int i2 = result * 59;
       i1 = ($menus == null)? i: $menus.hashCode();
       result = i2 + i1;
       String $moduleCode = this.getModuleCode();
       i1 = result * 59;
       if ($moduleCode != null) {
          i = $moduleCode.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setMenus(String menus){
       this.menus = menus;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public String toString(){
       return "RoleMenuManageDto\(roleCode="+this.getRoleCode()+", menus="+this.getMenus()+", moduleCode="+this.getModuleCode()+"\)";
    }
}
