package com.taikang.fly.check.dto.mapstruct.RoleMapping;
import java.util.List;
import com.taikang.fly.check.dto.system.role.RoleAddDto;
import com.taikang.fly.check.mybatis.domain.Role;
import com.taikang.fly.check.dto.system.role.RoleEditDto;
import java.util.Map;
import com.taikang.fly.check.dto.system.role.RoleIndexResDto;

public interface abstract RoleMapping	// class@000179 from classes.dex
{

    List listRoleMapToListDto(List p0);
    Role roleAddDtoToRole(RoleAddDto p0);
    Role roleEditDtoToRole(RoleEditDto p0);
    RoleIndexResDto roleMapToDto(Map p0);
    List rolesDtoToRoles(List p0);
    List rolesToRolesDto(List p0);
}
