package com.taikang.fly.check.dto.mapstruct.TemplateInfoMappingImpl;
import com.taikang.fly.check.dto.mapstruct.TemplateInfoMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.templateInfo.TemplateInfoAddDto;
import com.taikang.fly.check.mybatis.domain.TemplateInfo;
import java.lang.String;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.time.LocalDateTime;
import com.taikang.fly.check.utils.SequenceGenerator;
import com.taikang.fly.check.dto.templateInfo.TemplateInfoEditDto;

public class TemplateInfoMappingImpl implements TemplateInfoMapping	// class@00017c from classes.dex
{

    public void TemplateInfoMappingImpl(){
       super();
    }
    public TemplateInfo addDto2TemplateInfo(TemplateInfoAddDto addDto){
       TemplateInfo templateInfo;
       if (addDto == null) {
          templateInfo = null;
       }else {
          templateInfo = new TemplateInfo();
          templateInfo.setTableName(addDto.getTableName());
          templateInfo.setFieldItems(addDto.getFieldItems());
          templateInfo.setSourceTableName(addDto.getSourceTableName());
          templateInfo.setWhereCondition(addDto.getWhereCondition());
          templateInfo.setContent(addDto.getContent());
          templateInfo.setExplain(addDto.getExplain());
          templateInfo.setManufacturer(addDto.getManufacturer());
          templateInfo.setCreator(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          templateInfo.setModifyTime(LocalDateTime.now());
          templateInfo.setCreatedTime(LocalDateTime.now());
          templateInfo.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          templateInfo.setId(SequenceGenerator.getId());
          templateInfo.setStatus("00");
       }
       return templateInfo;
    }
    public TemplateInfo editDto2TemplateInfo(TemplateInfoEditDto editDto){
       TemplateInfo templateInfo;
       if (editDto == null) {
          templateInfo = null;
       }else {
          templateInfo = new TemplateInfo();
          templateInfo.setId(editDto.getId());
          templateInfo.setTableName(editDto.getTableName());
          templateInfo.setFieldItems(editDto.getFieldItems());
          templateInfo.setSourceTableName(editDto.getSourceTableName());
          templateInfo.setWhereCondition(editDto.getWhereCondition());
          templateInfo.setExplain(editDto.getExplain());
          templateInfo.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          templateInfo.setModifyTime(LocalDateTime.now());
       }
       return templateInfo;
    }
}
