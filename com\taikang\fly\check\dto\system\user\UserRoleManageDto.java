package com.taikang.fly.check.dto.system.user.UserRoleManageDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserRoleManageDto implements Serializable	// class@0001b9 from classes.dex
{
    private String roleCodes;
    private String userCode;
    private static final long serialVersionUID = 0x1;

    public void UserRoleManageDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserRoleManageDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserRoleManageDto) {
             b = false;
          }else {
             UserRoleManageDto userRoleMana = o;
             if (!userRoleMana.canEqual(this)) {
                b = false;
             }else {
                String userCode = this.getUserCode();
                String userCode1 = userRoleMana.getUserCode();
                if (userCode == null) {
                   if (userCode1 != null) {
                      b = false;
                   }
                }else if(userCode.equals(userCode1)){
                }
                String roleCodes = this.getRoleCodes();
                String roleCodes1 = userRoleMana.getRoleCodes();
                if (roleCodes == null) {
                   if (roleCodes1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!roleCodes.equals(roleCodes1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getRoleCodes(){
       return this.roleCodes;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $userCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($userCode = this.getUserCode()) == null)? i: $userCode.hashCode();
       result = i1 + 59;
       String $roleCodes = this.getRoleCodes();
       i1 = result * 59;
       if ($roleCodes != null) {
          i = $roleCodes.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setRoleCodes(String roleCodes){
       this.roleCodes = roleCodes;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserRoleManageDto\(userCode="+this.getUserCode()+", roleCodes="+this.getRoleCodes()+"\)";
    }
}
