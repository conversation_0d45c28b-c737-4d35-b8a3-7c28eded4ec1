package com.taikang.fly.check.dto.user.UserAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserAddDto implements Serializable	// class@0001c0 from classes.dex
{
    private String name;
    private String password;
    private String region;
    private String roleCode;
    private String userCode;
    private static final long serialVersionUID = 0x1;

    public void UserAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserAddDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserAddDto) {
             b = false;
          }else {
             UserAddDto userAddDto = o;
             if (!userAddDto.canEqual(this)) {
                b = false;
             }else {
                String password = this.getPassword();
                String password1 = userAddDto.getPassword();
                if (password == null) {
                   if (password1 != null) {
                      b = false;
                   }
                }else if(password.equals(password1)){
                }
                String userCode = this.getUserCode();
                String userCode1 = userAddDto.getUserCode();
                if (userCode == null) {
                   if (userCode1 != null) {
                      b = false;
                   }
                }else if(userCode.equals(userCode1)){
                }
                String name = this.getName();
                String name1 = userAddDto.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String region = this.getRegion();
                String region1 = userAddDto.getRegion();
                if (region == null) {
                   if (region1 != null) {
                      b = false;
                   }
                }else if(region.equals(region1)){
                }
                String roleCode = this.getRoleCode();
                String roleCode1 = userAddDto.getRoleCode();
                if (roleCode == null) {
                   if (roleCode1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!roleCode.equals(roleCode1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getName(){
       return this.name;
    }
    public String getPassword(){
       return this.password;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $password;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($password = this.getPassword()) == null)? i: $password.hashCode();
       result = i1 + 59;
       String $userCode = this.getUserCode();
       int i2 = result * 59;
       i1 = ($userCode == null)? i: $userCode.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $roleCode = this.getRoleCode();
       i1 = result * 59;
       if ($roleCode != null) {
          i = $roleCode.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setPassword(String password){
       this.password = password;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserAddDto\(password="+this.getPassword()+", userCode="+this.getUserCode()+", name="+this.getName()+", region="+this.getRegion()+", roleCode="+this.getRoleCode()+"\)";
    }
}
