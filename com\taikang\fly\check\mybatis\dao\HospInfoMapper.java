package com.taikang.fly.check.mybatis.dao.HospInfoMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import java.util.List;
import java.util.Map;
import com.taikang.fly.check.mybatis.domain.HospInfo;

public interface abstract HospInfoMapper implements BaseMapper	// class@0001fb from classes.dex
{

    void createMzMx(String p0);
    void createMzZd(String p0);
    void createZyMx(String p0);
    void createZyZd(String p0);
    void deleteOraUser(String p0);
    List findHospInfoPage(JSONObject p0);
    List getFilePath(String[] p0);
    List getHosName(String p0);
    List getHosNames(String p0);
    List getHospNameDetail(Map p0);
    List getHospitalName(String p0);
    String getOraUserNameByRegionAndHospName(String p0,String p1);
    List getOraUserNameByTagAndModifyTime(String p0);
    String getPathByRegionAndHospName(String p0,String p1);
    List getRegionAndHospitalName();
    void resetStatus();
    void updatePath(HospInfo p0);
}
