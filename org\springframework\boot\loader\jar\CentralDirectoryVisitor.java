package org.springframework.boot.loader.jar.CentralDirectoryVisitor;
import org.springframework.boot.loader.jar.CentralDirectoryFileHeader;
import org.springframework.boot.loader.jar.CentralDirectoryEndRecord;
import org.springframework.boot.loader.data.RandomAccessData;

interface abstract CentralDirectoryVisitor	// class@00054e from classes.dex
{

    void visitEnd();
    void visitFileHeader(CentralDirectoryFileHeader p0,int p1);
    void visitStart(CentralDirectoryEndRecord p0,RandomAccessData p1);
}
