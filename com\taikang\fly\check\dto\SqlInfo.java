package com.taikang.fly.check.dto.SqlInfo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class SqlInfo	// class@0000b0 from classes.dex
{
    private String fileName;
    private String hostName;
    private String sql;

    public void SqlInfo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof SqlInfo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof SqlInfo) {
             b = false;
          }else {
             SqlInfo sqlInfo = o;
             if (!sqlInfo.canEqual(this)) {
                b = false;
             }else {
                String hostName = this.getHostName();
                String hostName1 = sqlInfo.getHostName();
                if (hostName == null) {
                   if (hostName1 != null) {
                      b = false;
                   }
                }else if(hostName.equals(hostName1)){
                }
                String fileName = this.getFileName();
                String fileName1 = sqlInfo.getFileName();
                if (fileName == null) {
                   if (fileName1 != null) {
                      b = false;
                   }
                }else if(fileName.equals(fileName1)){
                }
                String sql = this.getSql();
                String sql1 = sqlInfo.getSql();
                if (sql == null) {
                   if (sql1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!sql.equals(sql1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFileName(){
       return this.fileName;
    }
    public String getHostName(){
       return this.hostName;
    }
    public String getSql(){
       return this.sql;
    }
    public int hashCode(){
       String $hostName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($hostName = this.getHostName()) == null)? i: $hostName.hashCode();
       result = i1 + 59;
       String $fileName = this.getFileName();
       int i2 = result * 59;
       i1 = ($fileName == null)? i: $fileName.hashCode();
       result = i2 + i1;
       String $sql = this.getSql();
       i1 = result * 59;
       if ($sql != null) {
          i = $sql.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFileName(String fileName){
       this.fileName = fileName;
    }
    public void setHostName(String hostName){
       this.hostName = hostName;
    }
    public void setSql(String sql){
       this.sql = sql;
    }
    public String toString(){
       return "SqlInfo\(hostName="+this.getHostName()+", fileName="+this.getFileName()+", sql="+this.getSql()+"\)";
    }
}
