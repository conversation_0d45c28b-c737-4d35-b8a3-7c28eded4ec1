package com.taikang.fly.check.rest.YbDrugInstructionsController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.ybDrugInstructions.YbDrugInstructionsSearchDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.YbDrugInstructionsService;

public class YbDrugInstructionsController	// class@0002bd from classes.dex
{
    private YbDrugInstructionsService ybDrugInstructionsService;
    private static final Logger log;

    static {
       YbDrugInstructionsController.log = LoggerFactory.getLogger(YbDrugInstructionsController.class);
    }
    public void YbDrugInstructionsController(){
       super();
    }
    public RmpResponse queryDrugInstructionsList(Integer page,Integer size,YbDrugInstructionsSearchDto ybDrugInstructionsSearchDto){
       return RmpResponse.success(this.ybDrugInstructionsService.queryList(page, size, ybDrugInstructionsSearchDto));
    }
}
