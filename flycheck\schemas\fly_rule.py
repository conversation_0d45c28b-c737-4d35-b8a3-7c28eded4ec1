"""
飞行检查规则Pydantic模型
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class FlyRuleBase(BaseModel):
    """规则基础模型"""
    rule_name: str = Field(..., description="规则名称")
    rule_describe: Optional[str] = Field(None, description="规则描述")
    rule_logic: Optional[str] = Field(None, description="规则逻辑")
    sql_name: Optional[str] = Field(None, description="SQL名称")
    rule_sql: Optional[str] = Field(None, description="规则SQL")
    
    # 规则分类
    rule_category1: Optional[str] = Field(None, description="规则分类1")
    rule_category2: Optional[str] = Field(None, description="规则分类2")
    rule_classify: Optional[str] = Field(None, description="规则分类")
    rule_dimension: Optional[str] = Field(None, description="规则维度")
    rule_level: Optional[str] = Field(None, description="规则级别")
    rule_type: Optional[str] = Field("sql", description="规则类型")
    
    # 政策相关
    policy_basis: Optional[str] = Field(None, description="政策依据")
    problem_description: Optional[str] = Field(None, description="问题描述")
    source_of_rule: Optional[str] = Field(None, description="规则来源")
    
    # 适用范围
    rule_scope_apply: Optional[str] = Field(None, description="规则适用范围")
    rule_suit_time_start: Optional[datetime] = Field(None, description="规则适用开始时间")
    rule_suit_time_end: Optional[datetime] = Field(None, description="规则适用结束时间")
    
    # 其他属性
    data_sources: Optional[str] = Field(None, description="数据源")
    region: Optional[str] = Field(None, description="地区")
    diagnosis_type: Optional[str] = Field(None, description="诊断类型")
    false_positive_rate: Optional[str] = Field(None, description="误报率")
    feedback_status: Optional[str] = Field(None, description="反馈状态")
    is_special: Optional[str] = Field("0", description="是否特殊规则")
    
    # 扩展字段
    red_field1: Optional[str] = Field(None, description="扩展字段1")
    red_field2: Optional[str] = Field(None, description="扩展字段2")
    red_field3: Optional[str] = Field(None, description="扩展字段3")
    ps: Optional[str] = Field(None, description="备注")


class FlyRuleCreate(FlyRuleBase):
    """创建规则模型"""
    pass


class FlyRuleUpdate(BaseModel):
    """更新规则模型"""
    rule_name: Optional[str] = None
    rule_describe: Optional[str] = None
    rule_logic: Optional[str] = None
    sql_name: Optional[str] = None
    rule_sql: Optional[str] = None
    rule_category1: Optional[str] = None
    rule_category2: Optional[str] = None
    rule_classify: Optional[str] = None
    rule_dimension: Optional[str] = None
    rule_level: Optional[str] = None
    rule_type: Optional[str] = None
    policy_basis: Optional[str] = None
    problem_description: Optional[str] = None
    source_of_rule: Optional[str] = None
    rule_scope_apply: Optional[str] = None
    rule_suit_time_start: Optional[datetime] = None
    rule_suit_time_end: Optional[datetime] = None
    data_sources: Optional[str] = None
    region: Optional[str] = None
    diagnosis_type: Optional[str] = None
    false_positive_rate: Optional[str] = None
    feedback_status: Optional[str] = None
    is_special: Optional[str] = None
    red_field1: Optional[str] = None
    red_field2: Optional[str] = None
    red_field3: Optional[str] = None
    ps: Optional[str] = None
    state: Optional[str] = None


class FlyRuleResponse(FlyRuleBase):
    """规则响应模型"""
    id: str
    state: str
    submit_state: Optional[str] = None
    result_flag: Optional[str] = None
    execution_date: Optional[datetime] = None
    created_time: datetime
    updated_time: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    
    class Config:
        from_attributes = True


class FlyRuleAuditBase(BaseModel):
    """规则审核基础模型"""
    audit_status: str = Field(..., description="审核状态")
    audit_opinion: Optional[str] = Field(None, description="审核意见")
    auditor: Optional[str] = Field(None, description="审核人")


class FlyRuleAuditCreate(FlyRuleAuditBase):
    """创建规则审核模型"""
    pass


class FlyRuleAuditResponse(FlyRuleAuditBase):
    """规则审核响应模型"""
    id: str
    rule_id: str
    audit_time: Optional[datetime] = None
    created_time: datetime
    
    class Config:
        from_attributes = True
