package com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleHisAddDto implements Serializable	// class@000113 from classes.dex
{
    private String newSqlName;
    private String ps;
    private String region;
    private String ruleName;
    private String ruleType;
    private String sqlName;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleHisAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleHisAddDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleHisAddDto){
          b = false;
       }else {
          FlyRuleHisAddDto uFlyRuleHisA = o;
          if (!uFlyRuleHisA.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleHisA.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleHisA.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uFlyRuleHisA.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = uFlyRuleHisA.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uFlyRuleHisA.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ps = this.getPs();
             String ps1 = uFlyRuleHisA.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_0085 :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $region = this.getRegion();
       int i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $sqlName = this.getSqlName();
       i2 = result * 59;
       i1 = ($sqlName == null)? i: $sqlName.hashCode();
       result = i2 + i1;
       String $newSqlName = this.getNewSqlName();
       i2 = result * 59;
       i1 = ($newSqlName == null)? i: $newSqlName.hashCode();
       result = i2 + i1;
       String $ruleType = this.getRuleType();
       i2 = result * 59;
       i1 = ($ruleType == null)? i: $ruleType.hashCode();
       result = i2 + i1;
       String ps = this.getPs();
       i1 = result * 59;
       if (ps != null) {
          i = ps.hashCode();
       }
       return (i1 + i);
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public String toString(){
       return "FlyRuleHisAddDto\(ruleName="+this.getRuleName()+", region="+this.getRegion()+", sqlName="+this.getSqlName()+", newSqlName="+this.getNewSqlName()+", ruleType="+this.getRuleType()+", ps="+this.getPs()+"\)";
    }
}
