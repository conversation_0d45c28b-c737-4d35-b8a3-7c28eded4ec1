package com.taikang.fly.check.dto.drg.DrgProfitAndLossDownloadDto;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class DrgProfitAndLossDownloadDto	// class@0000ec from classes.dex
{
    private Integer count;
    private String drgType;
    private String name;
    private String oraUserName;

    public void DrgProfitAndLossDownloadDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgProfitAndLossDownloadDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgProfitAndLossDownloadDto) {
             b = false;
          }else {
             DrgProfitAndLossDownloadDto uDrgProfitAn = o;
             if (!uDrgProfitAn.canEqual(this)) {
                b = false;
             }else {
                String oraUserName = this.getOraUserName();
                String oraUserName1 = uDrgProfitAn.getOraUserName();
                if (oraUserName == null) {
                   if (oraUserName1 != null) {
                      b = false;
                   }
                }else if(oraUserName.equals(oraUserName1)){
                }
                String drgType = this.getDrgType();
                String drgType1 = uDrgProfitAn.getDrgType();
                if (drgType == null) {
                   if (drgType1 != null) {
                      b = false;
                   }
                }else if(drgType.equals(drgType1)){
                }
                String name = this.getName();
                String name1 = uDrgProfitAn.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                Integer count = this.getCount();
                Integer count1 = uDrgProfitAn.getCount();
                if (count == null) {
                   if (count1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!count.equals(count1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public Integer getCount(){
       return this.count;
    }
    public String getDrgType(){
       return this.drgType;
    }
    public String getName(){
       return this.name;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public int hashCode(){
       String $oraUserName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($oraUserName = this.getOraUserName()) == null)? i: $oraUserName.hashCode();
       result = i1 + 59;
       String $drgType = this.getDrgType();
       int i2 = result * 59;
       i1 = ($drgType == null)? i: $drgType.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       Integer $count = this.getCount();
       i1 = result * 59;
       if ($count != null) {
          i = $count.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCount(Integer count){
       this.count = count;
    }
    public void setDrgType(String drgType){
       this.drgType = drgType;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public String toString(){
       return "DrgProfitAndLossDownloadDto\(oraUserName="+this.getOraUserName()+", drgType="+this.getDrgType()+", name="+this.getName()+", count="+this.getCount()+"\)";
    }
}
