package org.springframework.boot.loader.archive.Archive;
import java.lang.Iterable;
import java.util.jar.Manifest;
import org.springframework.boot.loader.archive.Archive$EntryFilter;
import java.util.List;
import java.net.URL;

public interface abstract Archive implements Iterable	// class@00053b from classes.dex
{

    Manifest getManifest();
    List getNestedArchives(Archive$EntryFilter p0);
    URL getUrl();
}
