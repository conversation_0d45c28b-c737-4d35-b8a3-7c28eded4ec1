package com.taikang.fly.check.mybatis.dao.RoleMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.Role;
import java.util.Map;
import java.util.List;

public interface abstract RoleMapper implements BaseMapper	// class@00021b from classes.dex
{

    Integer deleteByRoleId(String p0);
    Role findByRoleCode(String p0);
    Role getByRoleId(String p0);
    Integer getRolePageCount(Map p0);
    void insertSelective(Role p0);
    List queryListByParams(Map p0);
    Integer save(Role p0);
    Integer update(Role p0);
    Integer updateRoleById(Role p0);
}
