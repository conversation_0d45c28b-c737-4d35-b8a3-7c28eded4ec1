package com.taikang.fly.check.dto.mapstruct.FlyRuleHisMappingImpl;
import com.taikang.fly.check.dto.mapstruct.FlyRuleHisMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleHis;
import java.lang.String;
import java.util.Date;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisRespDto;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZoneId;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisEditDto;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class FlyRuleHisMappingImpl implements FlyRuleHisMapping	// class@000155 from classes.dex
{

    public void FlyRuleHisMappingImpl(){
       super();
    }
    public FlyRuleHis addDtoToDomain(FlyRuleHisAddDto addDto){
       FlyRuleHis uFlyRuleHis;
       if (addDto == null) {
          uFlyRuleHis = null;
       }else {
          uFlyRuleHis = new FlyRuleHis();
          uFlyRuleHis.setRuleName(addDto.getRuleName());
          uFlyRuleHis.setRegion(addDto.getRegion());
          uFlyRuleHis.setRuleType(addDto.getRuleType());
          uFlyRuleHis.setPs(addDto.getPs());
          uFlyRuleHis.setSqlName(addDto.getSqlName());
          uFlyRuleHis.setNewSqlName(addDto.getNewSqlName());
          uFlyRuleHis.setCreatedTime(new Date());
          uFlyRuleHis.setOperateTime(new Date());
       }
       return uFlyRuleHis;
    }
    public FlyRuleHisRespDto domainToInfoDto(FlyRuleHis domain){
       FlyRuleHisRespDto uFlyRuleHisR;
       if (domain == null) {
          uFlyRuleHisR = null;
       }else {
          uFlyRuleHisR = new FlyRuleHisRespDto();
          uFlyRuleHisR.setRuleName(domain.getRuleName());
          uFlyRuleHisR.setRegion(domain.getRegion());
          uFlyRuleHisR.setRuleType(domain.getRuleType());
          uFlyRuleHisR.setPs(domain.getPs());
          uFlyRuleHisR.setSqlName(domain.getSqlName());
          uFlyRuleHisR.setNewSqlName(domain.getNewSqlName());
          uFlyRuleHisR.setId(domain.getId());
          uFlyRuleHisR.setOperator(domain.getOperator());
          uFlyRuleHisR.setResultFlag(domain.getResultFlag());
          uFlyRuleHisR.setRemoved(domain.getRemoved());
          if (domain.getOperateTime() != null) {
             uFlyRuleHisR.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             uFlyRuleHisR.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
       }
       return uFlyRuleHisR;
    }
    public FlyRuleHis dtoToDomain(FlyRuleHisRespDto flyRuleRespDto){
       FlyRuleHis uFlyRuleHis;
       if (flyRuleRespDto == null) {
          uFlyRuleHis = null;
       }else {
          uFlyRuleHis = new FlyRuleHis();
          uFlyRuleHis.setId(flyRuleRespDto.getId());
          uFlyRuleHis.setRuleName(flyRuleRespDto.getRuleName());
          uFlyRuleHis.setOperator(flyRuleRespDto.getOperator());
          uFlyRuleHis.setRegion(flyRuleRespDto.getRegion());
          uFlyRuleHis.setRuleType(flyRuleRespDto.getRuleType());
          uFlyRuleHis.setResultFlag(flyRuleRespDto.getResultFlag());
          uFlyRuleHis.setRemoved(flyRuleRespDto.getRemoved());
          uFlyRuleHis.setPs(flyRuleRespDto.getPs());
          uFlyRuleHis.setSqlName(flyRuleRespDto.getSqlName());
          uFlyRuleHis.setNewSqlName(flyRuleRespDto.getNewSqlName());
          uFlyRuleHis.setCreatedTime(new Date());
          uFlyRuleHis.setOperateTime(new Date());
       }
       return uFlyRuleHis;
    }
    public FlyRuleHis editDtoToDomain(FlyRuleHisEditDto editDto,FlyRuleHis domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleName(editDto.getRuleName());
          domain.setRegion(editDto.getRegion());
          domain.setRuleType(editDto.getRuleType());
          domain.setPs(editDto.getPs());
          domain.setSqlName(editDto.getSqlName());
          domain.setNewSqlName(editDto.getNewSqlName());
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public List entityToDtos(List flyRules){
       List list;
       if (flyRules == null) {
          list = null;
       }else {
          list = new ArrayList(flyRules.size());
          Iterator iterator = flyRules.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToInfoDto(iterator.next()));
          }
       }
       return list;
    }
}
