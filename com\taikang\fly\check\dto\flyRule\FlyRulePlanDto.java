package com.taikang.fly.check.dto.flyRule.FlyRulePlanDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRulePlanDto implements Serializable	// class@000105 from classes.dex
{
    private String creater;
    private String diagnosisType;
    private String planId;
    private String region;
    private String ruleCategory1;
    private String ruleLevel;
    private String ruleName;
    private String state;
    private static final long serialVersionUID = 0x1;

    public void FlyRulePlanDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRulePlanDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRulePlanDto){
          b = false;
       }else {
          FlyRulePlanDto uFlyRulePlan = o;
          if (!uFlyRulePlan.canEqual(this)) {
             b = false;
          }else {
             String planId = this.getPlanId();
             String planId1 = uFlyRulePlan.getPlanId();
             if (planId == null) {
                if (planId1 != null) {
                   b = false;
                }
             }else if(planId.equals(planId1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRulePlan.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRulePlan.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String state = this.getState();
             String state1 = uFlyRulePlan.getState();
             if (state == null) {
                if (state1 != null) {
                   b = false;
                }
             }else if(state.equals(state1)){
             }
             String creater = this.getCreater();
             String creater1 = uFlyRulePlan.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRulePlan.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                label_009d :
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRulePlan.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uFlyRulePlan.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                label_00cb :
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getPlanId(){
       return this.planId;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getState(){
       return this.state;
    }
    public int hashCode(){
       String $planId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($planId = this.getPlanId()) == null)? i: $planId.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       int i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $state = this.getState();
       i2 = result * 59;
       i1 = ($state == null)? i: $state.hashCode();
       result = i2 + i1;
       String $creater = this.getCreater();
       i2 = result * 59;
       i1 = ($creater == null)? i: $creater.hashCode();
       result = i2 + i1;
       String ruleLevel = this.getRuleLevel();
       i2 = result * 59;
       i1 = (ruleLevel == null)? i: ruleLevel.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i2 = (i2 + i1) * 59;
       i1 = (diagnosisTyp == null)? i: diagnosisTyp.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i2 + i1) * 59;
       if (ruleCategory != null) {
          i = ruleCategory.hashCode();
       }
       return (i1 + i);
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setPlanId(String planId){
       this.planId = planId;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setState(String state){
       this.state = state;
    }
    public String toString(){
       return "FlyRulePlanDto\(planId="+this.getPlanId()+", ruleName="+this.getRuleName()+", region="+this.getRegion()+", state="+this.getState()+", creater="+this.getCreater()+", ruleLevel="+this.getRuleLevel()+", diagnosisType="+this.getDiagnosisType()+", ruleCategory1="+this.getRuleCategory1()+"\)";
    }
}
