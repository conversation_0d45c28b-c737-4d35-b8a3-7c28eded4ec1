package com.taikang.fly.check.dto.mapstruct.YbDiagnosisTreatmentListMappingImpl;
import com.taikang.fly.check.dto.mapstruct.YbDiagnosisTreatmentListMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.YbDiagnosisTreatmentList;
import com.taikang.fly.check.dto.ybDiagnosisTreatmentList.YbDiagnosisTreatmentListRespDto;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.DiagnosisTreatment;
import java.math.BigDecimal;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class YbDiagnosisTreatmentListMappingImpl implements YbDiagnosisTreatmentListMapping	// class@000186 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void YbDiagnosisTreatmentListMappingImpl(){
       super();
    }
    public YbDiagnosisTreatmentListRespDto domainToRespDto(YbDiagnosisTreatmentList ybDiagnosisTreatmentList){
       YbDiagnosisTreatmentListRespDto ybDiagnosisT;
       if (ybDiagnosisTreatmentList == null) {
          ybDiagnosisT = null;
       }else {
          ybDiagnosisT = new YbDiagnosisTreatmentListRespDto();
          ybDiagnosisT.setPaymentCategory(ybDiagnosisTreatmentList.getPaymentCategory());
          ybDiagnosisT.setRemark(ybDiagnosisTreatmentList.getRemark());
       }
       return ybDiagnosisT;
    }
    public YbDiagnosisTreatmentListRespDto domainsToRespDtoList(DiagnosisTreatment diagnosisTreatment){
       YbDiagnosisTreatmentListRespDto ybDiagnosisT;
       if (diagnosisTreatment == null) {
          ybDiagnosisT = null;
       }else {
          ybDiagnosisT = new YbDiagnosisTreatmentListRespDto();
          ybDiagnosisT.setLeavePrice(this.typeConversionMapper.BigDecimal2String(diagnosisTreatment.getLeavePrice()));
          ybDiagnosisT.setHospitalSelfPay(this.typeConversionMapper.BigDecimal2Percent(diagnosisTreatment.getHospitalSelfPay()));
          ybDiagnosisT.setOutpatientSelfPay(this.typeConversionMapper.BigDecimal2Percent(diagnosisTreatment.getOutpatientSelfPay()));
          ybDiagnosisT.setYourSelfPay(this.typeConversionMapper.BigDecimal2Percent(diagnosisTreatment.getYourSelfPay()));
          ybDiagnosisT.setThreeMaxPrice(this.typeConversionMapper.BigDecimal2String(diagnosisTreatment.getThreeMaxPrice()));
          ybDiagnosisT.setTwoMaxPrice(this.typeConversionMapper.BigDecimal2String(diagnosisTreatment.getTwoMaxPrice()));
          ybDiagnosisT.setStartTime(this.typeConversionMapper.DateTime2String(diagnosisTreatment.getStartTime()));
          ybDiagnosisT.setOneMaxPrice(this.typeConversionMapper.BigDecimal2String(diagnosisTreatment.getOneMaxPrice()));
          ybDiagnosisT.setEndTime(this.typeConversionMapper.DateTime2String(diagnosisTreatment.getEndTime()));
          ybDiagnosisT.setMaxPrice(this.typeConversionMapper.BigDecimal2String(diagnosisTreatment.getMaxPrice()));
          ybDiagnosisT.setWorkInjurySelfPay(this.typeConversionMapper.BigDecimal2Percent(diagnosisTreatment.getWorkInjurySelfPay()));
          ybDiagnosisT.setProCode(diagnosisTreatment.getProCode());
          ybDiagnosisT.setProName(diagnosisTreatment.getProName());
          ybDiagnosisT.setProConnotation(diagnosisTreatment.getProConnotation());
          ybDiagnosisT.setOutProInfo(diagnosisTreatment.getOutProInfo());
          ybDiagnosisT.setPayer(diagnosisTreatment.getPayer());
          ybDiagnosisT.setPaymentCategory(diagnosisTreatment.getPaymentCategory());
          ybDiagnosisT.setRemark(diagnosisTreatment.getRemark());
       }
       return ybDiagnosisT;
    }
    public List domainsToRespDtoList(List ybDiagnosisTreatmentLists){
       List list;
       if (ybDiagnosisTreatmentLists == null) {
          list = null;
       }else {
          list = new ArrayList(ybDiagnosisTreatmentLists.size());
          Iterator iterator = ybDiagnosisTreatmentLists.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainsToRespDtoList(iterator.next()));
          }
       }
       return list;
    }
}
