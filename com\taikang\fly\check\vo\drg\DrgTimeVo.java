package com.taikang.fly.check.vo.drg.DrgTimeVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgTimeVo	// class@00037b from classes.dex
{
    private String endDate;
    private String startDate;

    public void DrgTimeVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgTimeVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgTimeVo) {
             b = false;
          }else {
             DrgTimeVo uDrgTimeVo = o;
             if (!uDrgTimeVo.canEqual(this)) {
                b = false;
             }else {
                String startDate = this.getStartDate();
                String startDate1 = uDrgTimeVo.getStartDate();
                if (startDate == null) {
                   if (startDate1 != null) {
                      b = false;
                   }
                }else if(startDate.equals(startDate1)){
                }
                String endDate = this.getEndDate();
                String endDate1 = uDrgTimeVo.getEndDate();
                if (endDate == null) {
                   if (endDate1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!endDate.equals(endDate1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getEndDate(){
       return this.endDate;
    }
    public String getStartDate(){
       return this.startDate;
    }
    public int hashCode(){
       String $startDate;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($startDate = this.getStartDate()) == null)? i: $startDate.hashCode();
       result = i1 + 59;
       String $endDate = this.getEndDate();
       i1 = result * 59;
       if ($endDate != null) {
          i = $endDate.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setEndDate(String endDate){
       this.endDate = endDate;
    }
    public void setStartDate(String startDate){
       this.startDate = startDate;
    }
    public String toString(){
       return "DrgTimeVo\(startDate="+this.getStartDate()+", endDate="+this.getEndDate()+"\)";
    }
}
