package com.taikang.fly.check.dto.KillSessionDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class KillSessionDto implements Serializable	// class@0000a5 from classes.dex
{
    private String objectName;
    private String owner;
    private String serial;
    private String sid;
    private static final long serialVersionUID = 0x1;

    public void KillSessionDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof KillSessionDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof KillSessionDto) {
             b = false;
          }else {
             KillSessionDto killSessionD = o;
             if (!killSessionD.canEqual(this)) {
                b = false;
             }else {
                String owner = this.getOwner();
                String owner1 = killSessionD.getOwner();
                if (owner == null) {
                   if (owner1 != null) {
                      b = false;
                   }
                }else if(owner.equals(owner1)){
                }
                String objectName = this.getObjectName();
                String objectName1 = killSessionD.getObjectName();
                if (objectName == null) {
                   if (objectName1 != null) {
                      b = false;
                   }
                }else if(objectName.equals(objectName1)){
                }
                String sid = this.getSid();
                String sid1 = killSessionD.getSid();
                if (sid == null) {
                   if (sid1 != null) {
                      b = false;
                   }
                }else if(sid.equals(sid1)){
                }
                String serial = this.getSerial();
                String serial1 = killSessionD.getSerial();
                if (serial == null) {
                   if (serial1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!serial.equals(serial1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getObjectName(){
       return this.objectName;
    }
    public String getOwner(){
       return this.owner;
    }
    public String getSerial(){
       return this.serial;
    }
    public String getSid(){
       return this.sid;
    }
    public int hashCode(){
       String $owner;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($owner = this.getOwner()) == null)? i: $owner.hashCode();
       result = i1 + 59;
       String $objectName = this.getObjectName();
       int i2 = result * 59;
       i1 = ($objectName == null)? i: $objectName.hashCode();
       result = i2 + i1;
       String $sid = this.getSid();
       i2 = result * 59;
       i1 = ($sid == null)? i: $sid.hashCode();
       result = i2 + i1;
       String $serial = this.getSerial();
       i1 = result * 59;
       if ($serial != null) {
          i = $serial.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setObjectName(String objectName){
       this.objectName = objectName;
    }
    public void setOwner(String owner){
       this.owner = owner;
    }
    public void setSerial(String serial){
       this.serial = serial;
    }
    public void setSid(String sid){
       this.sid = sid;
    }
    public String toString(){
       return "KillSessionDto\(owner="+this.getOwner()+", objectName="+this.getObjectName()+", sid="+this.getSid()+", serial="+this.getSerial()+"\)";
    }
}
