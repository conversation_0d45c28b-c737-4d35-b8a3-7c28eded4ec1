package com.taikang.fly.check.comm.BizException;
import java.lang.RuntimeException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.String;

public class BizException extends RuntimeException	// class@00006e from classes.dex
{
    private final String code;
    private static final long serialVersionUID = 0x1;

    public void BizException(ResponseCodeEnum exceptionEnum){
       super(exceptionEnum.getMsg());
       this.code = exceptionEnum.getCode();
    }
    public void BizException(String code,String msg){
       super(msg);
       this.code = code;
    }
    public String getCode(){
       return this.code;
    }
}
