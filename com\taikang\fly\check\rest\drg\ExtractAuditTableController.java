package com.taikang.fly.check.rest.drg.ExtractAuditTableController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.util.List;
import com.taikang.fly.check.comm.CommResponse;
import java.util.ArrayList;
import com.taikang.fly.check.service.ProvincialPlatformService;
import java.lang.String;
import com.taikang.fly.check.vo.ProvincialPlatformMatching;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;

public class ExtractAuditTableController	// class@0002c2 from classes.dex
{
    private ProvincialPlatformService provincialPlatformService;
    private static final Logger log;

    static {
       ExtractAuditTableController.log = LoggerFactory.getLogger(ExtractAuditTableController.class);
    }
    public void ExtractAuditTableController(){
       super();
    }
    public CommResponse exeSql(List idList){
       ArrayList list = this.provincialPlatformService.exeSql(idList);
       CommResponse uCommRespons = (list.size() > 0)? CommResponse.error("0", list.toString(), null): CommResponse.success();
       return uCommRespons;
    }
    public CommResponse getList(){
       return CommResponse.success(this.provincialPlatformService.getList("02"));
    }
    public CommResponse rollbackSql(ProvincialPlatformMatching provincialPlatformMatching){
       boolean b;
       CommResponse uCommRespons = (b = this.provincialPlatformService.updateById(provincialPlatformMatching))? CommResponse.success(): CommResponse.error(ResponseCodeEnum.SAVE_ERROR);
       return uCommRespons;
    }
    public CommResponse updateSql(ProvincialPlatformMatching provincialPlatformMatching){
       boolean b;
       CommResponse uCommRespons = (b = this.provincialPlatformService.updateById(provincialPlatformMatching))? CommResponse.success(): CommResponse.error(ResponseCodeEnum.SAVE_ERROR);
       return uCommRespons;
    }
}
