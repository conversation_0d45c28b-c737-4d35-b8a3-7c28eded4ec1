package com.taikang.fly.check.dto.mapstruct.DrugCatalogueListMappingImpl;
import com.taikang.fly.check.dto.mapstruct.DrugCatalogueListMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.DrugCatalogue;
import com.taikang.fly.check.dto.ybdruglist.DrugCatalogueListRespDto;
import java.math.BigDecimal;
import java.lang.String;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class DrugCatalogueListMappingImpl implements DrugCatalogueListMapping	// class@00014f from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void DrugCatalogueListMappingImpl(){
       super();
    }
    public DrugCatalogueListRespDto entryToDtoList(DrugCatalogue drugCatalogue){
       DrugCatalogueListRespDto uDrugCatalog;
       if (drugCatalogue == null) {
          uDrugCatalog = null;
       }else {
          uDrugCatalog = new DrugCatalogueListRespDto();
          uDrugCatalog.setLeavePrice(this.typeConversionMapper.BigDecimal2String(drugCatalogue.getLeavePrice()));
          uDrugCatalog.setHospitalSelfPay(this.typeConversionMapper.BigDecimal2Percent(drugCatalogue.getHospitalSelfPay()));
          uDrugCatalog.setOutpatientSelfPay(this.typeConversionMapper.BigDecimal2Percent(drugCatalogue.getOutpatientSelfPay()));
          uDrugCatalog.setYourSelfPay(this.typeConversionMapper.BigDecimal2Percent(drugCatalogue.getYourSelfPay()));
          uDrugCatalog.setThreeMaxPrice(this.typeConversionMapper.BigDecimal2String(drugCatalogue.getThreeMaxPrice()));
          uDrugCatalog.setTwoMaxPrice(this.typeConversionMapper.BigDecimal2String(drugCatalogue.getTwoMaxPrice()));
          uDrugCatalog.setStartTime(this.typeConversionMapper.DateTime2String(drugCatalogue.getStartTime()));
          uDrugCatalog.setOneMaxPrice(this.typeConversionMapper.BigDecimal2String(drugCatalogue.getOneMaxPrice()));
          uDrugCatalog.setEndTime(this.typeConversionMapper.DateTime2String(drugCatalogue.getEndTime()));
          uDrugCatalog.setMaxPrice(this.typeConversionMapper.BigDecimal2String(drugCatalogue.getMaxPrice()));
          uDrugCatalog.setWorkInjurySelfPay(this.typeConversionMapper.BigDecimal2Percent(drugCatalogue.getWorkInjurySelfPay()));
          uDrugCatalog.setDrugsCode(drugCatalogue.getDrugsCode());
          uDrugCatalog.setDrugsName(drugCatalogue.getDrugsName());
          uDrugCatalog.setDosageForm(drugCatalogue.getDosageForm());
          uDrugCatalog.setPayer(drugCatalogue.getPayer());
          uDrugCatalog.setPaymentCategory(drugCatalogue.getPaymentCategory());
          uDrugCatalog.setRemark(drugCatalogue.getRemark());
       }
       return uDrugCatalog;
    }
    public List entryToDtoList(List drugCatalogue){
       List list;
       if (drugCatalogue == null) {
          list = null;
       }else {
          list = new ArrayList(drugCatalogue.size());
          Iterator iterator = drugCatalogue.iterator();
          while (iterator.hasNext()) {
             list.add(this.entryToDtoList(iterator.next()));
          }
       }
       return list;
    }
}
