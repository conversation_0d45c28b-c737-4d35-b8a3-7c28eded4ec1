package com.taikang.fly.check.dto.planLog.PlanExecuteDto;
import java.lang.Object;
import java.lang.String;
import org.springframework.jdbc.core.JdbcTemplate;
import java.util.List;
import java.lang.StringBuilder;

public class PlanExecuteDto	// class@0001a7 from classes.dex
{
    private List errorRules;
    private String hospName;
    private String initFilePath;
    private JdbcTemplate jdbcTemplate;
    private List ruleModelList;

    public void PlanExecuteDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanExecuteDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof PlanExecuteDto) {
             b = false;
          }else {
             PlanExecuteDto planExecuteD = o;
             if (!planExecuteD.canEqual(this)) {
                b = false;
             }else {
                String hospName = this.getHospName();
                String hospName1 = planExecuteD.getHospName();
                if (hospName == null) {
                   if (hospName1 != null) {
                      b = false;
                   }
                }else if(hospName.equals(hospName1)){
                }
                JdbcTemplate jdbcTemplate = this.getJdbcTemplate();
                JdbcTemplate jdbcTemplate1 = planExecuteD.getJdbcTemplate();
                if (jdbcTemplate == null) {
                   if (jdbcTemplate1 != null) {
                      b = false;
                   }
                }else if(jdbcTemplate.equals(jdbcTemplate1)){
                }
                String initFilePath = this.getInitFilePath();
                String initFilePath1 = planExecuteD.getInitFilePath();
                if (initFilePath == null) {
                   if (initFilePath1 != null) {
                      b = false;
                   }
                }else if(initFilePath.equals(initFilePath1)){
                }
                List ruleModelLis = this.getRuleModelList();
                List ruleModelLis1 = planExecuteD.getRuleModelList();
                if (ruleModelLis == null) {
                   if (ruleModelLis1 != null) {
                      b = false;
                   }
                }else if(ruleModelLis.equals(ruleModelLis1)){
                }
                List errorRules = this.getErrorRules();
                List errorRules1 = planExecuteD.getErrorRules();
                if (errorRules == null) {
                   if (errorRules1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!errorRules.equals(errorRules1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getErrorRules(){
       return this.errorRules;
    }
    public String getHospName(){
       return this.hospName;
    }
    public String getInitFilePath(){
       return this.initFilePath;
    }
    public JdbcTemplate getJdbcTemplate(){
       return this.jdbcTemplate;
    }
    public List getRuleModelList(){
       return this.ruleModelList;
    }
    public int hashCode(){
       String $hospName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($hospName = this.getHospName()) == null)? i: $hospName.hashCode();
       result = i1 + 59;
       JdbcTemplate $jdbcTemplate = this.getJdbcTemplate();
       int i2 = result * 59;
       i1 = ($jdbcTemplate == null)? i: $jdbcTemplate.hashCode();
       result = i2 + i1;
       String $initFilePath = this.getInitFilePath();
       i2 = result * 59;
       i1 = ($initFilePath == null)? i: $initFilePath.hashCode();
       result = i2 + i1;
       List $ruleModelList = this.getRuleModelList();
       i2 = result * 59;
       i1 = ($ruleModelList == null)? i: $ruleModelList.hashCode();
       result = i2 + i1;
       List $errorRules = this.getErrorRules();
       i1 = result * 59;
       if ($errorRules != null) {
          i = $errorRules.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setErrorRules(List errorRules){
       this.errorRules = errorRules;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public void setInitFilePath(String initFilePath){
       this.initFilePath = initFilePath;
    }
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate){
       this.jdbcTemplate = jdbcTemplate;
    }
    public void setRuleModelList(List ruleModelList){
       this.ruleModelList = ruleModelList;
    }
    public String toString(){
       return "PlanExecuteDto\(hospName="+this.getHospName()+", jdbcTemplate="+this.getJdbcTemplate()+", initFilePath="+this.getInitFilePath()+", ruleModelList="+this.getRuleModelList()+", errorRules="+this.getErrorRules()+"\)";
    }
}
