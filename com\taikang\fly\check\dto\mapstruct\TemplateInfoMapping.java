package com.taikang.fly.check.dto.mapstruct.TemplateInfoMapping;
import com.taikang.fly.check.dto.templateInfo.TemplateInfoAddDto;
import com.taikang.fly.check.mybatis.domain.TemplateInfo;
import com.taikang.fly.check.dto.templateInfo.TemplateInfoEditDto;

public interface abstract TemplateInfoMapping	// class@00017b from classes.dex
{

    TemplateInfo addDto2TemplateInfo(TemplateInfoAddDto p0);
    TemplateInfo editDto2TemplateInfo(TemplateInfoEditDto p0);
}
