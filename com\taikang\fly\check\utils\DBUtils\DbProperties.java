package com.taikang.fly.check.utils.DBUtils.DbProperties;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DbProperties	// class@000335 from classes.dex
{
    private String password;
    private String url;
    private String userName;

    public void DbProperties(){
       super();
    }
    public void DbProperties(String url,String userName,String password){
       super();
       this.url = url;
       this.userName = userName;
       this.password = password;
    }
    protected boolean canEqual(Object other){
       return other instanceof DbProperties;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DbProperties) {
             b = false;
          }else {
             DbProperties uDbPropertie = o;
             if (!uDbPropertie.canEqual(this)) {
                b = false;
             }else {
                String url = this.getUrl();
                String url1 = uDbPropertie.getUrl();
                if (url == null) {
                   if (url1 != null) {
                      b = false;
                   }
                }else if(url.equals(url1)){
                }
                String userName = this.getUserName();
                String userName1 = uDbPropertie.getUserName();
                if (userName == null) {
                   if (userName1 != null) {
                      b = false;
                   }
                }else if(userName.equals(userName1)){
                }
                String password = this.getPassword();
                String password1 = uDbPropertie.getPassword();
                if (password == null) {
                   if (password1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!password.equals(password1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getPassword(){
       return this.password;
    }
    public String getUrl(){
       return this.url;
    }
    public String getUserName(){
       return this.userName;
    }
    public int hashCode(){
       String $url;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($url = this.getUrl()) == null)? i: $url.hashCode();
       result = i1 + 59;
       String $userName = this.getUserName();
       int i2 = result * 59;
       i1 = ($userName == null)? i: $userName.hashCode();
       result = i2 + i1;
       String $password = this.getPassword();
       i1 = result * 59;
       if ($password != null) {
          i = $password.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setPassword(String password){
       this.password = password;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public void setUserName(String userName){
       this.userName = userName;
    }
    public String toString(){
       return "DbProperties\(url="+this.getUrl()+", userName="+this.getUserName()+", password="+this.getPassword()+"\)";
    }
}
