package com.taikang.fly.check.utils.ZipUtils;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import java.io.File;
import net.lingala.zip4j.model.ZipParameters;
import org.springframework.util.StringUtils;
import net.lingala.zip4j.core.ZipFile;
import java.lang.Exception;
import java.lang.Throwable;
import java.util.List;
import java.util.zip.ZipOutputStream;
import java.io.FileOutputStream;
import java.lang.StringBuilder;
import java.io.OutputStream;
import java.util.Iterator;
import java.util.zip.ZipEntry;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.io.FileInputStream;

public class ZipUtils	// class@000350 from classes.dex
{
    private static final Logger log;

    static {
       ZipUtils.log = LoggerFactory.getLogger(ZipUtils.class);
    }
    public void ZipUtils(){
       super();
    }
    public static File compress(String destFileName,String pwd,File[] files){
       File uFile;
       int i = 0;
       ZipParameters parameters = new ZipParameters();
       parameters.setCompressionMethod(8);
       parameters.setCompressionLevel(5);
       if (!StringUtils.isEmpty(pwd)) {
          parameters.setEncryptFiles(true);
          parameters.setEncryptionMethod(i);
          parameters.setPassword(pwd.toCharArray());
       }
       try{
          ZipFile zipFile = new ZipFile(destFileName);
          int len = files.length;
          for (; i < len; i = i + 1) {
             zipFile.addFile(files[i], parameters);
          }
          uFile = new File(destFileName);
       }catch(java.lang.Exception e0){
          ZipUtils.log.info(e0.getMessage(), e0);
          uFile = null;
       }
       return uFile;
    }
    public static void compressFiles(String zipPathDir,String zipFileName,List fileList){
       try{
          ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(new File(zipPathDir+zipFileName)));
          try{
             int i = 0;
             File zipFile = new File(zipPathDir);
             if (!zipFile.exists()) {
                zipFile.mkdirs();
             }
             Iterator iterator = fileList.iterator();
             while (iterator.hasNext()) {
                File uFile = new File(iterator.next());
                if (uFile.exists()) {
                   zos.putNextEntry(new ZipEntry(uFile.getName()));
                   byte[] uobyteArray = new byte[2048];
                   ZipUtils.compressSingleFile(uFile, zos, uobyteArray);
                }
             }
             try{
                zos.flush();
                if (zos != null) {
                   if (i) {
                      zos.close();
                   }else {
                      zos.close();
                   }
                }
             }catch(java.lang.Throwable e7){
                i.addSuppressed(e7);
             }catch(java.lang.Exception e1){
                throw new BizException(ResponseCodeEnum.DATASOURCE_SAVE_ERROR);
             }
             return;
          }catch(java.lang.Throwable e7){
             throw e7;
          }
       }catch(java.lang.Exception e1){
       }
    }
    public static void compressSingleFile(File file,ZipOutputStream zos,byte[] buffer){
       int len;
       try{
          FileInputStream fis = new FileInputStream(file);
          int i = 0;
          try{
             while ((len = fis.read(buffer)) > 0) {
                zos.write(buffer, 0, len);
                zos.flush();
             }
             try{
                zos.closeEntry();
                if (fis != null) {
                   if (i) {
                      fis.close();
                   }else {
                      fis.close();
                   }
                }
             }catch(java.lang.Throwable e3){
                i.addSuppressed(e3);
             }
             return;
          }catch(java.lang.Throwable e3){
             throw e3;
          }
       }catch(java.lang.Exception e0){
          throw new BizException(ResponseCodeEnum.DATASOURCE_SAVE_ERROR);
       }
    }
}
