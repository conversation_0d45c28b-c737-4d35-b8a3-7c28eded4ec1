package com.taikang.fly.check.utils.csvutil.CtrUtil;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import com.taikang.fly.check.dto.csventry.CsvImportDTO;
import java.lang.CharSequence;
import cn.hutool.core.util.StrUtil;
import java.lang.StringBuilder;
import com.taikang.fly.check.utils.PinyinUtils;
import com.taikang.fly.check.dto.csventry.CSVHeaderEntry;
import java.lang.Integer;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import cn.hutool.core.io.FileUtil;
import java.io.File;
import java.nio.charset.Charset;
import com.taikang.fly.check.utils.CharsetUtils;
import java.lang.System;

public class CtrUtil	// class@000352 from classes.dex
{
    private static final String WINDOWS = "windows";

    public void CtrUtil(){
       super();
    }
    public static String createCtrFile(List csvHeaderEntries,String pathName,String tableName,CsvImportDTO csvImportDTO){
       int i1;
       String str2;
       String ctlCharset = CtrUtil.getCtlCharset(pathName);
       if (StrUtil.isEmpty(csvImportDTO.getSeparator())) {
          csvImportDTO.setSeparator(",");
       }
       if (StrUtil.isEmpty(csvImportDTO.getBoundarySymbol())) {
          csvImportDTO.setBoundarySymbol("\"");
       }
       StringBuilder str = "";
       StringBuilder str1 = str+"load\n"+"characterset \'"+ctlCharset+"\'\n"+"infile \""+pathName+"\""+" "+"\"str \'\\r\\n\'"+"\"\n"+"append into table \""+PinyinUtils.getPingYin(tableName).toUpperCase()+"\"\n"+"fields terminated by \""+csvImportDTO.getSeparator()+"\" \n"+"OPTIONALLY ENCLOSED BY \'"+csvImportDTO.getBoundarySymbol()+"\'\n"+"trailing nullcols\n"+"\(\n";
       int i = 0;
       while (true) {
          if (i < csvHeaderEntries.size()) {
             CSVHeaderEntry uCSVHeaderEn = csvHeaderEntries.get(i);
             if (uCSVHeaderEn.getFieldChecked().intValue() == 1) {
                String fieldType = uCSVHeaderEn.getFieldType();
                i1 = -1;
                switch (fieldType.hashCode()){
                    case 0x86b89f31:
                      if (fieldType.equals("DECIMAL")) {
                         i1 = 2;
                      }
                      break;
                    case 0x9f810d1e:
                      if (fieldType.equals("INTEGER")) {
                         i1 = 0;
                      }
                      break;
                    case 0xa96138f6:
                      if (fieldType.equals("TIMESTAMP")) {
                         i1 = 4;
                      }
                      break;
                    case 0xe3d960f5:
                      if (fieldType.equals("VARCHAR2")) {
                         i1 = 1;
                      }
                      break;
                    case 0x1fe7ae:
                      if (fieldType.equals("DATE")) {
                         i1 = 3;
                      }
                      break;
                }
                switch (i1){
                    case 0:
                      str2 = "integer external";
                   label_010f :
                      str1 = str.append(PinyinUtils.getPingYin(uCSVHeaderEn.getFieldName()).toUpperCase()).append(" ").append(str2);
                      break;
                    case 1:
                      str2 = "".append("char\(").append(uCSVHeaderEn.getFieldLength()).append("\)").toString();
                      goto label_010f ;
                      break;
                    case 2:
                      str2 = "decimal external";
                      goto label_010f ;
                      break;
                    case 3:
                      str2 = "".append("date \"").append(uCSVHeaderEn.getDateFormat()).append("\"").toString();
                      goto label_010f ;
                      break;
                    case 4:
                      str2 = "".append("timestamp \"").append(uCSVHeaderEn.getDateFormat()).append("\"").toString();
                      goto label_010f ;
                      break;
                    default:
                      throw new BizException(ResponseCodeEnum.TYPE_IS_NOT);
                }
             }else {
                str1 = str.append(PinyinUtils.getPingYin(uCSVHeaderEn.getFieldName()).toUpperCase()).append(" FILLER");
             }
             i1 = csvHeaderEntries.size() - 1;
             if (i < i1) {
                str = str.append(" ,\n");
             }
             i = i + 1;
          }else {
             int i2 = csvHeaderEntries.size() - 1;
             if (csvHeaderEntries.get(i2).getFieldChecked().intValue() == 1 && ("INTEGER".equals(csvHeaderEntries.get(i2).getFieldType()) && !"DECIMAL".equals(csvHeaderEntries.get(i2).getFieldType()))) {
                str = str+" TERMINATED BY WHITESPACE";
                break ;
             }
             break ;
          }
       }
       File uFile = FileUtil.file(FileUtil.getParent(pathName, 1), FileUtil.mainName(pathName)+".ctl");
       FileUtil.writeString(str+"\n\)", uFile, Charset.forName(CtrUtil.getSysCharset()));
       return uFile.getPath();
    }
    public static String getCtlCharset(String fileFullPath){
       String str;
       CharsetUtils charsetUtils = new CharsetUtils();
       String charset = charsetUtils.getCharset(new File(fileFullPath));
       int i = -1;
       switch (charset.hashCode()){
           case 0x112d0:
             if (charset.equals("GBK")) {
                i = 0;
             }
             break;
           case 0x4d50972:
             if (charset.equals("UTF-8")) {
                i = 1;
             }
             break;
       }
       switch (i){
           case 0:
             str = "ZHS16GBK";
             break;
           case 1:
             str = "UTF8";
             break;
           default:
             str = "UTF8";
       }
       return str;
    }
    public static String getSysCharset(){
       String sys = System.getProperty("os.name").toLowerCase();
       String str = (sys.contains("windows"))? "GBK": "UTF-8";
       return str;
    }
}
