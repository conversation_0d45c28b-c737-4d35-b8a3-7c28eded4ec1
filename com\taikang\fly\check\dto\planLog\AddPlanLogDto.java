package com.taikang.fly.check.dto.planLog.AddPlanLogDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class AddPlanLogDto implements Serializable	// class@0001a6 from classes.dex
{
    private String id;
    private String planName;
    private static final long serialVersionUID = 0x1;

    public void AddPlanLogDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof AddPlanLogDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof AddPlanLogDto) {
             b = false;
          }else {
             AddPlanLogDto uAddPlanLogD = o;
             if (!uAddPlanLogD.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uAddPlanLogD.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String planName = this.getPlanName();
                String planName1 = uAddPlanLogD.getPlanName();
                if (planName == null) {
                   if (planName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!planName.equals(planName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getPlanName(){
       return this.planName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $planName = this.getPlanName();
       i1 = result * 59;
       if ($planName != null) {
          i = $planName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setPlanName(String planName){
       this.planName = planName;
    }
    public String toString(){
       return "AddPlanLogDto\(id="+this.getId()+", planName="+this.getPlanName()+"\)";
    }
}
