package org.springframework.boot.loader.archive.ExplodedArchive$FileEntryIterator$EntryComparator;
import java.util.Comparator;
import java.lang.Object;
import org.springframework.boot.loader.archive.ExplodedArchive$1;
import java.io.File;
import java.lang.String;

class ExplodedArchive$FileEntryIterator$EntryComparator implements Comparator	// class@00053e from classes.dex
{

    private void ExplodedArchive$FileEntryIterator$EntryComparator(){
       super();
    }
    void ExplodedArchive$FileEntryIterator$EntryComparator(ExplodedArchive$1 x0){
       super();
    }
    public int compare(File o1,File o2){
       return o1.getAbsolutePath().compareTo(o2.getAbsolutePath());
    }
    public int compare(Object p0,Object p1){
       return this.compare(p0, p1);
    }
}
