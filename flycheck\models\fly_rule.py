"""
飞行检查规则相关模型
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Text, DateTime, Integer, ForeignKey
from sqlalchemy.orm import relationship

from .base import BaseModel


class FlyRule(BaseModel):
    """飞行检查规则"""
    
    __tablename__ = "fly_rule"
    
    rule_name = Column(String(200), nullable=False, comment="规则名称")
    rule_describe = Column(Text, comment="规则描述")
    rule_logic = Column(Text, comment="规则逻辑")
    sql_name = Column(String(100), comment="SQL名称")
    rule_sql = Column(Text, comment="规则SQL")
    
    # 规则分类
    rule_category1 = Column(String(50), comment="规则分类1")
    rule_category2 = Column(String(50), comment="规则分类2")
    rule_classify = Column(String(50), comment="规则分类")
    rule_dimension = Column(String(50), comment="规则维度")
    rule_level = Column(String(20), comment="规则级别")
    rule_type = Column(String(20), comment="规则类型")
    
    # 政策相关
    policy_basis = Column(Text, comment="政策依据")
    problem_description = Column(Text, comment="问题描述")
    source_of_rule = Column(String(100), comment="规则来源")
    
    # 适用范围
    rule_scope_apply = Column(String(200), comment="规则适用范围")
    rule_suit_time_start = Column(DateTime, comment="规则适用开始时间")
    rule_suit_time_end = Column(DateTime, comment="规则适用结束时间")
    
    # 执行相关
    state = Column(String(10), default="1", comment="状态 1-启用 0-禁用")
    submit_state = Column(String(10), comment="提交状态")
    result_flag = Column(String(10), comment="结果标识")
    execution_date = Column(DateTime, comment="执行日期")
    
    # 其他属性
    data_sources = Column(String(100), comment="数据源")
    region = Column(String(50), comment="地区")
    diagnosis_type = Column(String(50), comment="诊断类型")
    false_positive_rate = Column(String(20), comment="误报率")
    feedback_status = Column(String(20), comment="反馈状态")
    is_special = Column(String(1), default="0", comment="是否特殊规则")
    
    # 扩展字段
    red_field1 = Column(String(200), comment="扩展字段1")
    red_field2 = Column(String(200), comment="扩展字段2")
    red_field3 = Column(String(200), comment="扩展字段3")
    ps = Column(Text, comment="备注")


class FlyRuleAudit(BaseModel):
    """飞行检查规则审核"""
    
    __tablename__ = "fly_rule_audit"
    
    rule_id = Column(String, ForeignKey("fly_rule.id"), nullable=False, comment="规则ID")
    audit_status = Column(String(20), nullable=False, comment="审核状态")
    audit_opinion = Column(Text, comment="审核意见")
    auditor = Column(String(50), comment="审核人")
    audit_time = Column(DateTime, comment="审核时间")
    
    # 关联关系
    rule = relationship("FlyRule", backref="audits")


class FlyRuleHistory(BaseModel):
    """飞行检查规则历史"""
    
    __tablename__ = "fly_rule_history"
    
    rule_id = Column(String, ForeignKey("fly_rule.id"), nullable=False, comment="规则ID")
    version = Column(String(20), nullable=False, comment="版本号")
    change_type = Column(String(20), nullable=False, comment="变更类型")
    change_content = Column(Text, comment="变更内容")
    change_reason = Column(Text, comment="变更原因")
    
    # 历史数据快照
    rule_snapshot = Column(Text, comment="规则快照(JSON)")
    
    # 关联关系
    rule = relationship("FlyRule", backref="histories")
