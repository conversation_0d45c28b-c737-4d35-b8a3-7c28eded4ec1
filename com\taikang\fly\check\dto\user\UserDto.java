package com.taikang.fly.check.dto.user.UserDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.dto.DatasourceInfo;
import java.lang.StringBuilder;

public class UserDto implements Serializable	// class@0001c1 from classes.dex
{
    private String createTime;
    private String creator;
    private DatasourceInfo datasourceInfo;
    private String id;
    private String modby;
    private String modifyTime;
    private String name;
    private String region;
    private List roleCode;
    private String status;
    private String userCode;
    private static final long serialVersionUID = 0x1;

    public void UserDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof UserDto){
          b = false;
       }else {
          UserDto userDto = o;
          if (!userDto.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = userDto.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String userCode = this.getUserCode();
             String userCode1 = userDto.getUserCode();
             if (userCode == null) {
                if (userCode1 != null) {
                   b = false;
                }
             }else if(userCode.equals(userCode1)){
             }
             String name = this.getName();
             String name1 = userDto.getName();
             if (name == null) {
                if (name1 != null) {
                   b = false;
                }
             }else if(name.equals(name1)){
             }
             String status = this.getStatus();
             String status1 = userDto.getStatus();
             if (status == null) {
                if (status1 != null) {
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             String creator = this.getCreator();
             String creator1 = userDto.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = userDto.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_00a1 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = userDto.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = userDto.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00cf :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String region = this.getRegion();
             String region1 = userDto.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             List roleCode = this.getRoleCode();
             List roleCode1 = userDto.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                label_00ff :
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             DatasourceInfo datasourceIn = this.getDatasourceInfo();
             DatasourceInfo datasourceIn1 = userDto.getDatasourceInfo();
             if (datasourceIn == null) {
                if (datasourceIn1 != null) {
                   b = false;
                }
             }else if(datasourceIn.equals(datasourceIn1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public DatasourceInfo getDatasourceInfo(){
       return this.datasourceInfo;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getName(){
       return this.name;
    }
    public String getRegion(){
       return this.region;
    }
    public List getRoleCode(){
       return this.roleCode;
    }
    public String getStatus(){
       return this.status;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $userCode = this.getUserCode();
       int i1 = result * 59;
       i = ($userCode == null)? 43: $userCode.hashCode();
       result = i1 + i;
       String $name = this.getName();
       i1 = result * 59;
       i = ($name == null)? 43: $name.hashCode();
       result = i1 + i;
       String $status = this.getStatus();
       i1 = result * 59;
       i = ($status == null)? 43: $status.hashCode();
       result = i1 + i;
       String $creator = this.getCreator();
       i1 = result * 59;
       i = ($creator == null)? 43: $creator.hashCode();
       result = i1 + i;
       String createTime = this.getCreateTime();
       i1 = result * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String region = this.getRegion();
       i1 = (i1 + i) * 59;
       i = (region == null)? 43: region.hashCode();
       List roleCode = this.getRoleCode();
       i1 = (i1 + i) * 59;
       i = (roleCode == null)? 43: roleCode.hashCode();
       DatasourceInfo datasourceIn = this.getDatasourceInfo();
       i1 = (i1 + i) * 59;
       i = (datasourceIn == null)? 43: datasourceIn.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDatasourceInfo(DatasourceInfo datasourceInfo){
       this.datasourceInfo = datasourceInfo;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRoleCode(List roleCode){
       this.roleCode = roleCode;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserDto\(id="+this.getId()+", userCode="+this.getUserCode()+", name="+this.getName()+", status="+this.getStatus()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", region="+this.getRegion()+", roleCode="+this.getRoleCode()+", datasourceInfo="+this.getDatasourceInfo()+"\)";
    }
}
