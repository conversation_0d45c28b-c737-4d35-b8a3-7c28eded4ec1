package com.taikang.fly.check.mybatis.dao.ClickHouseTableMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.ClickHouseTable;

public interface abstract ClickHouseTableMapper implements BaseMapper	// class@0001d8 from classes.dex
{

    void deleteData(String p0,List p1);
    void insertClickHouseTable(ClickHouseTable p0);
    ClickHouseTable queryClickHouseTableField(String p0);
    List queryClickHouseTableList(String p0);
    List queryClickHouseTableNameList(String p0);
}
