# FlyCheck Python 使用指南

## 系统概述

FlyCheck Python是基于原Java版本重写的医保飞行检查系统，保持了核心功能不变，采用了现代化的Python技术栈。

## 核心概念

### 1. 飞行检查规则 (FlyRule)
飞行检查规则是系统的核心，用于定义各种医保合规性检查逻辑。

**规则类型：**
- **SQL规则**: 使用SQL语句进行数据查询和分析
- **Python规则**: 使用Python代码进行复杂的数据处理
- **混合规则**: 结合SQL查询和Python处理

**规则属性：**
- 规则名称、描述、分类
- 规则逻辑（SQL或Python代码）
- 政策依据、问题描述
- 适用范围、生效时间
- 执行状态、审核状态

### 2. 检查计划 (Plan)
检查计划用于组织和调度多个规则的执行。

**计划特性：**
- 包含多个规则的执行序列
- 支持定期执行和一次性执行
- 可配置执行参数和数据范围
- 实时监控执行状态和结果

### 3. 医保基础数据
系统管理各类医保基础数据目录：
- 药品目录 (YB_DRUG_CATALOGUE)
- 诊疗项目 (YB_DIAGNOSIS_TREATMENT)  
- 医用耗材 (YB_CONSUMABLES_LIST)
- 诊断编码 (YB_DIAGNOSTIC_ENCODING)
- 手术编码 (YB_OPERATIVE_ENCODING)

## 快速上手

### 1. 启动系统
```bash
# 一键启动（推荐）
python run.py

# 手动启动
uv run uvicorn flycheck.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 访问系统
- 主页: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 3. 测试系统
```bash
# 运行系统测试
python test_system.py
```

## 使用示例

### 创建SQL规则

```python
import requests

rule_data = {
    "rule_name": "药品费用异常检查",
    "rule_describe": "检查单次药品费用是否超过合理范围",
    "rule_sql": """
        SELECT 
            patient_id,
            drug_code,
            drug_name,
            total_fee,
            visit_date
        FROM medical_records 
        WHERE total_fee > 1000 
        AND drug_type = '西药'
        AND visit_date >= '2024-01-01'
    """,
    "rule_category1": "费用检查",
    "rule_category2": "药品费用",
    "rule_type": "sql",
    "rule_level": "高",
    "policy_basis": "医保基金监管相关规定"
}

response = requests.post(
    "http://localhost:8000/api/v1/rules/",
    json=rule_data
)
rule = response.json()
print(f"规则创建成功: {rule['id']}")
```

### 创建Python规则

```python
rule_data = {
    "rule_name": "重复用药分析",
    "rule_describe": "分析患者重复用药情况",
    "rule_logic": """
import pandas as pd
from datetime import datetime, timedelta

# 获取最近30天的用药数据
end_date = datetime.now()
start_date = end_date - timedelta(days=30)

sql = f'''
    SELECT patient_id, drug_code, drug_name, visit_date, quantity
    FROM medical_records 
    WHERE visit_date BETWEEN '{start_date.date()}' AND '{end_date.date()}'
'''

df = pd.read_sql(sql, duckdb_conn)

# 分析重复用药
duplicate_drugs = df.groupby(['patient_id', 'drug_code']).agg({
    'visit_date': 'count',
    'quantity': 'sum',
    'drug_name': 'first'
}).reset_index()

# 筛选重复用药记录（同一药品超过3次）
result = duplicate_drugs[duplicate_drugs['visit_date'] > 3]
result = result.rename(columns={'visit_date': 'prescription_count'})
    """,
    "rule_category1": "合理用药",
    "rule_type": "python",
    "rule_level": "中"
}
```

### 执行规则

```python
# 执行单个规则
rule_id = "your-rule-id"
params = {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
}

response = requests.post(
    f"http://localhost:8000/api/v1/rules/{rule_id}/execute",
    json=params
)

result = response.json()
print(f"执行状态: {result['status']}")
print(f"检查记录数: {result['total_records']}")
print(f"异常记录数: {result['error_records']}")
```

### 创建检查计划

```python
plan_data = {
    "plan_name": "月度飞行检查计划",
    "plan_description": "每月定期执行的飞行检查",
    "plan_type": "定期检查",
    "data_source": "医保数据库",
    "data_range": "全市医疗机构",
    "rule_ids": ["rule_id_1", "rule_id_2", "rule_id_3"]
}

response = requests.post(
    "http://localhost:8000/api/v1/plans/",
    json=plan_data
)
plan = response.json()

# 激活计划
requests.post(f"http://localhost:8000/api/v1/plans/{plan['id']}/activate")

# 执行计划
requests.post(f"http://localhost:8000/api/v1/plans/{plan['id']}/execute")
```

### 导入医保数据

```python
# 导入药品目录
files = {'file': open('drug_catalogue.csv', 'rb')}
response = requests.post(
    "http://localhost:8000/api/v1/medical/drugs/import",
    files=files
)
print(response.json())
```

## 命令行工具

### 基本命令

```bash
# 查看帮助
uv run python -m flycheck.cli --help

# 启动服务
uv run python -m flycheck.cli serve --port 8000

# 查看系统信息
uv run python -m flycheck.cli info
```

### 数据库管理

```bash
# 初始化数据库
uv run python -m flycheck.cli db init

# 重置数据库
uv run python -m flycheck.cli db reset

# 创建示例数据
uv run python -m flycheck.cli db seed
```

### 规则管理

```bash
# 列出所有规则
uv run python -m flycheck.cli rule list

# 验证规则
uv run python -m flycheck.cli rule validate <rule_id>
```

## 配置管理

### 环境变量配置

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

### 主要配置项

```bash
# 数据库配置
FLYCHECK_DATABASE_URL=duckdb:///./flycheck.db

# 服务配置
FLYCHECK_HOST=0.0.0.0
FLYCHECK_PORT=8000
FLYCHECK_DEBUG=true

# 规则引擎配置
FLYCHECK_RULE_TIMEOUT=300
FLYCHECK_MAX_CONCURRENT_RULES=10

# 文件上传配置
FLYCHECK_UPLOAD_DIR=./uploads
FLYCHECK_MAX_FILE_SIZE=104857600
```

## 最佳实践

### 1. 规则编写建议

**SQL规则：**
- 使用参数化查询避免SQL注入
- 添加适当的索引提高查询性能
- 使用LIMIT限制结果集大小
- 添加时间范围过滤条件

**Python规则：**
- 使用pandas进行数据处理
- 避免在循环中执行数据库查询
- 合理使用内存，处理大数据集时分批处理
- 添加异常处理和日志记录

### 2. 性能优化

**数据库优化：**
- 为常用查询字段创建索引
- 使用DuckDB的列式存储优势
- 合理设置批处理大小

**规则执行优化：**
- 并发执行独立的规则
- 缓存常用的查询结果
- 定期清理历史执行日志

### 3. 监控和维护

**日志监控：**
```bash
# 查看应用日志
tail -f logs/flycheck.log

# 查看错误日志
grep ERROR logs/flycheck.log
```

**性能监控：**
- 监控规则执行时间
- 监控数据库查询性能
- 监控系统资源使用情况

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 检查Python环境和依赖
   - 查看错误日志

2. **规则执行超时**
   - 增加超时时间配置
   - 优化SQL查询性能
   - 减少数据处理量

3. **数据导入失败**
   - 检查文件格式和编码
   - 验证数据字段完整性
   - 查看详细错误信息

### 获取帮助

- 查看API文档: http://localhost:8000/docs
- 查看系统日志: `logs/flycheck.log`
- 运行系统测试: `python test_system.py`
