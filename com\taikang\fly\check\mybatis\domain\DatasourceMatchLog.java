package com.taikang.fly.check.mybatis.domain.DatasourceMatchLog;
import java.lang.Object;
import java.util.Date;
import java.lang.String;

public class DatasourceMatchLog	// class@000239 from classes.dex
{
    private Date createTime;
    private String creator;
    private String id;
    private String oraUserName;
    private String recordType;
    private String sqlContent;
    private String tableName;

    public void DatasourceMatchLog(){
       super();
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public String getRecordType(){
       return this.recordType;
    }
    public String getSqlContent(){
       return this.sqlContent;
    }
    public String getTableName(){
       return this.tableName;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public void setRecordType(String recordType){
       this.recordType = recordType;
    }
    public void setSqlContent(String sqlContent){
       this.sqlContent = sqlContent;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
}
