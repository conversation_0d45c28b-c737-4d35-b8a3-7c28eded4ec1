package com.taikang.fly.check.dto.hospitaldatapool.DataSummaryDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DataSummaryDto implements Serializable	// class@00012c from classes.dex
{
    private String avgDays;
    private String avgMedicalSummary;
    private String avgOwnSummary;
    private String avgSummary;
    private String medicalFundProportion;
    private String medicalPersonProportion;
    private String medicalTotalRevenue;
    private String mxCount;
    private String personTimes;
    private String zdCount;
    private static final long serialVersionUID = 0x1;

    public void DataSummaryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataSummaryDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DataSummaryDto){
          b = false;
       }else {
          DataSummaryDto uDataSummary = o;
          if (!uDataSummary.canEqual(this)) {
             b = false;
          }else {
             String personTimes = this.getPersonTimes();
             String personTimes1 = uDataSummary.getPersonTimes();
             if (personTimes == null) {
                if (personTimes1 != null) {
                   b = false;
                }
             }else if(personTimes.equals(personTimes1)){
             }
             String medicalTotal = this.getMedicalTotalRevenue();
             String medicalTotal1 = uDataSummary.getMedicalTotalRevenue();
             if (medicalTotal == null) {
                if (medicalTotal1 != null) {
                   b = false;
                }
             }else if(medicalTotal.equals(medicalTotal1)){
             }
             String medicalFundP = this.getMedicalFundProportion();
             String medicalFundP1 = uDataSummary.getMedicalFundProportion();
             if (medicalFundP == null) {
                if (medicalFundP1 != null) {
                   b = false;
                }
             }else if(medicalFundP.equals(medicalFundP1)){
             }
             String zdCount = this.getZdCount();
             String zdCount1 = uDataSummary.getZdCount();
             if (zdCount == null) {
                if (zdCount1 != null) {
                   b = false;
                }
             }else if(zdCount.equals(zdCount1)){
             }
             String mxCount = this.getMxCount();
             String mxCount1 = uDataSummary.getMxCount();
             if (mxCount == null) {
                if (mxCount1 != null) {
                   b = false;
                }
             }else if(mxCount.equals(mxCount1)){
             }
             String medicalPerso = this.getMedicalPersonProportion();
             String medicalPerso1 = uDataSummary.getMedicalPersonProportion();
             if (medicalPerso == null) {
                if (medicalPerso1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(medicalPerso.equals(medicalPerso1)){
             }
             String avgSummary = this.getAvgSummary();
             String avgSummary1 = uDataSummary.getAvgSummary();
             if (avgSummary == null) {
                if (avgSummary1 != null) {
                   b = false;
                }
             }else if(avgSummary.equals(avgSummary1)){
             }
             String avgMedicalSu = this.getAvgMedicalSummary();
             String avgMedicalSu1 = uDataSummary.getAvgMedicalSummary();
             if (avgMedicalSu == null) {
                if (avgMedicalSu1 != null) {
                label_00d3 :
                   b = false;
                }
             }else if(avgMedicalSu.equals(avgMedicalSu1)){
             }
             String avgOwnSummar = this.getAvgOwnSummary();
             String avgOwnSummar1 = uDataSummary.getAvgOwnSummary();
             if (avgOwnSummar == null) {
                if (avgOwnSummar1 != null) {
                   b = false;
                }
             }else if(avgOwnSummar.equals(avgOwnSummar1)){
             }
             String avgDays = this.getAvgDays();
             String avgDays1 = uDataSummary.getAvgDays();
             if (avgDays == null) {
                if (avgDays1 != null) {
                   b = false;
                }
             }else if(avgDays.equals(avgDays1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAvgDays(){
       return this.avgDays;
    }
    public String getAvgMedicalSummary(){
       return this.avgMedicalSummary;
    }
    public String getAvgOwnSummary(){
       return this.avgOwnSummary;
    }
    public String getAvgSummary(){
       return this.avgSummary;
    }
    public String getMedicalFundProportion(){
       return this.medicalFundProportion;
    }
    public String getMedicalPersonProportion(){
       return this.medicalPersonProportion;
    }
    public String getMedicalTotalRevenue(){
       return this.medicalTotalRevenue;
    }
    public String getMxCount(){
       return this.mxCount;
    }
    public String getPersonTimes(){
       return this.personTimes;
    }
    public String getZdCount(){
       return this.zdCount;
    }
    public int hashCode(){
       String $personTimes;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($personTimes = this.getPersonTimes()) == null)? i: $personTimes.hashCode();
       result = i1 + 59;
       String $medicalTotalRevenue = this.getMedicalTotalRevenue();
       int i2 = result * 59;
       i1 = ($medicalTotalRevenue == null)? i: $medicalTotalRevenue.hashCode();
       result = i2 + i1;
       String $medicalFundProportion = this.getMedicalFundProportion();
       i2 = result * 59;
       i1 = ($medicalFundProportion == null)? i: $medicalFundProportion.hashCode();
       result = i2 + i1;
       String $zdCount = this.getZdCount();
       i2 = result * 59;
       i1 = ($zdCount == null)? i: $zdCount.hashCode();
       result = i2 + i1;
       String $mxCount = this.getMxCount();
       i2 = result * 59;
       i1 = ($mxCount == null)? i: $mxCount.hashCode();
       result = i2 + i1;
       String medicalPerso = this.getMedicalPersonProportion();
       i2 = result * 59;
       i1 = (medicalPerso == null)? i: medicalPerso.hashCode();
       String avgSummary = this.getAvgSummary();
       i2 = (i2 + i1) * 59;
       i1 = (avgSummary == null)? i: avgSummary.hashCode();
       String avgMedicalSu = this.getAvgMedicalSummary();
       i2 = (i2 + i1) * 59;
       i1 = (avgMedicalSu == null)? i: avgMedicalSu.hashCode();
       String avgOwnSummar = this.getAvgOwnSummary();
       i2 = (i2 + i1) * 59;
       i1 = (avgOwnSummar == null)? i: avgOwnSummar.hashCode();
       String avgDays = this.getAvgDays();
       i1 = (i2 + i1) * 59;
       if (avgDays != null) {
          i = avgDays.hashCode();
       }
       return (i1 + i);
    }
    public void setAvgDays(String avgDays){
       this.avgDays = avgDays;
    }
    public void setAvgMedicalSummary(String avgMedicalSummary){
       this.avgMedicalSummary = avgMedicalSummary;
    }
    public void setAvgOwnSummary(String avgOwnSummary){
       this.avgOwnSummary = avgOwnSummary;
    }
    public void setAvgSummary(String avgSummary){
       this.avgSummary = avgSummary;
    }
    public void setMedicalFundProportion(String medicalFundProportion){
       this.medicalFundProportion = medicalFundProportion;
    }
    public void setMedicalPersonProportion(String medicalPersonProportion){
       this.medicalPersonProportion = medicalPersonProportion;
    }
    public void setMedicalTotalRevenue(String medicalTotalRevenue){
       this.medicalTotalRevenue = medicalTotalRevenue;
    }
    public void setMxCount(String mxCount){
       this.mxCount = mxCount;
    }
    public void setPersonTimes(String personTimes){
       this.personTimes = personTimes;
    }
    public void setZdCount(String zdCount){
       this.zdCount = zdCount;
    }
    public String toString(){
       return "DataSummaryDto\(personTimes="+this.getPersonTimes()+", medicalTotalRevenue="+this.getMedicalTotalRevenue()+", medicalFundProportion="+this.getMedicalFundProportion()+", zdCount="+this.getZdCount()+", mxCount="+this.getMxCount()+", medicalPersonProportion="+this.getMedicalPersonProportion()+", avgSummary="+this.getAvgSummary()+", avgMedicalSummary="+this.getAvgMedicalSummary()+", avgOwnSummary="+this.getAvgOwnSummary()+", avgDays="+this.getAvgDays()+"\)";
    }
}
