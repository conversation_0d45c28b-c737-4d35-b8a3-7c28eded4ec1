"""
规则引擎核心模块
"""

import json
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import pandas as pd
from loguru import logger
from sqlalchemy.orm import Session

from flycheck.database import get_duckdb_connection, db_manager
from flycheck.models import FlyRule, PlanLog
from flycheck.config import get_settings

settings = get_settings()


class RuleType(Enum):
    """规则类型"""
    SQL = "sql"
    PYTHON = "python"
    HYBRID = "hybrid"


class RuleStatus(Enum):
    """规则执行状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"
    TIMEOUT = "timeout"


@dataclass
class RuleResult:
    """规则执行结果"""
    rule_id: str
    status: RuleStatus
    total_records: int = 0
    error_records: int = 0
    warning_records: int = 0
    execution_time: float = 0.0
    error_message: Optional[str] = None
    result_data: Optional[List[Dict]] = None
    result_file_path: Optional[str] = None


class RuleEngine:
    """规则引擎"""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.duckdb_conn = get_duckdb_connection()
    
    def execute_rule(self, rule: FlyRule, params: Dict[str, Any] = None) -> RuleResult:
        """执行单个规则"""
        logger.info(f"开始执行规则: {rule.rule_name} (ID: {rule.id})")
        
        start_time = datetime.now()
        result = RuleResult(rule_id=rule.id, status=RuleStatus.RUNNING)
        
        try:
            # 根据规则类型执行
            if rule.rule_type == RuleType.SQL.value:
                result = self._execute_sql_rule(rule, params or {})
            elif rule.rule_type == RuleType.PYTHON.value:
                result = self._execute_python_rule(rule, params or {})
            elif rule.rule_type == RuleType.HYBRID.value:
                result = self._execute_hybrid_rule(rule, params or {})
            else:
                # 默认按SQL规则处理
                result = self._execute_sql_rule(rule, params or {})
            
            result.status = RuleStatus.COMPLETED
            
        except Exception as e:
            logger.error(f"规则执行失败: {rule.rule_name}, 错误: {str(e)}")
            result.status = RuleStatus.ERROR
            result.error_message = str(e)
        
        finally:
            end_time = datetime.now()
            result.execution_time = (end_time - start_time).total_seconds()
            
            # 记录执行日志
            self._log_execution(rule, result, start_time, end_time)
        
        return result
    
    def _execute_sql_rule(self, rule: FlyRule, params: Dict[str, Any]) -> RuleResult:
        """执行SQL规则"""
        if not rule.rule_sql:
            raise ValueError("SQL规则缺少SQL语句")
        
        # 参数替换
        sql = self._replace_sql_params(rule.rule_sql, params)
        
        logger.debug(f"执行SQL: {sql}")
        
        # 执行查询
        df = pd.read_sql(sql, self.duckdb_conn)
        
        result = RuleResult(rule_id=rule.id, status=RuleStatus.RUNNING)
        result.total_records = len(df)
        
        # 分析结果
        if not df.empty:
            # 假设有问题的记录都是错误记录
            result.error_records = len(df)
            result.result_data = df.to_dict('records')
            
            # 保存结果到文件
            result_file = f"./results/rule_{rule.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(result_file, index=False, encoding='utf-8-sig')
            result.result_file_path = result_file
        
        return result
    
    def _execute_python_rule(self, rule: FlyRule, params: Dict[str, Any]) -> RuleResult:
        """执行Python规则"""
        if not rule.rule_logic:
            raise ValueError("Python规则缺少逻辑代码")
        
        # 准备执行环境
        exec_globals = {
            'pd': pd,
            'duckdb_conn': self.duckdb_conn,
            'params': params,
            'logger': logger,
        }
        
        exec_locals = {}
        
        # 执行Python代码
        exec(rule.rule_logic, exec_globals, exec_locals)
        
        # 获取结果
        if 'result' not in exec_locals:
            raise ValueError("Python规则必须返回result变量")
        
        python_result = exec_locals['result']
        
        result = RuleResult(rule_id=rule.id, status=RuleStatus.RUNNING)
        
        if isinstance(python_result, pd.DataFrame):
            result.total_records = len(python_result)
            result.error_records = len(python_result)
            result.result_data = python_result.to_dict('records')
        elif isinstance(python_result, dict):
            result.total_records = python_result.get('total_records', 0)
            result.error_records = python_result.get('error_records', 0)
            result.warning_records = python_result.get('warning_records', 0)
            result.result_data = python_result.get('data', [])
        
        return result
    
    def _execute_hybrid_rule(self, rule: FlyRule, params: Dict[str, Any]) -> RuleResult:
        """执行混合规则（SQL + Python）"""
        # 先执行SQL获取数据
        if rule.rule_sql:
            sql = self._replace_sql_params(rule.rule_sql, params)
            df = pd.read_sql(sql, self.duckdb_conn)
        else:
            df = pd.DataFrame()
        
        # 然后执行Python逻辑处理数据
        if rule.rule_logic:
            exec_globals = {
                'pd': pd,
                'df': df,
                'duckdb_conn': self.duckdb_conn,
                'params': params,
                'logger': logger,
            }
            
            exec_locals = {}
            exec(rule.rule_logic, exec_globals, exec_locals)
            
            if 'result' in exec_locals:
                df = exec_locals['result']
        
        result = RuleResult(rule_id=rule.id, status=RuleStatus.RUNNING)
        result.total_records = len(df)
        result.error_records = len(df)
        result.result_data = df.to_dict('records') if not df.empty else []
        
        return result
    
    def _replace_sql_params(self, sql: str, params: Dict[str, Any]) -> str:
        """替换SQL中的参数"""
        for key, value in params.items():
            placeholder = f"${{{key}}}"
            if isinstance(value, str):
                sql = sql.replace(placeholder, f"'{value}'")
            else:
                sql = sql.replace(placeholder, str(value))
        
        return sql
    
    def _log_execution(self, rule: FlyRule, result: RuleResult, start_time: datetime, end_time: datetime):
        """记录执行日志"""
        try:
            log_entry = PlanLog(
                rule_id=rule.id,
                execution_start=start_time,
                execution_end=end_time,
                execution_status=result.status.value,
                total_records=result.total_records,
                error_records=result.error_records,
                warning_records=result.warning_records,
                error_message=result.error_message,
                result_file_path=result.result_file_path,
                result_data=result.result_data[:100] if result.result_data else None  # 只保存前100条
            )
            
            self.db_session.add(log_entry)
            self.db_session.commit()
            
        except Exception as e:
            logger.error(f"记录执行日志失败: {str(e)}")
    
    def validate_rule(self, rule: FlyRule) -> Tuple[bool, str]:
        """验证规则"""
        try:
            if rule.rule_type == RuleType.SQL.value:
                if not rule.rule_sql:
                    return False, "SQL规则缺少SQL语句"
                
                # 简单的SQL语法检查
                sql_lower = rule.rule_sql.lower().strip()
                if not sql_lower.startswith('select'):
                    return False, "SQL规则必须以SELECT开头"
            
            elif rule.rule_type == RuleType.PYTHON.value:
                if not rule.rule_logic:
                    return False, "Python规则缺少逻辑代码"
                
                # 简单的Python语法检查
                try:
                    compile(rule.rule_logic, '<string>', 'exec')
                except SyntaxError as e:
                    return False, f"Python语法错误: {str(e)}"
            
            return True, "规则验证通过"
            
        except Exception as e:
            return False, f"规则验证失败: {str(e)}"
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'duckdb_conn'):
            self.duckdb_conn.close()
