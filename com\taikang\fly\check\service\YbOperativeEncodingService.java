package com.taikang.fly.check.service.YbOperativeEncodingService;
import java.lang.Object;
import com.taikang.fly.check.dto.operativeencoding.OperativeSearchDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.util.ObjectUtils;
import java.lang.String;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import java.lang.Integer;
import com.taikang.fly.check.comm.NativePage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.YbOperativeEncodingDao;
import com.taikang.fly.check.dto.mapstruct.OperativeEncodingMapping;

public class YbOperativeEncodingService	// class@000310 from classes.dex
{
    private OperativeEncodingMapping operativeEncodingMapping;
    private YbOperativeEncodingDao ybOperativeEncodingDao;

    public void YbOperativeEncodingService(){
       super();
    }
    private QueryWrapper getQueryWrapper(OperativeSearchDto operativeSearchDto){
       QueryWrapper queryWrapper = new QueryWrapper();
       if (!ObjectUtils.isEmpty(operativeSearchDto)) {
          if (StringUtils.isNotBlank(operativeSearchDto.getSurgeryCode())) {
             queryWrapper.eq("surgery_code", operativeSearchDto.getSurgeryCode());
          }
          if (StringUtils.isNotBlank(operativeSearchDto.getSurgeryName())) {
             queryWrapper.like("surgery_name", operativeSearchDto.getSurgeryName());
          }
       }
       return queryWrapper;
    }
    public NativePage queryList(Integer page,Integer size,OperativeSearchDto operativeSearchDto){
       QueryWrapper queryWrapper = this.getQueryWrapper(operativeSearchDto);
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List ybOperativeEncodings = this.ybOperativeEncodingDao.selectList(queryWrapper);
       List operativeEncodingDtos = this.operativeEncodingMapping.toYbDtoList(ybOperativeEncodings);
       NativePage pageDto = new NativePage(operativeEncodingDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
}
