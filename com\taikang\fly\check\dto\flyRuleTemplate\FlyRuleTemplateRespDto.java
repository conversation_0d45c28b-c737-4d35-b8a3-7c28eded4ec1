package com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDate;
import java.util.Date;
import java.lang.StringBuilder;

public class FlyRuleTemplateRespDto implements Serializable	// class@00011a from classes.dex
{
    private LocalDate createdTime;
    private String creater;
    private String diagnosisType;
    private String id;
    private String isSpecial;
    private LocalDate operateTime;
    private String operator;
    private String parameter;
    private String policyBasis;
    private String ps;
    private String removed;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleDimension;
    private String ruleLevel;
    private String ruleName;
    private Date ruleSuitTimeEnd;
    private Date ruleSuitTimeStart;
    private String ruleType;
    private String sqlExample;
    private String sqlTemplate;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleTemplateRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleTemplateRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleTemplateRespDto){
          b = false;
       }else {
          FlyRuleTemplateRespDto uFlyRuleTemp = o;
          if (!uFlyRuleTemp.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleTemp.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleTemp.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uFlyRuleTemp.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uFlyRuleTemp.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleTemp.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uFlyRuleTemp.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ps = this.getPs();
             String ps1 = uFlyRuleTemp.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_00c1 :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String sqlTemplate = this.getSqlTemplate();
             String sqlTemplate1 = uFlyRuleTemp.getSqlTemplate();
             if (sqlTemplate == null) {
                if (sqlTemplate1 != null) {
                   b = false;
                }
             }else if(sqlTemplate.equals(sqlTemplate1)){
             }
             String sqlExample = this.getSqlExample();
             String sqlExample1 = uFlyRuleTemp.getSqlExample();
             if (sqlExample == null) {
                if (sqlExample1 != null) {
                label_00f3 :
                   b = false;
                }
             }else if(sqlExample.equals(sqlExample1)){
             }
             String parameter = this.getParameter();
             String parameter1 = uFlyRuleTemp.getParameter();
             if (parameter == null) {
                if (parameter1 != null) {
                   b = false;
                }
             }else if(parameter.equals(parameter1)){
             }
             String creater = this.getCreater();
             String creater1 = uFlyRuleTemp.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                label_0125 :
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String operator = this.getOperator();
             String operator1 = uFlyRuleTemp.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             LocalDate operateTime = this.getOperateTime();
             LocalDate operateTime1 = uFlyRuleTemp.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             LocalDate createdTime = this.getCreatedTime();
             LocalDate createdTime1 = uFlyRuleTemp.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                label_016d :
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String id = this.getId();
             String id1 = uFlyRuleTemp.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String removed = this.getRemoved();
             String removed1 = uFlyRuleTemp.getRemoved();
             if (removed == null) {
                if (removed1 != null) {
                   b = false;
                }
             }else if(removed.equals(removed1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = uFlyRuleTemp.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_01b5 :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uFlyRuleTemp.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String isSpecial = this.getIsSpecial();
             String isSpecial1 = uFlyRuleTemp.getIsSpecial();
             if (isSpecial == null) {
                if (isSpecial1 != null) {
                label_01e7 :
                   b = false;
                }
             }else if(isSpecial.equals(isSpecial1)){
             }
             Date ruleSuitTime = this.getRuleSuitTimeStart();
             Date ruleSuitTime1 = uFlyRuleTemp.getRuleSuitTimeStart();
             if (ruleSuitTime == null) {
                if (ruleSuitTime1 != null) {
                   b = false;
                }
             }else if(ruleSuitTime.equals(ruleSuitTime1)){
             }
             Date ruleSuitTime2 = this.getRuleSuitTimeEnd();
             Date ruleSuitTime3 = uFlyRuleTemp.getRuleSuitTimeEnd();
             if (ruleSuitTime2 == null) {
                if (ruleSuitTime3 != null) {
                label_0219 :
                   b = false;
                }
             }else if(ruleSuitTime2.equals(ruleSuitTime3)){
             }
             String ruleDimensio = this.getRuleDimension();
             String ruleDimensio1 = uFlyRuleTemp.getRuleDimension();
             if (ruleDimensio == null) {
                if (ruleDimensio1 != null) {
                label_0233 :
                   b = false;
                }
             }else if(ruleDimensio.equals(ruleDimensio1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDate getCreatedTime(){
       return this.createdTime;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public String getIsSpecial(){
       return this.isSpecial;
    }
    public LocalDate getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getParameter(){
       return this.parameter;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleDimension(){
       return this.ruleDimension;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public Date getRuleSuitTimeEnd(){
       return this.ruleSuitTimeEnd;
    }
    public Date getRuleSuitTimeStart(){
       return this.ruleSuitTimeStart;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlExample(){
       return this.sqlExample;
    }
    public String getSqlTemplate(){
       return this.sqlTemplate;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $ruleLevel = this.getRuleLevel();
       int i1 = result * 59;
       i = ($ruleLevel == null)? 43: $ruleLevel.hashCode();
       result = i1 + i;
       String $ruleCategory1 = this.getRuleCategory1();
       i1 = result * 59;
       i = ($ruleCategory1 == null)? 43: $ruleCategory1.hashCode();
       result = i1 + i;
       String $ruleCategory2 = this.getRuleCategory2();
       i1 = result * 59;
       i = ($ruleCategory2 == null)? 43: $ruleCategory2.hashCode();
       result = i1 + i;
       String $diagnosisType = this.getDiagnosisType();
       i1 = result * 59;
       i = ($diagnosisType == null)? 43: $diagnosisType.hashCode();
       result = i1 + i;
       String ruleDescribe = this.getRuleDescribe();
       i1 = result * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String sqlTemplate = this.getSqlTemplate();
       i1 = (i1 + i) * 59;
       i = (sqlTemplate == null)? 43: sqlTemplate.hashCode();
       String sqlExample = this.getSqlExample();
       i1 = (i1 + i) * 59;
       i = (sqlExample == null)? 43: sqlExample.hashCode();
       String parameter = this.getParameter();
       i1 = (i1 + i) * 59;
       i = (parameter == null)? 43: parameter.hashCode();
       String creater = this.getCreater();
       i1 = (i1 + i) * 59;
       i = (creater == null)? 43: creater.hashCode();
       String operator = this.getOperator();
       i1 = (i1 + i) * 59;
       i = (operator == null)? 43: operator.hashCode();
       LocalDate operateTime = this.getOperateTime();
       i1 = (i1 + i) * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       LocalDate createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String id = this.getId();
       i1 = (i1 + i) * 59;
       i = (id == null)? 43: id.hashCode();
       String removed = this.getRemoved();
       i1 = (i1 + i) * 59;
       i = (removed == null)? 43: removed.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String ruleType = this.getRuleType();
       i1 = (i1 + i) * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String isSpecial = this.getIsSpecial();
       i1 = (i1 + i) * 59;
       i = (isSpecial == null)? 43: isSpecial.hashCode();
       Date ruleSuitTime = this.getRuleSuitTimeStart();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime == null)? 43: ruleSuitTime.hashCode();
       Date ruleSuitTime1 = this.getRuleSuitTimeEnd();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime1 == null)? 43: ruleSuitTime1.hashCode();
       String ruleDimensio = this.getRuleDimension();
       i1 = (i1 + i) * 59;
       i = (ruleDimensio == null)? 43: ruleDimensio.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(LocalDate createdTime){
       this.createdTime = createdTime;
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsSpecial(String isSpecial){
       this.isSpecial = isSpecial;
    }
    public void setOperateTime(LocalDate operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setParameter(String parameter){
       this.parameter = parameter;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRemoved(String removed){
       this.removed = removed;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleDimension(String ruleDimension){
       this.ruleDimension = ruleDimension;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleSuitTimeEnd(Date ruleSuitTimeEnd){
       this.ruleSuitTimeEnd = ruleSuitTimeEnd;
    }
    public void setRuleSuitTimeStart(Date ruleSuitTimeStart){
       this.ruleSuitTimeStart = ruleSuitTimeStart;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlExample(String sqlExample){
       this.sqlExample = sqlExample;
    }
    public void setSqlTemplate(String sqlTemplate){
       this.sqlTemplate = sqlTemplate;
    }
    public String toString(){
       return "FlyRuleTemplateRespDto\(ruleName="+this.getRuleName()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ps="+this.getPs()+", sqlTemplate="+this.getSqlTemplate()+", sqlExample="+this.getSqlExample()+", parameter="+this.getParameter()+", creater="+this.getCreater()+", operator="+this.getOperator()+", operateTime="+this.getOperateTime()+", createdTime="+this.getCreatedTime()+", id="+this.getId()+", removed="+this.getRemoved()+", policyBasis="+this.getPolicyBasis()+", ruleType="+this.getRuleType()+", isSpecial="+this.getIsSpecial()+", ruleSuitTimeStart="+this.getRuleSuitTimeStart()+", ruleSuitTimeEnd="+this.getRuleSuitTimeEnd()+", ruleDimension="+this.getRuleDimension()+"\)";
    }
}
