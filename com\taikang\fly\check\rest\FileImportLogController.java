package com.taikang.fly.check.rest.FileImportLogController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.service.FileImportLogService;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.FileImportLog;
import javax.servlet.http.HttpServletResponse;
import java.lang.StringBuilder;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.taikang.fly.check.utils.FileUtils;
import com.taikang.fly.check.comm.NativePage;

public class FileImportLogController	// class@00028d from classes.dex
{
    private String fileDataUpdataPath;
    private FileImportLogService fileImportLogService;
    private static final Logger log;

    static {
       FileImportLogController.log = LoggerFactory.getLogger(FileImportLogController.class);
    }
    public void FileImportLogController(){
       super();
    }
    public CommResponse addCsvFilesManual(String path){
       return CommResponse.success(Integer.valueOf(this.fileImportLogService.addCsvFilesManual(path)));
    }
    public CommResponse addFlyRule(FileImportLog fileImportLog){
       return CommResponse.success(Integer.valueOf(this.fileImportLogService.saveEntity(fileImportLog)));
    }
    public void downloadLog(String fileName,HttpServletResponse response){
       fileName = PinyinUtil.getFirstLetter(FileUtil.mainName(fileName), "")+".log";
       FileUtils.downloadFile(fileName, this.fileDataUpdataPath+fileName, response);
    }
    public CommResponse findByType(Integer pageNum,Integer pageSize,String fileType){
       return CommResponse.success(this.fileImportLogService.findByType(pageNum.intValue(), pageSize.intValue(), fileType.toLowerCase()));
    }
}
