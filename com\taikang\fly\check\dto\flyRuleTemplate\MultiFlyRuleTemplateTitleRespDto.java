package com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateTitleRespDto;
import java.io.Serializable;
import java.lang.String;
import java.lang.Object;
import java.lang.StringBuilder;

public class MultiFlyRuleTemplateTitleRespDto implements Serializable	// class@000124 from classes.dex
{
    private String label;
    private String prop;
    private static final long serialVersionUID = 0x1;

    public void MultiFlyRuleTemplateTitleRespDto(String label,String prop){
       super();
       this.label = label;
       this.prop = prop;
    }
    protected boolean canEqual(Object other){
       return other instanceof MultiFlyRuleTemplateTitleRespDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MultiFlyRuleTemplateTitleRespDto) {
             b = false;
          }else {
             MultiFlyRuleTemplateTitleRespDto multiFlyRule = o;
             if (!multiFlyRule.canEqual(this)) {
                b = false;
             }else {
                String label = this.getLabel();
                String label1 = multiFlyRule.getLabel();
                if (label == null) {
                   if (label1 != null) {
                      b = false;
                   }
                }else if(label.equals(label1)){
                }
                String prop = this.getProp();
                String prop1 = multiFlyRule.getProp();
                if (prop == null) {
                   if (prop1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!prop.equals(prop1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getLabel(){
       return this.label;
    }
    public String getProp(){
       return this.prop;
    }
    public int hashCode(){
       String $label;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($label = this.getLabel()) == null)? i: $label.hashCode();
       result = i1 + 59;
       String $prop = this.getProp();
       i1 = result * 59;
       if ($prop != null) {
          i = $prop.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setLabel(String label){
       this.label = label;
    }
    public void setProp(String prop){
       this.prop = prop;
    }
    public String toString(){
       return "MultiFlyRuleTemplateTitleRespDto\(label="+this.getLabel()+", prop="+this.getProp()+"\)";
    }
}
