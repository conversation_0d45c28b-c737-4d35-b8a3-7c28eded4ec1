package com.taikang.fly.check.dto.importData.ImportDataBo;
import java.lang.Object;
import java.lang.String;
import java.util.List;

public class ImportDataBo	// class@000132 from classes.dex
{
    private String dataFileName;
    private String dataFilePath;
    private String hospitalName;
    private String logFileName;
    private String sourceTableSpace;
    private String sourceUserName;
    private List tableList;
    private String targetUserName;
    private String type;

    public void ImportDataBo(){
       super();
    }
    public String getDataFileName(){
       return this.dataFileName;
    }
    public String getDataFilePath(){
       return this.dataFilePath;
    }
    public String getHospitalName(){
       return this.hospitalName;
    }
    public String getLogFileName(){
       return this.logFileName;
    }
    public String getSourceTableSpace(){
       return this.sourceTableSpace;
    }
    public String getSourceUserName(){
       return this.sourceUserName;
    }
    public List getTableList(){
       return this.tableList;
    }
    public String getTargetUserName(){
       return this.targetUserName;
    }
    public String getType(){
       return this.type;
    }
    public void setDataFileName(String dataFileName){
       this.dataFileName = dataFileName;
    }
    public void setDataFilePath(String dataFilePath){
       this.dataFilePath = dataFilePath;
    }
    public void setHospitalName(String hospitalName){
       this.hospitalName = hospitalName;
    }
    public void setLogFileName(String logFileName){
       this.logFileName = logFileName;
    }
    public void setSourceTableSpace(String sourceTableSpace){
       this.sourceTableSpace = sourceTableSpace;
    }
    public void setSourceUserName(String sourceUserName){
       this.sourceUserName = sourceUserName;
    }
    public void setTableList(List tableList){
       this.tableList = tableList;
    }
    public void setTargetUserName(String targetUserName){
       this.targetUserName = targetUserName;
    }
    public void setType(String type){
       this.type = type;
    }
}
