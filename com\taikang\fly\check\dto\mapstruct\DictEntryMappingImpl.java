package com.taikang.fly.check.dto.mapstruct.DictEntryMappingImpl;
import com.taikang.fly.check.dto.mapstruct.DictEntryMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.DictEntry;
import com.taikang.fly.check.dto.dictEntry.DictEntryDto;
import java.lang.String;
import java.lang.Integer;
import java.util.Date;
import java.text.SimpleDateFormat;
import com.taikang.fly.check.dto.csventry.CSVHeaderEntry;
import com.taikang.fly.check.dto.csventry.CsvConfigEntry;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;

public class DictEntryMappingImpl implements DictEntryMapping	// class@00014b from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void DictEntryMappingImpl(){
       super();
    }
    protected DictEntryDto dictEntryToDictEntryDto(DictEntry dictEntry){
       DictEntryDto uDictEntryDt;
       if (dictEntry == null) {
          uDictEntryDt = null;
       }else {
          uDictEntryDt = new DictEntryDto();
          uDictEntryDt.setId(dictEntry.getId());
          uDictEntryDt.setDictTypeCode(dictEntry.getDictTypeCode());
          uDictEntryDt.setDictCode(dictEntry.getDictCode());
          uDictEntryDt.setDictName(dictEntry.getDictName());
          if (dictEntry.getSortNo() != null) {
             uDictEntryDt.setSortNo(String.valueOf(dictEntry.getSortNo()));
          }
          uDictEntryDt.setCreator(dictEntry.getCreator());
          if (dictEntry.getCreateTime() != null) {
             uDictEntryDt.setCreateTime(new SimpleDateFormat().format(dictEntry.getCreateTime()));
          }
          uDictEntryDt.setModby(dictEntry.getModby());
          if (dictEntry.getModifyTime() != null) {
             uDictEntryDt.setModifyTime(new SimpleDateFormat().format(dictEntry.getModifyTime()));
          }
          uDictEntryDt.setSignature(dictEntry.getSignature());
       }
       return uDictEntryDt;
    }
    public CsvConfigEntry entryToConfig(CSVHeaderEntry csvHeaderEntry){
       CsvConfigEntry uCsvConfigEn;
       if (csvHeaderEntry == null) {
          uCsvConfigEn = null;
       }else {
          uCsvConfigEn = new CsvConfigEntry();
          uCsvConfigEn.setFieldName(csvHeaderEntry.getFieldName());
          uCsvConfigEn.setFieldType(csvHeaderEntry.getFieldType());
          uCsvConfigEn.setFieldLength(csvHeaderEntry.getFieldLength());
          uCsvConfigEn.setFieldAccurary(csvHeaderEntry.getFieldAccurary());
          uCsvConfigEn.setDateFormat(csvHeaderEntry.getDateFormat());
          uCsvConfigEn.setRemark(csvHeaderEntry.getRemark());
       }
       return uCsvConfigEn;
    }
    public DictEntry toDto(DictEntry dictEntry){
       DictEntry uDictEntry;
       if (dictEntry == null) {
          uDictEntry = null;
       }else {
          uDictEntry = new DictEntry();
          uDictEntry.setSortNo(dictEntry.getSortNo());
          uDictEntry.setModifyTime(dictEntry.getModifyTime());
          uDictEntry.setCreateTime(dictEntry.getCreateTime());
          uDictEntry.setId(dictEntry.getId());
          uDictEntry.setDictTypeCode(dictEntry.getDictTypeCode());
          uDictEntry.setDictCode(dictEntry.getDictCode());
          uDictEntry.setDictName(dictEntry.getDictName());
          uDictEntry.setCreator(dictEntry.getCreator());
          uDictEntry.setModby(dictEntry.getModby());
          uDictEntry.setSignature(dictEntry.getSignature());
          uDictEntry.setDictType(dictEntry.getDictType());
          uDictEntry.setDictNec(dictEntry.getDictNec());
       }
       return uDictEntry;
    }
    public List toDtoList(List dictEntryDtos){
       List list;
       if (dictEntryDtos == null) {
          list = null;
       }else {
          list = new ArrayList(dictEntryDtos.size());
          Iterator iterator = dictEntryDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.dictEntryToDictEntryDto(iterator.next()));
          }
       }
       return list;
    }
    public DictEntry toEntity(DictEntryDto dictEntryDto){
       DictEntry uDictEntry;
       if (dictEntryDto == null) {
          uDictEntry = null;
       }else {
          uDictEntry = new DictEntry();
          uDictEntry.setSortNo(this.typeConversionMapper.String2Integer(dictEntryDto.getSortNo()));
          uDictEntry.setModifyTime(this.typeConversionMapper.String2Time(dictEntryDto.getModifyTime()));
          uDictEntry.setCreateTime(this.typeConversionMapper.String2Time(dictEntryDto.getCreateTime()));
          uDictEntry.setId(dictEntryDto.getId());
          uDictEntry.setDictTypeCode(dictEntryDto.getDictTypeCode());
          uDictEntry.setDictCode(dictEntryDto.getDictCode());
          uDictEntry.setDictName(dictEntryDto.getDictName());
          uDictEntry.setCreator(dictEntryDto.getCreator());
          uDictEntry.setModby(dictEntryDto.getModby());
          uDictEntry.setSignature(dictEntryDto.getSignature());
       }
       return uDictEntry;
    }
    public List toEntityList(List dictEntryDtos){
       List list;
       if (dictEntryDtos == null) {
          list = null;
       }else {
          list = new ArrayList(dictEntryDtos.size());
          Iterator iterator = dictEntryDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toEntity(iterator.next()));
          }
       }
       return list;
    }
}
