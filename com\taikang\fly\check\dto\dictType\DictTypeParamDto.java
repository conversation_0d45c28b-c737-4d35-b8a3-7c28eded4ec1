package com.taikang.fly.check.dto.dictType.DictTypeParamDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DictTypeParamDto implements Serializable	// class@0000e8 from classes.dex
{
    private String dictTypeId;
    private String dictTypeName;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a1;

    public void DictTypeParamDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictTypeParamDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DictTypeParamDto) {
             b = false;
          }else {
             DictTypeParamDto uDictTypePar = o;
             if (!uDictTypePar.canEqual(this)) {
                b = false;
             }else {
                String dictTypeId = this.getDictTypeId();
                String dictTypeId1 = uDictTypePar.getDictTypeId();
                if (dictTypeId == null) {
                   if (dictTypeId1 != null) {
                      b = false;
                   }
                }else if(dictTypeId.equals(dictTypeId1)){
                }
                String dictTypeName = this.getDictTypeName();
                String dictTypeName1 = uDictTypePar.getDictTypeName();
                if (dictTypeName == null) {
                   if (dictTypeName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!dictTypeName.equals(dictTypeName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDictTypeId(){
       return this.dictTypeId;
    }
    public String getDictTypeName(){
       return this.dictTypeName;
    }
    public int hashCode(){
       String $dictTypeId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($dictTypeId = this.getDictTypeId()) == null)? i: $dictTypeId.hashCode();
       result = i1 + 59;
       String $dictTypeName = this.getDictTypeName();
       i1 = result * 59;
       if ($dictTypeName != null) {
          i = $dictTypeName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDictTypeId(String dictTypeId){
       this.dictTypeId = dictTypeId;
    }
    public void setDictTypeName(String dictTypeName){
       this.dictTypeName = dictTypeName;
    }
    public String toString(){
       return "DictTypeParamDto\(dictTypeId="+this.getDictTypeId()+", dictTypeName="+this.getDictTypeName()+"\)";
    }
}
