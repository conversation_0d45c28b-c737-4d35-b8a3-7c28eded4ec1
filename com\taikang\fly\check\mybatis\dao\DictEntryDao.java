package com.taikang.fly.check.mybatis.dao.DictEntryDao;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.Map;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.DictEntry;
import java.lang.Object;

public interface abstract DictEntryDao implements BaseMapper	// class@0001e3 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    void deleteEntityByTypeCode(String p0);
    List findAll(Map p0);
    DictEntry findByDictEntry(DictEntry p0);
    String findDictCode(Map p0);
    List findDictDetail(Map p0);
    List findDictDetails(Map p0);
    String findDictName(Map p0);
    String getStamp();
    int insert(DictEntry p0);
    default int insert(Object p0){
       return this.insert(p0);
    }
    int insertSelective(DictEntry p0);
    int insertSelectives(DictEntry p0);
    List queryDictCode(String p0);
    List queryDictCodeSta(String p0);
    List queryDictDetails(String p0);
    List queryDictName(String p0);
    DictEntry selectByPrimaryKey(String p0);
    DictEntry selectBySelective(Map p0);
    int updateByPrimaryKey(DictEntry p0);
    int updateByPrimaryKeySelective(DictEntry p0);
    void updateCurrentDataUser(String p0);
    void updateOraUserName(String p0);
}
