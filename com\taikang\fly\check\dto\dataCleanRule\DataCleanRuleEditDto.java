package com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleEditDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DataCleanRuleEditDto	// class@0000dd from classes.dex
{
    private String cleanRule;
    private String explain;
    private String fieldName;
    private String id;
    private String tableName;

    public void DataCleanRuleEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataCleanRuleEditDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DataCleanRuleEditDto) {
             b = false;
          }else {
             DataCleanRuleEditDto uDataCleanRu = o;
             if (!uDataCleanRu.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uDataCleanRu.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String tableName = this.getTableName();
                String tableName1 = uDataCleanRu.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String fieldName = this.getFieldName();
                String fieldName1 = uDataCleanRu.getFieldName();
                if (fieldName == null) {
                   if (fieldName1 != null) {
                      b = false;
                   }
                }else if(fieldName.equals(fieldName1)){
                }
                String cleanRule = this.getCleanRule();
                String cleanRule1 = uDataCleanRu.getCleanRule();
                if (cleanRule == null) {
                   if (cleanRule1 != null) {
                      b = false;
                   }
                }else if(cleanRule.equals(cleanRule1)){
                }
                String explain = this.getExplain();
                String explain1 = uDataCleanRu.getExplain();
                if (explain == null) {
                   if (explain1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!explain.equals(explain1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCleanRule(){
       return this.cleanRule;
    }
    public String getExplain(){
       return this.explain;
    }
    public String getFieldName(){
       return this.fieldName;
    }
    public String getId(){
       return this.id;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tableName = this.getTableName();
       int i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $fieldName = this.getFieldName();
       i2 = result * 59;
       i1 = ($fieldName == null)? i: $fieldName.hashCode();
       result = i2 + i1;
       String $cleanRule = this.getCleanRule();
       i2 = result * 59;
       i1 = ($cleanRule == null)? i: $cleanRule.hashCode();
       result = i2 + i1;
       String $explain = this.getExplain();
       i1 = result * 59;
       if ($explain != null) {
          i = $explain.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCleanRule(String cleanRule){
       this.cleanRule = cleanRule;
    }
    public void setExplain(String explain){
       this.explain = explain;
    }
    public void setFieldName(String fieldName){
       this.fieldName = fieldName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "DataCleanRuleEditDto\(id="+this.getId()+", tableName="+this.getTableName()+", fieldName="+this.getFieldName()+", cleanRule="+this.getCleanRule()+", explain="+this.getExplain()+"\)";
    }
}
