package com.taikang.fly.check.mybatis.domain.NationalStandardVersionDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.Class;

public class NationalStandardVersionDto implements Serializable	// class@000262 from classes.dex
{
    private String code;
    private String name;
    private static final long serialVersionUID = 0x1;

    public void NationalStandardVersionDto(){
       super();
    }
    public String getCode(){
       return this.code;
    }
    public String getName(){
       return this.name;
    }
    public void setCode(String code){
       String str = (code == null)? null: code.trim();
       this.code = str;
       return;
    }
    public void setName(String name){
       String str = (name == null)? null: name.trim();
       this.name = str;
       return;
    }
    public String toString(){
       StringBuilder sb = "";
       sb = sb+this.getClass().getSimpleName();
       sb = sb+" [";
       StringBuilder sb1 = sb+"Hash = ";
       sb1 = sb1+this.hashCode();
       sb1 = sb+", code=";
       sb1 = sb1+this.code;
       sb1 = sb+", name=";
       sb1 = sb1+this.name;
       sb1 = sb+", serialVersionUID=";
       sb1 = sb1+1;
       sb = sb+"]";
       return sb;
    }
}
