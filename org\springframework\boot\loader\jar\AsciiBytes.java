package org.springframework.boot.loader.jar.AsciiBytes;
import java.lang.String;
import java.nio.charset.StandardCharsets;
import java.nio.charset.Charset;
import java.lang.Object;
import java.lang.IndexOutOfBoundsException;
import java.lang.CharSequence;
import org.springframework.boot.loader.jar.StringSequence;
import java.lang.Class;

final class AsciiBytes	// class@000549 from classes.dex
{
    private final byte[] bytes;
    private int hash;
    private final int length;
    private final int offset;
    private String string;
    private static final String EMPTY_STRING = "";
    private static final int[] INITIAL_BYTE_BITMASK;
    private static final int SUBSEQUENT_BYTE_BITMASK;

    static {
       AsciiBytes.INITIAL_BYTE_BITMASK = new int[4]{127,31,15,7};
    }
    void AsciiBytes(String string){
       super(string.getBytes(StandardCharsets.UTF_8));
       this.string = string;
    }
    void AsciiBytes(byte[] bytes){
       super(bytes, 0, bytes.length);
    }
    void AsciiBytes(byte[] bytes,int offset,int length){
       super();
       if (offset < 0 || (length < 0 || ((offset + length)) > bytes.length)) {
          throw new IndexOutOfBoundsException();
       }
       this.bytes = bytes;
       this.offset = offset;
       this.length = length;
       return;
    }
    private char getChar(CharSequence name,char suffix,int index){
       if (index < name.length()) {
          suffix = name.charAt(index);
       }else if(index != name.length()){
          suffix = 0;
       }
       return suffix;
    }
    private int getNumberOfUtfBytes(int b){
       int i;
       int i1;
       if (!((b & 0x0080))) {
          i = 1;
       }else {
          i = 0;
          while (i1 = b & 0x0080) {
             b = b << 1;
             i = i + 1;
          }
       }
       return i;
    }
    public static int hashCode(int hash,char suffix){
       if (suffix) {
          hash = (hash * 31) + suffix;
       }
       return hash;
    }
    public static int hashCode(CharSequence charSequence){
       int i = (charSequence instanceof StringSequence)? charSequence.hashCode(): charSequence.toString().hashCode();
       return i;
    }
    static String toString(byte[] bytes){
       return new String(bytes, StandardCharsets.UTF_8);
    }
    public boolean endsWith(AsciiBytes postfix){
       boolean b = true;
       if (this != postfix) {
          if (postfix.length > this.length) {
             b = false;
          }else {
             int i = 0;
             while (i < postfix.length) {
                int i1 = this.length - 1;
                int i2 = this.offset + i1;
                i2 = i2 - i;
                int i3 = postfix.length - 1;
                i1 = postfix.offset + i3;
                i1 = i1 - i;
                if (this.bytes[i2] != postfix.bytes[i1]) {
                   b = false;
                   break ;
                }
                i = i + 1;
             }
          }
       }
       return b;
    }
    public boolean equals(Object obj){
       boolean b = false;
       if (obj != null) {
          if (this == obj) {
             b = true;
          }else if(obj.getClass() == AsciiBytes.class){
             AsciiBytes uAsciiBytes = obj;
             if (this.length == uAsciiBytes.length) {
                int i = 0;
                while (true) {
                   if (i < this.length) {
                      int i1 = this.offset + i;
                      int i2 = uAsciiBytes.offset + i;
                      if (this.bytes[i1] == uAsciiBytes.bytes[i2]) {
                         i = i + 1;
                      }
                   }else {
                      b = true;
                      break ;
                   }
                }
             }
          }
       }
       return b;
    }
    public int hashCode(){
       int hash;
       int i3;
       if ((hash = this.hash) == null && this.bytes.length > 0) {
          AsciiBytes toffset = this.offset;
          while (true) {
             int i = this.offset + this.length;
             if (toffset < i) {
                int b = this.bytes[toffset];
                int i1 = this.getNumberOfUtfBytes(b) - 1;
                b = b & AsciiBytes.INITIAL_BYTE_BITMASK[i1];
                for (int i2 = 0; i2 < i1; i2 = i2 + 1) {
                   i = b << 6;
                   toffset = toffset + 1;
                   i3 = this.bytes[toffset] & 0x3f;
                   b = i + i3;
                }
                if (b <= 0xffff) {
                   i = hash * 31;
                   hash = i + b;
                }else {
                   i = hash * 31;
                   i3 = b >> 10;
                   i3 = i3 + 0xd7c0;
                   hash = i + i3;
                   i = hash * 31;
                   i3 = b & 0x03ff;
                   i3 = i3 + 0xdc00;
                   hash = i + i3;
                }
                toffset = toffset + 1;
             }else {
                break ;
             }
          }
          this.hash = hash;
       }
       return hash;
    }
    public int length(){
       return this.length;
    }
    public boolean matches(CharSequence name,char suffix){
       int ix = 1;
       boolean b = false;
       int charIndex = 0;
       int nameLen = name.length();
       int ix1 = (suffix)? ix: b;
       int totalLen = nameLen + ix1;
       int i = this.offset;
       while (true) {
          ix1 = this.offset + this.length;
          if (i < ix1) {
             int b1 = this.bytes[i];
             int ix2 = this.getNumberOfUtfBytes(b1) - 1;
             b1 = b1 & AsciiBytes.INITIAL_BYTE_BITMASK[ix2];
             for (int ix3 = 0; ix3 < ix2; ix3 = ix3 + 1) {
                ix1 = b1 << 6;
                i++;
                int ix4 = this.bytes[i] & 0x3f;
                b1 = ix1 + ix4;
             }
             int ix5 = charIndex + 1;
             char charx = this.getChar(name, suffix, charIndex);
             if (b1 <= 0xffff) {
                if (charx != b1) {
                   charIndex = ix5;
                }else {
                   charIndex = ix5;
                }
             }else {
                ix1 = b1 >> 10;
                ix1 = ix1 + 0xd7c0;
                if (charx != ix1) {
                   charIndex = ix5;
                }else {
                   charIndex = ix5 + 1;
                   ix1 = b1 & 0x03ff;
                   ix1 = ix1 + 0xdc00;
                   if (this.getChar(name, suffix, ix5) != ix1) {
                   }
                }
             }
             i++;
          }else if(charIndex == totalLen){
             ix = b;
          }
          b = ix;
          break ;
       }
       return b;
    }
    public boolean startsWith(AsciiBytes prefix){
       boolean b = true;
       if (this != prefix) {
          if (prefix.length > this.length) {
             b = false;
          }else {
             int i = 0;
             while (i < prefix.length) {
                int i1 = this.offset + i;
                int i2 = prefix.offset + i;
                if (this.bytes[i1] != prefix.bytes[i2]) {
                   b = false;
                   break ;
                }
                i = i + 1;
             }
          }
       }
       return b;
    }
    public AsciiBytes substring(int beginIndex){
       return this.substring(beginIndex, this.length);
    }
    public AsciiBytes substring(int beginIndex,int endIndex){
       int length = endIndex - beginIndex;
       if (((this.offset + length)) > this.bytes.length) {
          throw new IndexOutOfBoundsException();
       }
       return new AsciiBytes(this.bytes, (this.offset + beginIndex), length);
    }
    public String toString(){
       if (this.string == null) {
          this.string = (this.length == null)? "": new String(this.bytes, this.offset, this.length, StandardCharsets.UTF_8);
       }
       return this.string;
    }
}
