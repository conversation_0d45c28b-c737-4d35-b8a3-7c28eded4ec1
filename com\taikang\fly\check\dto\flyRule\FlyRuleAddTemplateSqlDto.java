package com.taikang.fly.check.dto.flyRule.FlyRuleAddTemplateSqlDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class FlyRuleAddTemplateSqlDto implements Serializable	// class@0000f7 from classes.dex
{
    private List args;
    private String diagnosisType;
    private String ruleName;
    private String sqlTemplate;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleAddTemplateSqlDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleAddTemplateSqlDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleAddTemplateSqlDto) {
             b = false;
          }else {
             FlyRuleAddTemplateSqlDto uFlyRuleAddT = o;
             if (!uFlyRuleAddT.canEqual(this)) {
                b = false;
             }else {
                String ruleName = this.getRuleName();
                String ruleName1 = uFlyRuleAddT.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String sqlTemplate = this.getSqlTemplate();
                String sqlTemplate1 = uFlyRuleAddT.getSqlTemplate();
                if (sqlTemplate == null) {
                   if (sqlTemplate1 != null) {
                      b = false;
                   }
                }else if(sqlTemplate.equals(sqlTemplate1)){
                }
                String diagnosisTyp = this.getDiagnosisType();
                String diagnosisTyp1 = uFlyRuleAddT.getDiagnosisType();
                if (diagnosisTyp == null) {
                   if (diagnosisTyp1 != null) {
                      b = false;
                   }
                }else if(diagnosisTyp.equals(diagnosisTyp1)){
                }
                List args = this.getArgs();
                List args1 = uFlyRuleAddT.getArgs();
                if (args == null) {
                   if (args1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!args.equals(args1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getArgs(){
       return this.args;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getSqlTemplate(){
       return this.sqlTemplate;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $sqlTemplate = this.getSqlTemplate();
       int i2 = result * 59;
       i1 = ($sqlTemplate == null)? i: $sqlTemplate.hashCode();
       result = i2 + i1;
       String $diagnosisType = this.getDiagnosisType();
       i2 = result * 59;
       i1 = ($diagnosisType == null)? i: $diagnosisType.hashCode();
       result = i2 + i1;
       List $args = this.getArgs();
       i1 = result * 59;
       if ($args != null) {
          i = $args.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setArgs(List args){
       this.args = args;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setSqlTemplate(String sqlTemplate){
       this.sqlTemplate = sqlTemplate;
    }
    public String toString(){
       return "FlyRuleAddTemplateSqlDto\(ruleName="+this.getRuleName()+", sqlTemplate="+this.getSqlTemplate()+", diagnosisType="+this.getDiagnosisType()+", args="+this.getArgs()+"\)";
    }
}
