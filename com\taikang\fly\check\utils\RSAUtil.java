package com.taikang.fly.check.utils.RSAUtil;
import java.lang.Class;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.lang.Object;
import java.lang.String;
import java.util.Base64$Decoder;
import java.util.Base64;
import java.util.Base64$Encoder;
import org.apache.commons.codec.binary.Base64;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.KeyFactory;
import java.security.spec.KeySpec;
import java.security.PrivateKey;
import javax.crypto.Cipher;
import java.security.Key;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;

public class RSAUtil	// class@000347 from classes.dex
{
    private static final String KEYALGORITHM = "RSA";
    private static final String PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCPDorteL8smR0S984d9TEHTrUyUl2ph5KHRLg1mNdJCo2zyb0jeZmQjq0J78TAWqWrUROqGpIM+E9xe9+A1q6R4TLlTWhI6OnCrqzqNPsWJweNvru3W1RpGze/2bFzxIQDszF7jomBpQB6KOzEpUHDcmZDez/ODyQFth4GCj1KXhwuDyxm+f1DZs94dCgjOZM3117eNdddNuThZ8lAPF+7jsFHE/cH48QfnPfjIZk0v+2hUN+mZyRdJb8RwcApeEE5RiezMRlY2YpfDM72qIPavKojsDRig67FHgRe6Nu/97QkL1aq0Egn6uNB5Vh0BDXDtOHmoxXz83tZu8jJCFIDAgMBAAECggEANlgP7qYv+YEwykPYyQib12rO6o7fhcc6oqg4UiP9pIGd0u6QKynGbGPG2D99PpUw2iOylKwDT2oEw9eK0Ca9qi0iaHk/ZHMWWh6BEXeO6ptpAy+xn7XSGxt70iWgg5njjIRGB+nd0qIbzEw/pXQhx11t+7SWSHD3/jNwtFqvbdA/02yePERucq9jStLz+2l6F6GTsB7zhfdbsmQuWSByYha9zug3o911zGWbb7Nqp6l4mmaid3QUpVrAHi8Nf16bxNsQsEGf30LOgJhL8FGGRyk1cdQRBmRYJ8GTgMkSg4fpHaBrthxYwIYQHg4g2U/BuYaxRR6h+hZS8+E1H848AQKBgQDZOj+MKOBjQlpP0E4/KUlUYE7EsDmNpddUwNMutVZu7xmR/6+GZIEeTD8VA6kLBSgOVpzLO7/VizKKatO8olZq3SzJYiL5X/uSQsXa8XF5sz92wfsRdmvnIm/Xd350MmsvuU6wfgFQI8pq7fvLrBkaLddV6nBw9zhwN8+hN4oheQKBgQColzhpMzbG2JkbBwOzhc9y0fv7ZWcw/LDSKnQFLpzF/wmWNoyEQcUZOG6WUILQYBXlLMSQfNky6yDjHuBnFNPnGwqrMpgo2Ov6DL3wpCvy3fOxdVpKItMGB8NIhRC0a7bVorfARGrI/ouRn0hTx2sopuOwDx5/4StxsNn229LMWwKBgCc2In50anzAX3kEyvsPwtLeR5mtjdYJ16LO49AMn7uKoXz/QHnr7nq/nKt4a603kxFzhP4SnQA5Djh6LBpi22KS5iC7/EAzmT+/meN5rdggCRaw0VdHnxar2rB0O3lSGJMWyiOFu0ryCvneV5NeT3GwUKyLkxxxI9jt2ibp1Z9RAoGAVnhpeJvKBqdlsBUi8ts5Eb1eLw1NpjSVRNZ9d4L26h7rfd4wqE1LmKn3HqXg0pvV3xvdHfbLOHCdB6D4nCZ0oV6fVDu5C4pnTnNmoJyz5fetXGHCk6/U6IzmvrVxYfi1aFHzSWxOERVt5Jz77eykq88djVnT3OdS7xc0VIdfUW0CgYEAlBNWTln5sxCmjxpKJ2JqcOhgmB4C1T3m0QW8D7I01B0Dficn0vAYAZfzFvS8h+ZLC3ijryWVlR2e9MrRLw4HmpFSRkB6YqrmKlvXcdi9gnXqkt4oYEFeXxV0wVH/NuhBEM7AxTJ6fMbhaf7cX94dWAMEfFkork/e9O760qaRS80=";
    private static final String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjw6K7Xi/LJkdEvfOHfUxB061MlJdqYeSh0S4NZjXSQqNs8m9I3mZkI6tCe/EwFqlq1ETqhqSDPhPcXvfgNaukeEy5U1oSOjpwq6s6jT7FicHjb67t1tUaRs3v9mxc8SEA7Mxe46JgaUAeijsxKVBw3JmQ3s/zg8kBbYeBgo9Sl4cLg8sZvn9Q2bPeHQoIzmTN9de3jXXXTbk4WfJQDxfu47BRxP3B+PEH5z34yGZNL/toVDfpmckXSW/EcHAKXhBOUYnszEZWNmKXwzO9qiD2ryqI7A0YoOuxR4EXujbv/e0JC9WqtBIJ+rjQeVYdAQ1w7Th5qMV8/N7WbvIyQhSAwIDAQAB";
    private static Log log;

    static {
       RSAUtil.log = LogFactory.getLog(RSAUtil.class);
    }
    public void RSAUtil(){
       super();
    }
    public static byte[] base642Byte(String base64Key){
       Base64$Decoder decoder = Base64.getDecoder();
       return decoder.decode(base64Key);
    }
    public static String byte2Base64(byte[] bytes){
       Base64$Encoder encoder = Base64.getEncoder();
       return encoder.encodeToString(bytes);
    }
    public static byte[] decryptBASE64(String key){
       Base64 base64 = new Base64();
       return base64.decode(key);
    }
    public static byte[] decryptByPrivateKey(String data,String key){
       return RSAUtil.decryptByPrivateKey(RSAUtil.decryptBASE64(data), key);
    }
    public static byte[] decryptByPrivateKey(byte[] data,String key){
       byte[] uobyteArray;
       try{
          byte[] keyBytes = RSAUtil.decryptBASE64(key);
          PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
          KeyFactory keyFactory = KeyFactory.getInstance("RSA");
          Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
          Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
          cipher.init(2, privateKey);
          uobyteArray = cipher.doFinal(data);
       }catch(java.lang.Exception e1){
          RSAUtil.log.error("RSAUtilsPrivateKeyDecryptError");
          uobyteArray = new byte[0];
       }
       return uobyteArray;
    }
    public static KeyPair getKeyPair(){
       KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
       keyPairGenerator.initialize(2048);
       KeyPair keyPair = keyPairGenerator.generateKeyPair();
       return keyPair;
    }
    public static String getPrivateKey(KeyPair keyPair){
       PrivateKey privateKey = keyPair.getPrivate();
       byte[] bytes = privateKey.getEncoded();
       return RSAUtil.byte2Base64(bytes);
    }
    public static String getPublicKey(KeyPair keyPair){
       PublicKey publicKey = keyPair.getPublic();
       byte[] bytes = publicKey.getEncoded();
       return RSAUtil.byte2Base64(bytes);
    }
    public static String privateDecrypt(String content){
       byte[] base642Byte = RSAUtil.base642Byte(content);
       PrivateKey privateKey = RSAUtil.string2PrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCPDorteL8smR0S984d9TEHTrUyUl2ph5KHRLg1mNdJCo2zyb0jeZmQjq0J78TAWqWrUROqGpIM+E9xe9+A1q6R4TLlTWhI6OnCrqzqNPsWJweNvru3W1RpGze/2bFzxIQDszF7jomBpQB6KOzEpUHDcmZDez/ODyQFth4GCj1KXhwuDyxm+f1DZs94dCgjOZM3117eNdddNuThZ8lAPF+7jsFHE/cH48QfnPfjIZk0v+2hUN+mZyRdJb8RwcApeEE5RiezMRlY2YpfDM72qIPavKojsDRig67FHgRe6Nu/97QkL1aq0Egn6uNB5Vh0BDXDtOHmoxXz83tZu8jJCFIDAgMBAAECggEANlgP7qYv+YEwykPYyQib12rO6o7fhcc6oqg4UiP9pIGd0u6QKynGbGPG2D99PpUw2iOylKwDT2oEw9eK0Ca9qi0iaHk/ZHMWWh6BEXeO6ptpAy+xn7XSGxt70iWgg5njjIRGB+nd0qIbzEw/pXQhx11t+7SWSHD3/jNwtFqvbdA/02yePERucq9jStLz+2l6F6GTsB7zhfdbsmQuWSByYha9zug3o911zGWbb7Nqp6l4mmaid3QUpVrAHi8Nf16bxNsQsEGf30LOgJhL8FGGRyk1cdQRBmRYJ8GTgMkSg4fpHaBrthxYwIYQHg4g2U/BuYaxRR6h+hZS8+E1H848AQKBgQDZOj+MKOBjQlpP0E4/KUlUYE7EsDmNpddUwNMutVZu7xmR/6+GZIEeTD8VA6kLBSgOVpzLO7/VizKKatO8olZq3SzJYiL5X/uSQsXa8XF5sz92wfsRdmvnIm/Xd350MmsvuU6wfgFQI8pq7fvLrBkaLddV6nBw9zhwN8+hN4oheQKBgQColzhpMzbG2JkbBwOzhc9y0fv7ZWcw/LDSKnQFLpzF/wmWNoyEQcUZOG6WUILQYBXlLMSQfNky6yDjHuBnFNPnGwqrMpgo2Ov6DL3wpCvy3fOxdVpKItMGB8NIhRC0a7bVorfARGrI/ouRn0hTx2sopuOwDx5/4StxsNn229LMWwKBgCc2In50anzAX3kEyvsPwtLeR5mtjdYJ16LO49AMn7uKoXz/QHnr7nq/nKt4a603kxFzhP4SnQA5Djh6LBpi22KS5iC7/EAzmT+/meN5rdggCRaw0VdHnxar2rB0O3lSGJMWyiOFu0ryCvneV5NeT3GwUKyLkxxxI9jt2ibp1Z9RAoGAVnhpeJvKBqdlsBUi8ts5Eb1eLw1NpjSVRNZ9d4L26h7rfd4wqE1LmKn3HqXg0pvV3xvdHfbLOHCdB6D4nCZ0oV6fVDu5C4pnTnNmoJyz5fetXGHCk6/U6IzmvrVxYfi1aFHzSWxOERVt5Jz77eykq88djVnT3OdS7xc0VIdfUW0CgYEAlBNWTln5sxCmjxpKJ2JqcOhgmB4C1T3m0QW8D7I01B0Dficn0vAYAZfzFvS8h+ZLC3ijryWVlR2e9MrRLw4HmpFSRkB6YqrmKlvXcdi9gnXqkt4oYEFeXxV0wVH/NuhBEM7AxTJ6fMbhaf7cX94dWAMEfFkork/e9O760qaRS80=");
       Cipher cipher = Cipher.getInstance("RSA");
       cipher.init(2, privateKey);
       byte[] bytes = cipher.doFinal(base642Byte);
       return new String(bytes);
    }
    public static String publicEncrypt(String content){
       PublicKey publicKey = RSAUtil.string2PublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjw6K7Xi/LJkdEvfOHfUxB061MlJdqYeSh0S4NZjXSQqNs8m9I3mZkI6tCe/EwFqlq1ETqhqSDPhPcXvfgNaukeEy5U1oSOjpwq6s6jT7FicHjb67t1tUaRs3v9mxc8SEA7Mxe46JgaUAeijsxKVBw3JmQ3s/zg8kBbYeBgo9Sl4cLg8sZvn9Q2bPeHQoIzmTN9de3jXXXTbk4WfJQDxfu47BRxP3B+PEH5z34yGZNL/toVDfpmckXSW/EcHAKXhBOUYnszEZWNmKXwzO9qiD2ryqI7A0YoOuxR4EXujbv/e0JC9WqtBIJ+rjQeVYdAQ1w7Th5qMV8/N7WbvIyQhSAwIDAQAB");
       Cipher cipher = Cipher.getInstance("RSA");
       cipher.init(1, publicKey);
       byte[] bytes = cipher.doFinal(content.getBytes());
       return RSAUtil.byte2Base64(bytes);
    }
    public static PrivateKey string2PrivateKey(String priStr){
       byte[] keyBytes = RSAUtil.base642Byte(priStr);
       PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
       PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
       return privateKey;
    }
    public static PublicKey string2PublicKey(String pubStr){
       byte[] keyBytes = RSAUtil.base642Byte(pubStr);
       X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
       PublicKey publicKey = keyFactory.generatePublic(keySpec);
       return publicKey;
    }
}
