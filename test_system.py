#!/usr/bin/env python3
"""
系统功能测试脚本
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        print("✅ 健康检查通过")
        return True
    else:
        print(f"❌ 健康检查失败: {response.status_code}")
        return False

def test_api_docs():
    """测试API文档"""
    print("📚 测试API文档...")
    response = requests.get(f"{BASE_URL}/docs")
    if response.status_code == 200:
        print("✅ API文档可访问")
        return True
    else:
        print(f"❌ API文档访问失败: {response.status_code}")
        return False

def test_create_rule():
    """测试创建规则"""
    print("📝 测试创建规则...")
    
    rule_data = {
        "rule_name": "测试规则",
        "rule_describe": "这是一个测试规则",
        "rule_sql": "SELECT 1 as test_column",
        "rule_type": "sql",
        "rule_category1": "测试分类",
        "rule_level": "低"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/rules/",
        json=rule_data,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        rule = response.json()
        print(f"✅ 规则创建成功: {rule['id']}")
        return rule['id']
    else:
        print(f"❌ 规则创建失败: {response.status_code}")
        print(response.text)
        return None

def test_get_rules():
    """测试获取规则列表"""
    print("📋 测试获取规则列表...")
    
    response = requests.get(f"{BASE_URL}/api/v1/rules/")
    
    if response.status_code == 200:
        rules = response.json()
        print(f"✅ 获取规则列表成功，共 {len(rules)} 条规则")
        return True
    else:
        print(f"❌ 获取规则列表失败: {response.status_code}")
        return False

def test_execute_rule(rule_id):
    """测试执行规则"""
    if not rule_id:
        return False
        
    print(f"⚡ 测试执行规则: {rule_id}")
    
    response = requests.post(
        f"{BASE_URL}/api/v1/rules/{rule_id}/execute",
        json={},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 规则执行成功，状态: {result['status']}")
        return True
    else:
        print(f"❌ 规则执行失败: {response.status_code}")
        print(response.text)
        return False

def test_create_plan():
    """测试创建计划"""
    print("📅 测试创建计划...")
    
    plan_data = {
        "plan_name": "测试计划",
        "plan_description": "这是一个测试计划",
        "plan_type": "测试",
        "data_source": "测试数据源"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/plans/",
        json=plan_data,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        plan = response.json()
        print(f"✅ 计划创建成功: {plan['id']}")
        return plan['id']
    else:
        print(f"❌ 计划创建失败: {response.status_code}")
        print(response.text)
        return None

def test_medical_data():
    """测试医保数据接口"""
    print("🏥 测试医保数据接口...")
    
    # 测试药品目录
    response = requests.get(f"{BASE_URL}/api/v1/medical/drugs")
    if response.status_code == 200:
        drugs = response.json()
        print(f"✅ 药品目录接口正常，共 {len(drugs)} 条数据")
    else:
        print(f"❌ 药品目录接口失败: {response.status_code}")
        return False
    
    # 测试诊疗项目
    response = requests.get(f"{BASE_URL}/api/v1/medical/diagnosis-treatments")
    if response.status_code == 200:
        treatments = response.json()
        print(f"✅ 诊疗项目接口正常，共 {len(treatments)} 条数据")
    else:
        print(f"❌ 诊疗项目接口失败: {response.status_code}")
        return False
    
    # 测试统计接口
    response = requests.get(f"{BASE_URL}/api/v1/medical/stats")
    if response.status_code == 200:
        stats = response.json()
        print(f"✅ 医保数据统计接口正常: {stats}")
        return True
    else:
        print(f"❌ 医保数据统计接口失败: {response.status_code}")
        return False

def test_system_info():
    """测试系统信息接口"""
    print("ℹ️ 测试系统信息接口...")
    
    response = requests.get(f"{BASE_URL}/api/v1/system/info")
    if response.status_code == 200:
        info = response.json()
        print(f"✅ 系统信息获取成功")
        print(f"   应用名称: {info.get('app_name')}")
        print(f"   应用版本: {info.get('app_version')}")
        print(f"   Python版本: {info.get('python_version', '').split()[0]}")
        return True
    else:
        print(f"❌ 系统信息获取失败: {response.status_code}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始FlyCheck Python系统测试")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    tests = [
        test_health,
        test_api_docs,
        test_system_info,
        test_get_rules,
        test_medical_data,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    # 测试创建和执行规则
    try:
        rule_id = test_create_rule()
        if rule_id:
            passed += 1
            if test_execute_rule(rule_id):
                passed += 1
            total += 1
        total += 1
        print()
    except Exception as e:
        print(f"❌ 规则测试异常: {e}")
        total += 2
        print()
    
    # 测试创建计划
    try:
        plan_id = test_create_plan()
        if plan_id:
            passed += 1
        total += 1
        print()
    except Exception as e:
        print(f"❌ 计划测试异常: {e}")
        total += 1
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查系统状态")
        return 1

if __name__ == "__main__":
    exit(main())
