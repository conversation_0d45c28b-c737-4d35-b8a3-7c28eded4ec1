package com.taikang.fly.check.dto.plan.PlanLogSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class PlanLogSearchDto implements Serializable	// class@0001a1 from classes.dex
{
    private String createTimeEnd;
    private String createTimeStart;
    private String creator;
    private String missionName;
    private String missionStatus;
    private String planId;
    private List ruleIdList;
    private static final long serialVersionUID = 0x1;

    public void PlanLogSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanLogSearchDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanLogSearchDto){
          b = false;
       }else {
          PlanLogSearchDto planLogSearc = o;
          if (!planLogSearc.canEqual(this)) {
             b = false;
          }else {
             String missionName = this.getMissionName();
             String missionName1 = planLogSearc.getMissionName();
             if (missionName == null) {
                if (missionName1 != null) {
                   b = false;
                }
             }else if(missionName.equals(missionName1)){
             }
             String creator = this.getCreator();
             String creator1 = planLogSearc.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTimeSt = this.getCreateTimeStart();
             String createTimeSt1 = planLogSearc.getCreateTimeStart();
             if (createTimeSt == null) {
                if (createTimeSt1 != null) {
                   b = false;
                }
             }else if(createTimeSt.equals(createTimeSt1)){
             }
             String createTimeEn = this.getCreateTimeEnd();
             String createTimeEn1 = planLogSearc.getCreateTimeEnd();
             if (createTimeEn == null) {
                if (createTimeEn1 != null) {
                   b = false;
                }
             }else if(createTimeEn.equals(createTimeEn1)){
             }
             String missionStatu = this.getMissionStatus();
             String missionStatu1 = planLogSearc.getMissionStatus();
             if (missionStatu == null) {
                if (missionStatu1 != null) {
                   b = false;
                }
             }else if(missionStatu.equals(missionStatu1)){
             }
             String planId = this.getPlanId();
             String planId1 = planLogSearc.getPlanId();
             if (planId == null) {
                if (planId1 != null) {
                label_0098 :
                   b = false;
                }
             }else if(planId.equals(planId1)){
             }
             List ruleIdList = this.getRuleIdList();
             List ruleIdList1 = planLogSearc.getRuleIdList();
             if (ruleIdList == null) {
                if (ruleIdList1 != null) {
                   b = false;
                }
             }else if(ruleIdList.equals(ruleIdList1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTimeEnd(){
       return this.createTimeEnd;
    }
    public String getCreateTimeStart(){
       return this.createTimeStart;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getMissionName(){
       return this.missionName;
    }
    public String getMissionStatus(){
       return this.missionStatus;
    }
    public String getPlanId(){
       return this.planId;
    }
    public List getRuleIdList(){
       return this.ruleIdList;
    }
    public int hashCode(){
       String $missionName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($missionName = this.getMissionName()) == null)? i: $missionName.hashCode();
       result = i1 + 59;
       String $creator = this.getCreator();
       int i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTimeStart = this.getCreateTimeStart();
       i2 = result * 59;
       i1 = ($createTimeStart == null)? i: $createTimeStart.hashCode();
       result = i2 + i1;
       String $createTimeEnd = this.getCreateTimeEnd();
       i2 = result * 59;
       i1 = ($createTimeEnd == null)? i: $createTimeEnd.hashCode();
       result = i2 + i1;
       String $missionStatus = this.getMissionStatus();
       i2 = result * 59;
       i1 = ($missionStatus == null)? i: $missionStatus.hashCode();
       result = i2 + i1;
       String planId = this.getPlanId();
       i2 = result * 59;
       i1 = (planId == null)? i: planId.hashCode();
       List ruleIdList = this.getRuleIdList();
       i1 = (i2 + i1) * 59;
       if (ruleIdList != null) {
          i = ruleIdList.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTimeEnd(String createTimeEnd){
       this.createTimeEnd = createTimeEnd;
    }
    public void setCreateTimeStart(String createTimeStart){
       this.createTimeStart = createTimeStart;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setMissionName(String missionName){
       this.missionName = missionName;
    }
    public void setMissionStatus(String missionStatus){
       this.missionStatus = missionStatus;
    }
    public void setPlanId(String planId){
       this.planId = planId;
    }
    public void setRuleIdList(List ruleIdList){
       this.ruleIdList = ruleIdList;
    }
    public String toString(){
       return "PlanLogSearchDto\(missionName="+this.getMissionName()+", creator="+this.getCreator()+", createTimeStart="+this.getCreateTimeStart()+", createTimeEnd="+this.getCreateTimeEnd()+", missionStatus="+this.getMissionStatus()+", planId="+this.getPlanId()+", ruleIdList="+this.getRuleIdList()+"\)";
    }
}
