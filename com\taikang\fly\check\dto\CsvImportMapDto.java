package com.taikang.fly.check.dto.CsvImportMapDto;
import java.lang.Object;
import java.lang.Boolean;
import java.lang.Integer;
import java.lang.String;

public class CsvImportMapDto	// class@00009a from classes.dex
{
    private String csvHeaderName;
    private String dateTypeFormat;
    private String dbFieldName;
    private String dbFieldType;
    private Boolean importTag;
    private Integer indexNum;
    private Boolean required;

    public void CsvImportMapDto(){
       super();
       this.importTag = Boolean.valueOf(false);
       this.required = Boolean.valueOf(false);
    }
    public void CsvImportMapDto(Integer indexNum,String csvHeaderName,String dbFieldName,String dbFieldType,Boolean importTag,Boolean required,String dateTypeFormat){
       super();
       this.importTag = Boolean.valueOf(false);
       this.required = Boolean.valueOf(false);
       this.indexNum = indexNum;
       this.csvHeaderName = csvHeaderName;
       this.dbFieldName = dbFieldName;
       this.dbFieldType = dbFieldType;
       this.importTag = importTag;
       this.required = required;
       this.dateTypeFormat = dateTypeFormat;
    }
    public String getCsvHeaderName(){
       return this.csvHeaderName;
    }
    public String getDateTypeFormat(){
       return this.dateTypeFormat;
    }
    public String getDbFieldName(){
       return this.dbFieldName;
    }
    public String getDbFieldType(){
       return this.dbFieldType;
    }
    public Boolean getImportTag(){
       return this.importTag;
    }
    public Integer getIndexNum(){
       return this.indexNum;
    }
    public Boolean getRequired(){
       return this.required;
    }
    public void setCsvHeaderName(String csvHeaderName){
       this.csvHeaderName = csvHeaderName;
    }
    public void setDateTypeFormat(String dateTypeFormat){
       this.dateTypeFormat = dateTypeFormat;
    }
    public void setDbFieldName(String dbFieldName){
       this.dbFieldName = dbFieldName;
    }
    public void setDbFieldType(String dbFieldType){
       this.dbFieldType = dbFieldType;
    }
    public void setImportTag(Boolean importTag){
       this.importTag = importTag;
    }
    public void setIndexNum(Integer indexNum){
       this.indexNum = indexNum;
    }
    public void setRequired(Boolean required){
       this.required = required;
    }
}
