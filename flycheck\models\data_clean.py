"""
数据清洗相关模型
"""

from sqlalchemy import Column, String, Text, Integer, JSON

from .base import BaseModel


class DataCleanRule(BaseModel):
    """数据清洗规则"""
    
    __tablename__ = "data_clean_rule"
    
    rule_name = Column(String(200), nullable=False, comment="规则名称")
    rule_description = Column(Text, comment="规则描述")
    rule_type = Column(String(50), comment="规则类型")
    
    # 数据源配置
    source_table = Column(String(100), comment="源表名")
    target_table = Column(String(100), comment="目标表名")
    
    # 清洗逻辑
    clean_logic = Column(Text, comment="清洗逻辑")
    clean_sql = Column(Text, comment="清洗SQL")
    
    # 执行配置
    batch_size = Column(Integer, default=1000, comment="批处理大小")
    execution_order = Column(Integer, comment="执行顺序")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-启用 0-禁用")
    
    # 扩展配置
    config = Column(JSON, comment="扩展配置")
