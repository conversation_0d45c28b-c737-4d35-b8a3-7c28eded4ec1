package com.taikang.fly.check.service.ClickHouseClusterTableService;
import com.baomidou.mybatisplus.extension.service.IService;
import java.lang.String;
import java.util.List;

public interface abstract ClickHouseClusterTableService implements IService	// class@0002c9 from classes.dex
{

    void createBillAndDetails();
    void delClickhouseClusterTable(String p0);
    void execute(List p0);
    void geneSql(String p0);
    List queryClickHouseClusterTableField(String p0);
    List queryClickHouseClusterTableList();
    String querySqlById(String p0);
    void renameClickhouseClusterTable(String p0,String p1);
    void saveClickHouseClusterTable(String p0,String p1,int p2);
}
