package com.taikang.fly.check.rest.BusinessColConfigController;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.service.BusinessColConfigService;
import java.util.List;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigQueryDto;
import java.lang.Integer;
import com.taikang.fly.check.comm.Page;
import com.taikang.fly.check.service.MfromtOfficeConfigService;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigAddDto;

public class BusinessColConfigController	// class@000280 from classes.dex
{
    private BusinessColConfigService businessColConfigService;
    private MfromtOfficeConfigService mfromtOfficeConfigService;

    public void BusinessColConfigController(){
       super();
    }
    public RmpResponse deleteColConfigByKey(String businessKey){
       this.businessColConfigService.deleteByBusinessKey(businessKey);
       return RmpResponse.success();
    }
    public RmpResponse queryColConfigList(String businessKey){
       List colConfigRespDtoPage = this.businessColConfigService.queryColConfigList3(businessKey);
       return RmpResponse.success(colConfigRespDtoPage);
    }
    public RmpResponse queryColConfigListPage(ColConfigQueryDto colConfigQueryDto,Integer pageNum,Integer pageSize){
       Page colConfigRespDtoPage = this.mfromtOfficeConfigService.queryListPage(colConfigQueryDto, pageNum, pageSize);
       return RmpResponse.success(colConfigRespDtoPage);
    }
    public RmpResponse saveColConfig(ColConfigAddDto colConfigAddDto){
       this.businessColConfigService.saveColConfig2(colConfigAddDto);
       return RmpResponse.success();
    }
}
