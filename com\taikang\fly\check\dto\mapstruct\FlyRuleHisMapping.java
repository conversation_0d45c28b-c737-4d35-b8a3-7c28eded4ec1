package com.taikang.fly.check.dto.mapstruct.FlyRuleHisMapping;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleHis;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisRespDto;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisEditDto;
import java.util.List;

public interface abstract FlyRuleHisMapping	// class@000154 from classes.dex
{

    FlyRuleHis addDtoToDomain(FlyRuleHisAddDto p0);
    FlyRuleHisRespDto domainToInfoDto(FlyRuleHis p0);
    FlyRuleHis dtoToDomain(FlyRuleHisRespDto p0);
    FlyRuleHis editDtoToDomain(FlyRuleHisEditDto p0,FlyRuleHis p1);
    List entityToDtos(List p0);
}
