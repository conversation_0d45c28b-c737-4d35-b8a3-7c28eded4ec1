package com.taikang.fly.check.service.RoleMenuService;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Iterator;
import com.taikang.fly.check.dto.menu.MenuIdDto;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Map;
import com.taikang.fly.check.mybatis.dao.RoleMenuMapper;
import java.util.Collection;
import org.springframework.util.CollectionUtils;
import com.taikang.fly.check.dto.system.role.RoleMenuManageDto;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.mybatis.domain.RoleMenu;
import java.util.Date;
import com.taikang.fly.check.utils.SequenceGenerator;
import java.lang.Integer;

public class RoleMenuService	// class@0002fa from classes.dex
{
    private RoleMenuMapper roleMenuMapper;

    public void RoleMenuService(){
       super();
    }
    private List getMenuParentId(List parentIdList,String parentId,List menuIdList){
       Iterator iterator = menuIdList.iterator();
       while (iterator.hasNext()) {
          MenuIdDto menuIdDto = iterator.next();
          if (parentId.equals(menuIdDto.getParentId())) {
             parentIdList.add(parentId);
             this.getMenuParentId(parentIdList, menuIdDto.getMenuId(), menuIdList);
          }
       }
       return parentIdList;
    }
    public List menuIdListByRoleCode(String roleCode,String moduleCode){
       Map params = new HashMap(2);
       List returnList = new ArrayList();
       List parentIdList = new ArrayList();
       params.put("roleCode", roleCode);
       params.put("moduleCode", moduleCode);
       List menuIdList = this.roleMenuMapper.queryMenuIdListByParams(params);
       if (!CollectionUtils.isEmpty(menuIdList)) {
          Iterator iterator = menuIdList.iterator();
          while (iterator.hasNext()) {
             returnList.add(iterator.next().getMenuId());
          }
          returnList.removeAll(this.getMenuParentId(parentIdList, "0", menuIdList));
       }
       return returnList;
    }
    public void rolemMenuManage(RoleMenuManageDto roleMenuMangageDto){
       Map params = new HashMap(2);
       params.put("roleCode", roleMenuMangageDto.getRoleCode());
       params.put("moduleCode", roleMenuMangageDto.getModuleCode());
       this.roleMenuMapper.deleteByRoleCode(params);
       String menus = (roleMenuMangageDto.getMenus() == null)? "": roleMenuMangageDto.getMenus();
       if (!StringUtils.isBlank(menus)) {
          UserDto userInfo = ThreadLocalContextHolder.getContext().getUserInfo();
          String[] stringArray = menus.split(",");
          RoleMenu roleMenu = new RoleMenu();
          roleMenu.setCreateTime(new Date());
          roleMenu.setCreator(userInfo.getUserCode());
          roleMenu.setRoleCode(roleMenuMangageDto.getRoleCode());
          roleMenu.setModby(userInfo.getUserCode());
          roleMenu.setModifyTime(new Date());
          roleMenu.setSignature("1");
          roleMenu.setIsValid("1");
          roleMenu.setModuleCode(roleMenuMangageDto.getModuleCode());
          int len = stringArray.length;
          for (int i = 0; i < len; i = i + 1) {
             roleMenu.setId(SequenceGenerator.getId());
             roleMenu.setMenuCode(stringArray[i]);
             this.roleMenuMapper.saveRoleMenu(roleMenu);
          }
       }
       return;
    }
}
