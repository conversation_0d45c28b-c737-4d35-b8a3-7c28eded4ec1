package com.taikang.fly.check.mybatis.dao.FlyRuleTemplateMapper;
import java.util.Map;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.FlyRuleTemplate;
import java.lang.String;
import java.lang.Integer;

public interface abstract FlyRuleTemplateMapper	// class@0001f7 from classes.dex
{

    List findFlyRuleTemplatePage(Map p0);
    int insert(FlyRuleTemplate p0);
    List selectAll();
    FlyRuleTemplate selectById(String p0);
    FlyRuleTemplate selectByPrimaryKey(String p0);
    List selectPrimaryMenu();
    List selectRuleName();
    List selectSecondaryMenu(String p0);
    Integer updateByPrimaryKey(FlyRuleTemplate p0);
}
