package org.springframework.boot.loader.jar.Handler;
import java.net.URLStreamHandler;
import java.lang.String;
import java.util.regex.Pattern;
import java.lang.ref.SoftReference;
import java.lang.Object;
import org.springframework.boot.loader.jar.JarFile;
import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.CharSequence;
import java.lang.Class;
import java.lang.IllegalStateException;
import java.net.URL;
import java.lang.StringBuilder;
import java.lang.IllegalArgumentException;
import java.lang.Throwable;
import java.io.IOException;
import java.net.URI;
import java.lang.Exception;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.lang.System;
import java.io.PrintStream;
import java.net.URLConnection;
import java.lang.RuntimeException;
import java.util.regex.Matcher;
import org.springframework.boot.loader.jar.JarURLConnection;
import java.net.MalformedURLException;

public class Handler extends URLStreamHandler	// class@000550 from classes.dex
{
    private URLStreamHandler fallbackHandler;
    private final JarFile jarFile;
    private static final String CURRENT_DIR = "/./";
    private static final Pattern CURRENT_DIR_PATTERN;
    private static final String[] FALLBACK_HANDLERS;
    private static final String FILE_PROTOCOL;
    private static final String JAR_PROTOCOL;
    private static final String PARENT_DIR;
    private static final String SEPARATOR;
    private static SoftReference rootFileCache;

    static {
       Handler.CURRENT_DIR_PATTERN = Pattern.compile("/./");
       String[] stringArray = new String[]{"sun.net.www.protocol.jar.Handler"};
       Handler.FALLBACK_HANDLERS = stringArray;
       Handler.rootFileCache = new SoftReference(null);
    }
    public void Handler(){
       super(null);
    }
    public void Handler(JarFile jarFile){
       super();
       this.jarFile = jarFile;
    }
    static void addToRootFileCache(File sourceFile,JarFile jarFile){
       Map map;
       if ((map = Handler.rootFileCache.get()) == null) {
          map = new ConcurrentHashMap();
          Handler.rootFileCache = new SoftReference(map);
       }
       map.put(sourceFile, jarFile);
       return;
    }
    private String canonicalize(String path){
       return path.replace("!/", "/");
    }
    private URLStreamHandler getFallbackHandler(){
       Handler tHandler;
       if (this.fallbackHandler != null) {
          tHandler = this.fallbackHandler;
       }else {
          String[] fALLBACK_HAN = Handler.FALLBACK_HANDLERS;
          int len = fALLBACK_HAN.length;
          int i = 0;
       label_000c :
          if (i < len) {
             object oobject = fALLBACK_HAN[i];
             try{
                this.fallbackHandler = Class.forName(oobject).newInstance();
                tHandler = this.fallbackHandler;
             }catch(java.lang.Exception e2){
                int i1 = i + 1;
                i = i1;
                goto label_000c ;
             }
          }else {
             throw new IllegalStateException("Unable to find fallback handler");
          }
       }
       return tHandler;
    }
    private String getFileFromContext(URL context,String spec){
       String str;
       int i;
       String file = context.getFile();
       if (spec.startsWith("/")) {
          str = this.trimToJarRoot(file)+"!/"+spec.substring(1);
       }else if(file.endsWith("/")){
          str = file+spec;
       }else if((i = file.lastIndexOf(47)) == -1){
          throw new IllegalArgumentException("No / found in context URL\'s file \'"+file+"\'");
       }else {
          str = file.substring(0, (i + 1))+spec;
       }
       return str;
    }
    private String getFileFromSpec(String spec){
       int separatorIndex;
       if ((separatorIndex = spec.lastIndexOf("!/")) == -1) {
          throw new IllegalArgumentException("No !/ in spec \'"+spec+"\'");
       }
       try{
          URL uRL = new URL(spec.substring(0, separatorIndex));
          return spec;
       }catch(java.net.MalformedURLException e0){
          throw new IllegalArgumentException("Invalid spec URL \'"+spec+"\'", e0);
       }
    }
    private JarFile getRootJarFile(String name){
       Map map;
       try{
          if (!name.startsWith("file:")) {
             throw new IllegalStateException("Not a file URL");
          }
          File uFile = new File(URI.create(name));
          JarFile jarFile = ((map = Handler.rootFileCache.get()) != null)? map.get(uFile): null;
          if (jarFile == null) {
             jarFile = new JarFile(uFile);
             Handler.addToRootFileCache(uFile, jarFile);
          }
          return jarFile;
       }catch(java.lang.Exception e1){
          throw new IOException("Unable to open root Jar file \'"+name+"\'", e1);
       }
    }
    private int hashCode(String protocol,String file){
       int separatorIndex;
       int i = 0;
       int result = (protocol != null)? protocol.hashCode(): i;
       if ((separatorIndex = file.indexOf("!/")) == -1) {
          i = file.hashCode() + result;
          result = result;
       }else {
          String str = file.substring(i, separatorIndex);
          String str1 = this.canonicalize(file.substring((separatorIndex + 2)));
          try{
             result = result + new URL(str).hashCode();
          }catch(java.net.MalformedURLException e1){
             result = result + str.hashCode();
          }
          result = result + str1.hashCode();
          i = result;
       }
       return i;
    }
    private boolean isUrlInJarFile(URL url,JarFile jarFile){
       boolean b = (url.getPath().startsWith(jarFile.getUrl().getPath()) && url.toString().startsWith(jarFile.getUrlString()))? true: false;
       return b;
    }
    private void log(boolean warning,String message,Exception cause){
       Level level;
       try{
          if (warning) {
             level = Level.WARNING;
          label_0004 :
             Logger.getLogger(this.getClass().getName()).log(level, message, cause);
          }else {
             level = Level.FINEST;
             goto label_0004 ;
          }
       }catch(java.lang.Exception e0){
          if (warning) {
             System.err.println("WARNING: "+message);
          }
       }
       return;
    }
    private String normalize(String file){
       if (file.contains("/./") || file.contains("/../")) {
          int i = file.lastIndexOf("!/") + "!/".length();
          file = file.substring(0, i)+this.replaceCurrentDir(this.replaceParentDir(file.substring(i)));
       }
       return file;
    }
    private URLConnection openConnection(URLStreamHandler handler,URL url){
       return new URL(null, url.toExternalForm(), handler).openConnection();
    }
    private URLConnection openFallbackConnection(URL url,Exception reason){
       try{
          return this.openConnection(this.getFallbackHandler(), url);
       }catch(java.lang.Exception e0){
          if (reason instanceof IOException) {
             this.log(false, "Unable to open fallback handler", e0);
             throw reason;
          }else {
             this.log(true, "Unable to open fallback handler", e0);
             if (reason instanceof RuntimeException) {
                throw reason;
             }
             throw new IllegalStateException(reason);
          }
       }
    }
    private String replaceCurrentDir(String file){
       return Handler.CURRENT_DIR_PATTERN.matcher(file).replaceAll("/");
    }
    private String replaceParentDir(String file){
       int parentDirIndex;
       int i1;
       while ((parentDirIndex = file.indexOf("/../")) >= 0) {
          int i = parentDirIndex - 1;
          if ((i1 = file.lastIndexOf(47, i)) >= 0) {
             i = parentDirIndex + 3;
             file = "".append(file.substring(0, i1)).append(file.substring(i)).toString();
          }else {
             int i2 = parentDirIndex + 4;
             file = file.substring(i2);
          }
       }
       return file;
    }
    private void setFile(URL context,String file){
       int queryIndex;
       String path = this.normalize(file);
       String query = null;
       if ((queryIndex = path.lastIndexOf(63)) != -1) {
          query = path.substring((queryIndex + 1));
          path = path.substring(0, queryIndex);
       }
       this.setURL(context, "jar:", null, -1, null, null, path, query, context.getRef());
       return;
    }
    public static void setUseFastConnectionExceptions(boolean useFastConnectionExceptions){
       JarURLConnection.setUseFastExceptions(useFastConnectionExceptions);
    }
    private String trimToJarRoot(String file){
       int lastSeparatorIndex;
       if ((lastSeparatorIndex = file.lastIndexOf("!/")) == -1) {
          throw new IllegalArgumentException("No !/ found in context URL\'s file \'"+file+"\'");
       }
       return file.substring(0, lastSeparatorIndex);
    }
    public JarFile getRootJarFileFromUrl(URL url){
       int separatorIndex;
       String spec = url.getFile();
       if ((separatorIndex = spec.indexOf("!/")) == -1) {
          throw new MalformedURLException("Jar URL does not contain !/ separator");
       }
       return this.getRootJarFile(spec.substring(0, separatorIndex));
    }
    protected int hashCode(URL u){
       return this.hashCode(u.getProtocol(), u.getFile());
    }
    protected URLConnection openConnection(URL url){
       JarURLConnection jarURLConnec;
       if (this.jarFile != null && this.isUrlInJarFile(url, this.jarFile)) {
          jarURLConnec = JarURLConnection.get(url, this.jarFile);
       }else {
          try{
             jarURLConnec = JarURLConnection.get(url, this.getRootJarFileFromUrl(url));
          }catch(java.lang.Exception e0){
             jarURLConnec = this.openFallbackConnection(url, e0);
          }
       }
       return jarURLConnec;
    }
    protected void parseURL(URL context,String spec,int start,int limit){
       if (spec.regionMatches(true, 0, "jar:", 0, "jar:".length())) {
          this.setFile(context, this.getFileFromSpec(spec.substring(start, limit)));
       }else {
          this.setFile(context, this.getFileFromContext(context, spec.substring(start, limit)));
       }
       return;
    }
    protected boolean sameFile(URL u1,URL u2){
       boolean b = false;
       if (u1.getProtocol().equals("jar") && u2.getProtocol().equals("jar")) {
          int i = u1.getFile().indexOf("!/");
          int i1 = u2.getFile().indexOf("!/");
          if (i != -1 && i1 != -1) {
             String str = u1.getFile().substring(("!/".length() + i));
             String str1 = u2.getFile().substring(("!/".length() + i1));
             if (str.equals(str1) || this.canonicalize(str).equals(this.canonicalize(str1))) {
                String str2 = u1.getFile().substring(b, i);
                String str3 = u2.getFile().substring(b, i1);
                try{
                   b = super.sameFile(new URL(str2), new URL(str3));
                }catch(java.net.MalformedURLException e8){
                   b = super.sameFile(u1, u2);
                }
             }
          }else {
             b = super.sameFile(u1, u2);
          }
       }
       return b;
    }
}
