package com.taikang.fly.check.rest.FlyRuleHisController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisAddDto;
import com.taikang.fly.check.comm.CommResponse;
import java.lang.Integer;
import com.taikang.fly.check.service.FlyRuleHisService;
import javax.servlet.http.HttpServletResponse;
import java.lang.String;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisEditDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleHis;
import com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisRespDto;
import com.taikang.fly.check.comm.NativePage;

public class FlyRuleHisController	// class@000292 from classes.dex
{
    private FlyRuleHisService flyRuleHisService;
    private static final Logger log;

    static {
       FlyRuleHisController.log = LoggerFactory.getLogger(FlyRuleHisController.class);
    }
    public void FlyRuleHisController(){
       super();
    }
    public CommResponse addFlyRule(FlyRuleHisAddDto flyRuleHisAddDto){
       return CommResponse.success(this.flyRuleHisService.addFlyRule(flyRuleHisAddDto));
    }
    public void batchExportHis(HttpServletResponse response){
       this.flyRuleHisService.batchExportHis(response);
    }
    public CommResponse dataSyn(){
       Integer integer = this.flyRuleHisService.dataSyn();
       return CommResponse.success(integer);
    }
    public CommResponse delFlyRule(String id){
       return CommResponse.success(this.flyRuleHisService.delFlyRule(id));
    }
    public CommResponse editFlyRule(FlyRuleHisEditDto flyRuleEditDto){
       return CommResponse.success(this.flyRuleHisService.editFlyRule(flyRuleEditDto));
    }
    public CommResponse getFlyRuleById(String id){
       return CommResponse.success(this.flyRuleHisService.getFlyRuleHisById(id));
    }
    public CommResponse listPage(Integer page,Integer size){
       return CommResponse.success(this.flyRuleHisService.listFlyRuleHis(page, size));
    }
}
