package com.taikang.fly.check.mybatis.domain.FlyRuleNumbers;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class FlyRuleNumbers implements Serializable	// class@000246 from classes.dex
{
    private String flyRuleId;
    private String flyRuleName;
    private String id;
    private Integer numbers;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleNumbers(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleNumbers;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleNumbers) {
             b = false;
          }else {
             FlyRuleNumbers uFlyRuleNumb = o;
             if (!uFlyRuleNumb.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uFlyRuleNumb.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String flyRuleName = this.getFlyRuleName();
                String flyRuleName1 = uFlyRuleNumb.getFlyRuleName();
                if (flyRuleName == null) {
                   if (flyRuleName1 != null) {
                      b = false;
                   }
                }else if(flyRuleName.equals(flyRuleName1)){
                }
                String flyRuleId = this.getFlyRuleId();
                String flyRuleId1 = uFlyRuleNumb.getFlyRuleId();
                if (flyRuleId == null) {
                   if (flyRuleId1 != null) {
                      b = false;
                   }
                }else if(flyRuleId.equals(flyRuleId1)){
                }
                Integer numbers = this.getNumbers();
                Integer numbers1 = uFlyRuleNumb.getNumbers();
                if (numbers == null) {
                   if (numbers1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!numbers.equals(numbers1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFlyRuleId(){
       return this.flyRuleId;
    }
    public String getFlyRuleName(){
       return this.flyRuleName;
    }
    public String getId(){
       return this.id;
    }
    public Integer getNumbers(){
       return this.numbers;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $flyRuleName = this.getFlyRuleName();
       int i2 = result * 59;
       i1 = ($flyRuleName == null)? i: $flyRuleName.hashCode();
       result = i2 + i1;
       String $flyRuleId = this.getFlyRuleId();
       i2 = result * 59;
       i1 = ($flyRuleId == null)? i: $flyRuleId.hashCode();
       result = i2 + i1;
       Integer $numbers = this.getNumbers();
       i1 = result * 59;
       if ($numbers != null) {
          i = $numbers.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFlyRuleId(String flyRuleId){
       this.flyRuleId = flyRuleId;
    }
    public void setFlyRuleName(String flyRuleName){
       this.flyRuleName = flyRuleName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNumbers(Integer numbers){
       this.numbers = numbers;
    }
    public String toString(){
       return "FlyRuleNumbers\(id="+this.getId()+", flyRuleName="+this.getFlyRuleName()+", flyRuleId="+this.getFlyRuleId()+", numbers="+this.getNumbers()+"\)";
    }
}
