package com.taikang.fly.check.dto.hospinfo.HospInfoRespDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class HospInfoRespDto	// class@000127 from classes.dex
{
    private String createdTime;
    private String creator;
    private String hospName;
    private String id;
    private String oraUserName;
    private String region;

    public void HospInfoRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof HospInfoRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof HospInfoRespDto){
          b = false;
       }else {
          HospInfoRespDto hospInfoResp = o;
          if (!hospInfoResp.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = hospInfoResp.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String region = this.getRegion();
             String region1 = hospInfoResp.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String hospName = this.getHospName();
             String hospName1 = hospInfoResp.getHospName();
             if (hospName == null) {
                if (hospName1 != null) {
                   b = false;
                }
             }else if(hospName.equals(hospName1)){
             }
             String oraUserName = this.getOraUserName();
             String oraUserName1 = hospInfoResp.getOraUserName();
             if (oraUserName == null) {
                if (oraUserName1 != null) {
                   b = false;
                }
             }else if(oraUserName.equals(oraUserName1)){
             }
             String creator = this.getCreator();
             String creator1 = hospInfoResp.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createdTime = this.getCreatedTime();
             String createdTime1 = hospInfoResp.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                label_0085 :
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getHospName(){
       return this.hospName;
    }
    public String getId(){
       return this.id;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public String getRegion(){
       return this.region;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $region = this.getRegion();
       int i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $hospName = this.getHospName();
       i2 = result * 59;
       i1 = ($hospName == null)? i: $hospName.hashCode();
       result = i2 + i1;
       String $oraUserName = this.getOraUserName();
       i2 = result * 59;
       i1 = ($oraUserName == null)? i: $oraUserName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String createdTime = this.getCreatedTime();
       i1 = result * 59;
       if (createdTime != null) {
          i = createdTime.hashCode();
       }
       return (i1 + i);
    }
    public void setCreatedTime(String createdTime){
       this.createdTime = createdTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public String toString(){
       return "HospInfoRespDto\(id="+this.getId()+", region="+this.getRegion()+", hospName="+this.getHospName()+", oraUserName="+this.getOraUserName()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+"\)";
    }
}
