package com.taikang.fly.check.dto.csventry.CsvConfigEntry;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.util.Date;
import java.lang.StringBuilder;

public class CsvConfigEntry	// class@0000d5 from classes.dex
{
    private String configName;
    private Date createTime;
    private String dateFormat;
    private Integer fieldAccurary;
    private Integer fieldLength;
    private String fieldName;
    private String fieldType;
    private String id;
    private String remark;

    public void CsvConfigEntry(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CsvConfigEntry;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof CsvConfigEntry){
          b = false;
       }else {
          CsvConfigEntry uCsvConfigEn = o;
          if (!uCsvConfigEn.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uCsvConfigEn.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String configName = this.getConfigName();
             String configName1 = uCsvConfigEn.getConfigName();
             if (configName == null) {
                if (configName1 != null) {
                   b = false;
                }
             }else if(configName.equals(configName1)){
             }
             String fieldName = this.getFieldName();
             String fieldName1 = uCsvConfigEn.getFieldName();
             if (fieldName == null) {
                if (fieldName1 != null) {
                   b = false;
                }
             }else if(fieldName.equals(fieldName1)){
             }
             String fieldType = this.getFieldType();
             String fieldType1 = uCsvConfigEn.getFieldType();
             if (fieldType == null) {
                if (fieldType1 != null) {
                   b = false;
                }
             }else if(fieldType.equals(fieldType1)){
             }
             Integer fieldLength = this.getFieldLength();
             Integer fieldLength1 = uCsvConfigEn.getFieldLength();
             if (fieldLength == null) {
                if (fieldLength1 != null) {
                   b = false;
                }
             }else if(fieldLength.equals(fieldLength1)){
             }
             Integer fieldAccurar = this.getFieldAccurary();
             Integer fieldAccurar1 = uCsvConfigEn.getFieldAccurary();
             if (fieldAccurar == null) {
                if (fieldAccurar1 != null) {
                   b = false;
                }
             }else if(fieldAccurar.equals(fieldAccurar1)){
             }
             String dateFormat = this.getDateFormat();
             String dateFormat1 = uCsvConfigEn.getDateFormat();
             if (dateFormat == null) {
                if (dateFormat1 != null) {
                label_00b7 :
                   b = false;
                }
             }else if(dateFormat.equals(dateFormat1)){
             }
             String remark = this.getRemark();
             String remark1 = uCsvConfigEn.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = uCsvConfigEn.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_00e5 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getConfigName(){
       return this.configName;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getDateFormat(){
       return this.dateFormat;
    }
    public Integer getFieldAccurary(){
       return this.fieldAccurary;
    }
    public Integer getFieldLength(){
       return this.fieldLength;
    }
    public String getFieldName(){
       return this.fieldName;
    }
    public String getFieldType(){
       return this.fieldType;
    }
    public String getId(){
       return this.id;
    }
    public String getRemark(){
       return this.remark;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $configName = this.getConfigName();
       int i2 = result * 59;
       i1 = ($configName == null)? i: $configName.hashCode();
       result = i2 + i1;
       String $fieldName = this.getFieldName();
       i2 = result * 59;
       i1 = ($fieldName == null)? i: $fieldName.hashCode();
       result = i2 + i1;
       String $fieldType = this.getFieldType();
       i2 = result * 59;
       i1 = ($fieldType == null)? i: $fieldType.hashCode();
       result = i2 + i1;
       Integer $fieldLength = this.getFieldLength();
       i2 = result * 59;
       i1 = ($fieldLength == null)? i: $fieldLength.hashCode();
       result = i2 + i1;
       Integer fieldAccurar = this.getFieldAccurary();
       i2 = result * 59;
       i1 = (fieldAccurar == null)? i: fieldAccurar.hashCode();
       String dateFormat = this.getDateFormat();
       i2 = (i2 + i1) * 59;
       i1 = (dateFormat == null)? i: dateFormat.hashCode();
       String remark = this.getRemark();
       i2 = (i2 + i1) * 59;
       i1 = (remark == null)? i: remark.hashCode();
       Date createTime = this.getCreateTime();
       i1 = (i2 + i1) * 59;
       if (createTime != null) {
          i = createTime.hashCode();
       }
       return (i1 + i);
    }
    public void setConfigName(String configName){
       this.configName = configName;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setDateFormat(String dateFormat){
       this.dateFormat = dateFormat;
    }
    public void setFieldAccurary(Integer fieldAccurary){
       this.fieldAccurary = fieldAccurary;
    }
    public void setFieldLength(Integer fieldLength){
       this.fieldLength = fieldLength;
    }
    public void setFieldName(String fieldName){
       this.fieldName = fieldName;
    }
    public void setFieldType(String fieldType){
       this.fieldType = fieldType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public String toString(){
       return "CsvConfigEntry\(id="+this.getId()+", configName="+this.getConfigName()+", fieldName="+this.getFieldName()+", fieldType="+this.getFieldType()+", fieldLength="+this.getFieldLength()+", fieldAccurary="+this.getFieldAccurary()+", dateFormat="+this.getDateFormat()+", remark="+this.getRemark()+", createTime="+this.getCreateTime()+"\)";
    }
}
