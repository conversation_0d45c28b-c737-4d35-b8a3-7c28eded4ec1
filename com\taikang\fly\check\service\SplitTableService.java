package com.taikang.fly.check.service.SplitTableService;
import com.baomidou.mybatisplus.extension.service.IService;
import com.taikang.fly.check.dto.importData.SplitInfoDto;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

public interface abstract SplitTableService implements IService	// class@0002fe from classes.dex
{

    String dataSplit(SplitInfoDto p0);
    void insertSplit();
    List pageByAll(Integer p0);
    String transferTable(List p0);
}
