package com.taikang.fly.check.dto.SplitTableLogsDto;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;
import java.lang.StringBuilder;

public class SplitTableLogsDto	// class@0000af from classes.dex
{
    private Integer pageNo;
    private Integer pageSize;

    public void SplitTableLogsDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof SplitTableLogsDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof SplitTableLogsDto) {
             b = false;
          }else {
             SplitTableLogsDto splitTableLo = o;
             if (!splitTableLo.canEqual(this)) {
                b = false;
             }else {
                Integer pageNo = this.getPageNo();
                Integer pageNo1 = splitTableLo.getPageNo();
                if (pageNo == null) {
                   if (pageNo1 != null) {
                      b = false;
                   }
                }else if(pageNo.equals(pageNo1)){
                }
                Integer pageSize = this.getPageSize();
                Integer pageSize1 = splitTableLo.getPageSize();
                if (pageSize == null) {
                   if (pageSize1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!pageSize.equals(pageSize1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public Integer getPageNo(){
       return this.pageNo;
    }
    public Integer getPageSize(){
       return this.pageSize;
    }
    public int hashCode(){
       Integer $pageNo;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($pageNo = this.getPageNo()) == null)? i: $pageNo.hashCode();
       result = i1 + 59;
       Integer $pageSize = this.getPageSize();
       i1 = result * 59;
       if ($pageSize != null) {
          i = $pageSize.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setPageNo(Integer pageNo){
       this.pageNo = pageNo;
    }
    public void setPageSize(Integer pageSize){
       this.pageSize = pageSize;
    }
    public String toString(){
       return "SplitTableLogsDto\(pageNo="+this.getPageNo()+", pageSize="+this.getPageSize()+"\)";
    }
}
