package com.taikang.fly.check.dto.mapstruct.YbConsumablesListMappingImpl;
import com.taikang.fly.check.dto.mapstruct.YbConsumablesListMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.YbConsumablesList;
import com.taikang.fly.check.dto.ybconsumablesList.YbConsumablesListRespDto;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.Consumables;
import java.math.BigDecimal;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class YbConsumablesListMappingImpl implements YbConsumablesListMapping	// class@000184 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void YbConsumablesListMappingImpl(){
       super();
    }
    public YbConsumablesListRespDto entryToDto(YbConsumablesList ybConsumablesList){
       YbConsumablesListRespDto ybConsumable;
       if (ybConsumablesList == null) {
          ybConsumable = null;
       }else {
          ybConsumable = new YbConsumablesListRespDto();
          ybConsumable.setPaymentCategory(ybConsumablesList.getPaymentCategory());
          ybConsumable.setRemark(ybConsumablesList.getRemark());
       }
       return ybConsumable;
    }
    public YbConsumablesListRespDto entryToDtoList(Consumables ybConsumables){
       YbConsumablesListRespDto ybConsumable;
       if (ybConsumables == null) {
          ybConsumable = null;
       }else {
          ybConsumable = new YbConsumablesListRespDto();
          ybConsumable.setHospitalSelfPay(this.typeConversionMapper.BigDecimal2Percent(ybConsumables.getHospitalSelfPay()));
          ybConsumable.setOutpatientSelfPay(this.typeConversionMapper.BigDecimal2Percent(ybConsumables.getOutpatientSelfPay()));
          ybConsumable.setYourSelfPay(this.typeConversionMapper.BigDecimal2Percent(ybConsumables.getYourSelfPay()));
          ybConsumable.setThreeMaxPrice(this.typeConversionMapper.BigDecimal2String(ybConsumables.getThreeMaxPrice()));
          ybConsumable.setTwoMaxPrice(this.typeConversionMapper.BigDecimal2String(ybConsumables.getTwoMaxPrice()));
          ybConsumable.setStartTime(this.typeConversionMapper.DateTime2String(ybConsumables.getStartTime()));
          ybConsumable.setOneMaxPrice(this.typeConversionMapper.BigDecimal2String(ybConsumables.getOneMaxPrice()));
          ybConsumable.setEndTime(this.typeConversionMapper.DateTime2String(ybConsumables.getEndTime()));
          ybConsumable.setMaxPrice(this.typeConversionMapper.BigDecimal2String(ybConsumables.getMaxPrice()));
          ybConsumable.setWorkInjurySelfPay(this.typeConversionMapper.BigDecimal2Percent(ybConsumables.getWorkInjurySelfPay()));
          ybConsumable.setProCode(ybConsumables.getProCode());
          ybConsumable.setProName(ybConsumables.getProName());
          ybConsumable.setProConnotation(ybConsumables.getProConnotation());
          ybConsumable.setOutProInfo(ybConsumables.getOutProInfo());
          ybConsumable.setPayer(ybConsumables.getPayer());
          ybConsumable.setPaymentCategory(ybConsumables.getPaymentCategory());
          ybConsumable.setRemark(ybConsumables.getRemark());
       }
       return ybConsumable;
    }
    public List entryToDtoList(List ybConsumablesLists){
       List list;
       if (ybConsumablesLists == null) {
          list = null;
       }else {
          list = new ArrayList(ybConsumablesLists.size());
          Iterator iterator = ybConsumablesLists.iterator();
          while (iterator.hasNext()) {
             list.add(this.entryToDtoList(iterator.next()));
          }
       }
       return list;
    }
}
