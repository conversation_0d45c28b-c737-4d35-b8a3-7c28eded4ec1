package com.taikang.fly.check.rest.NationalStandardVersionController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.NationalStandardVersionDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.NationalStandardVersionService;

public class NationalStandardVersionController	// class@0002a3 from classes.dex
{
    private NationalStandardVersionService nationalStandardVersionService;
    private static final Logger log;

    static {
       NationalStandardVersionController.log = LoggerFactory.getLogger(NationalStandardVersionController.class);
    }
    public void NationalStandardVersionController(){
       super();
    }
    public RmpResponse queryCD10List(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       return RmpResponse.success(this.nationalStandardVersionService.queryICD10List(page, size, nationalStandardVersionDto));
    }
    public RmpResponse queryHerbalList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       return RmpResponse.success(this.nationalStandardVersionService.queryHerbalList(page, size, nationalStandardVersionDto));
    }
    public RmpResponse queryICD9List(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       return RmpResponse.success(this.nationalStandardVersionService.queryICD9List(page, size, nationalStandardVersionDto));
    }
    public RmpResponse queryPatentList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       return RmpResponse.success(this.nationalStandardVersionService.queryPatentList(page, size, nationalStandardVersionDto));
    }
    public RmpResponse queryTreatmentList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       return RmpResponse.success(this.nationalStandardVersionService.queryTreatmentList(page, size, nationalStandardVersionDto));
    }
    public RmpResponse queryWesternList(Integer page,Integer size,NationalStandardVersionDto nationalStandardVersionDto){
       return RmpResponse.success(this.nationalStandardVersionService.queryWesternList(page, size, nationalStandardVersionDto));
    }
}
