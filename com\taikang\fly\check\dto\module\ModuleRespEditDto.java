package com.taikang.fly.check.dto.module.ModuleRespEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModuleRespEditDto implements Serializable	// class@00019a from classes.dex
{
    private String description;
    private String icon;
    private String moduleCode;
    private String moduleName;
    private String moduleOrder;
    private String moduleType;
    private String url;
    private static final long serialVersionUID = 0x349d06aa3e3fa7be;

    public void ModuleRespEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModuleRespEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ModuleRespEditDto){
          b = false;
       }else {
          ModuleRespEditDto moduleRespEd = o;
          if (!moduleRespEd.canEqual(this)) {
             b = false;
          }else {
             String moduleCode = this.getModuleCode();
             String moduleCode1 = moduleRespEd.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String moduleName = this.getModuleName();
             String moduleName1 = moduleRespEd.getModuleName();
             if (moduleName == null) {
                if (moduleName1 != null) {
                   b = false;
                }
             }else if(moduleName.equals(moduleName1)){
             }
             String moduleOrder = this.getModuleOrder();
             String moduleOrder1 = moduleRespEd.getModuleOrder();
             if (moduleOrder == null) {
                if (moduleOrder1 != null) {
                   b = false;
                }
             }else if(moduleOrder.equals(moduleOrder1)){
             }
             String description = this.getDescription();
             String description1 = moduleRespEd.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String icon = this.getIcon();
             String icon1 = moduleRespEd.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String url = this.getUrl();
             String url1 = moduleRespEd.getUrl();
             if (url == null) {
                if (url1 != null) {
                label_0098 :
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String moduleType = this.getModuleType();
             String moduleType1 = moduleRespEd.getModuleType();
             if (moduleType == null) {
                if (moduleType1 != null) {
                   b = false;
                }
             }else if(moduleType.equals(moduleType1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDescription(){
       return this.description;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getModuleName(){
       return this.moduleName;
    }
    public String getModuleOrder(){
       return this.moduleOrder;
    }
    public String getModuleType(){
       return this.moduleType;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $moduleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($moduleCode = this.getModuleCode()) == null)? i: $moduleCode.hashCode();
       result = i1 + 59;
       String $moduleName = this.getModuleName();
       int i2 = result * 59;
       i1 = ($moduleName == null)? i: $moduleName.hashCode();
       result = i2 + i1;
       String $moduleOrder = this.getModuleOrder();
       i2 = result * 59;
       i1 = ($moduleOrder == null)? i: $moduleOrder.hashCode();
       result = i2 + i1;
       String $description = this.getDescription();
       i2 = result * 59;
       i1 = ($description == null)? i: $description.hashCode();
       result = i2 + i1;
       String $icon = this.getIcon();
       i2 = result * 59;
       i1 = ($icon == null)? i: $icon.hashCode();
       result = i2 + i1;
       String url = this.getUrl();
       i2 = result * 59;
       i1 = (url == null)? i: url.hashCode();
       String moduleType = this.getModuleType();
       i1 = (i2 + i1) * 59;
       if (moduleType != null) {
          i = moduleType.hashCode();
       }
       return (i1 + i);
    }
    public void setDescription(String description){
       this.description = description;
    }
    public void setIcon(String icon){
       this.icon = icon;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setModuleName(String moduleName){
       this.moduleName = moduleName;
    }
    public void setModuleOrder(String moduleOrder){
       this.moduleOrder = moduleOrder;
    }
    public void setModuleType(String moduleType){
       this.moduleType = moduleType;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ModuleRespEditDto\(moduleCode="+this.getModuleCode()+", moduleName="+this.getModuleName()+", moduleOrder="+this.getModuleOrder()+", description="+this.getDescription()+", icon="+this.getIcon()+", url="+this.getUrl()+", moduleType="+this.getModuleType()+"\)";
    }
}
