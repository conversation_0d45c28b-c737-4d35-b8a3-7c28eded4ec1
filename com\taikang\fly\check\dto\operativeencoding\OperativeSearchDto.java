package com.taikang.fly.check.dto.operativeencoding.OperativeSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class OperativeSearchDto implements Serializable	// class@00019d from classes.dex
{
    private String surgeryCode;
    private String surgeryName;
    private static final long serialVersionUID = 0x1;

    public void OperativeSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof OperativeSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof OperativeSearchDto) {
             b = false;
          }else {
             OperativeSearchDto operativeSea = o;
             if (!operativeSea.canEqual(this)) {
                b = false;
             }else {
                String surgeryCode = this.getSurgeryCode();
                String surgeryCode1 = operativeSea.getSurgeryCode();
                if (surgeryCode == null) {
                   if (surgeryCode1 != null) {
                      b = false;
                   }
                }else if(surgeryCode.equals(surgeryCode1)){
                }
                String surgeryName = this.getSurgeryName();
                String surgeryName1 = operativeSea.getSurgeryName();
                if (surgeryName == null) {
                   if (surgeryName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!surgeryName.equals(surgeryName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getSurgeryCode(){
       return this.surgeryCode;
    }
    public String getSurgeryName(){
       return this.surgeryName;
    }
    public int hashCode(){
       String $surgeryCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($surgeryCode = this.getSurgeryCode()) == null)? i: $surgeryCode.hashCode();
       result = i1 + 59;
       String $surgeryName = this.getSurgeryName();
       i1 = result * 59;
       if ($surgeryName != null) {
          i = $surgeryName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setSurgeryCode(String surgeryCode){
       this.surgeryCode = surgeryCode;
    }
    public void setSurgeryName(String surgeryName){
       this.surgeryName = surgeryName;
    }
    public String toString(){
       return "OperativeSearchDto\(surgeryCode="+this.getSurgeryCode()+", surgeryName="+this.getSurgeryName()+"\)";
    }
}
