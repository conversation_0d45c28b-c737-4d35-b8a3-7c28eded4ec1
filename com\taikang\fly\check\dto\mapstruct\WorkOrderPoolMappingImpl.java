package com.taikang.fly.check.dto.mapstruct.WorkOrderPoolMappingImpl;
import com.taikang.fly.check.dto.mapstruct.WorkOrderPoolMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolAddDto;
import com.taikang.fly.check.mybatis.domain.WorkOrderPool;
import java.lang.String;
import java.util.Date;
import com.taikang.fly.check.utils.DateUtils;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolImportDto;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.util.UUID;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolEditDto;
import java.text.SimpleDateFormat;
import java.lang.RuntimeException;
import java.lang.Throwable;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolRespDto;
import com.taikang.fly.check.mybatis.domain.FlyRule;

public class WorkOrderPoolMappingImpl implements WorkOrderPoolMapping	// class@000182 from classes.dex
{

    public void WorkOrderPoolMappingImpl(){
       super();
    }
    public WorkOrderPool addToEntry(WorkOrderPoolAddDto workOrderPoolAddDto){
       WorkOrderPool workOrderPoo;
       if (workOrderPoolAddDto == null) {
          workOrderPoo = null;
       }else {
          workOrderPoo = new WorkOrderPool();
          workOrderPoo.setRuleName(workOrderPoolAddDto.getRuleName());
          workOrderPoo.setRegion(workOrderPoolAddDto.getRegion());
          workOrderPoo.setRuleState(workOrderPoolAddDto.getRuleState());
          workOrderPoo.setRuleLevel(workOrderPoolAddDto.getRuleLevel());
          workOrderPoo.setRuleCategory1(workOrderPoolAddDto.getRuleCategory1());
          workOrderPoo.setRuleCategory2(workOrderPoolAddDto.getRuleCategory2());
          workOrderPoo.setDiagnosisType(workOrderPoolAddDto.getDiagnosisType());
          workOrderPoo.setRuleDescribe(workOrderPoolAddDto.getRuleDescribe());
          workOrderPoo.setRedField1(workOrderPoolAddDto.getRedField1());
          workOrderPoo.setPolicyBasis(workOrderPoolAddDto.getPolicyBasis());
          workOrderPoo.setIsSpecial(workOrderPoolAddDto.getIsSpecial());
          workOrderPoo.setRuleScopeApply(workOrderPoolAddDto.getRuleScopeApply());
          workOrderPoo.setRuleDimension(workOrderPoolAddDto.getRuleDimension());
          workOrderPoo.setCreatedTime(new Date());
          workOrderPoo.setRuleSuitTimeEnd(DateUtils.parseDateSupportNull(workOrderPoolAddDto.getRuleSuitTimeEnd()));
          workOrderPoo.setRuleSuitTimeStart(DateUtils.parseDateSupportNull(workOrderPoolAddDto.getRuleSuitTimeStart()));
          workOrderPoo.setDemandSource(workOrderPoolAddDto.getDemandSource());
          workOrderPoo.setOperateTime(new Date());
       }
       return workOrderPoo;
    }
    public WorkOrderPool dtoToDomain(WorkOrderPoolImportDto workOrderPoolRespDto){
       WorkOrderPool workOrderPoo;
       if (workOrderPoolRespDto == null) {
          workOrderPoo = null;
       }else {
          workOrderPoo = new WorkOrderPool();
          workOrderPoo.setRuleName(workOrderPoolRespDto.getRuleName());
          workOrderPoo.setRuleLevel(workOrderPoolRespDto.getRuleLevel());
          workOrderPoo.setRuleCategory1(workOrderPoolRespDto.getRuleCategory1());
          workOrderPoo.setDiagnosisType(workOrderPoolRespDto.getDiagnosisType());
          workOrderPoo.setRuleDescribe(workOrderPoolRespDto.getRuleDescribe());
          workOrderPoo.setRedField1(workOrderPoolRespDto.getRedField1());
          workOrderPoo.setDemandSource(workOrderPoolRespDto.getDemandSource());
          workOrderPoo.setPolicyBasis(workOrderPoolRespDto.getPolicyBasis());
          workOrderPoo.setIsSpecial(workOrderPoolRespDto.getIsSpecial());
          workOrderPoo.setRuleState("0");
          workOrderPoo.setRuleSuitTimeEnd(DateUtils.parseDate(workOrderPoolRespDto.getRuleSuitTimeEnd()));
          workOrderPoo.setRemoved("1");
          workOrderPoo.setRuleSuitTimeStart(DateUtils.parseDate(workOrderPoolRespDto.getRuleSuitTimeStart()));
          workOrderPoo.setOperateTime(new Date());
          workOrderPoo.setCreater(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          workOrderPoo.setCreatedTime(new Date());
          workOrderPoo.setId(UUID.randomUUID().toString());
          workOrderPoo.setRegion(ThreadLocalContextHolder.getContext().getUserInfo().getRegion());
          workOrderPoo.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
       }
       return workOrderPoo;
    }
    public List dtosToDomains(List workOrderPoolRespDtoList){
       List list;
       if (workOrderPoolRespDtoList == null) {
          list = null;
       }else {
          list = new ArrayList(workOrderPoolRespDtoList.size());
          Iterator iterator = workOrderPoolRespDtoList.iterator();
          while (iterator.hasNext()) {
             list.add(this.dtoToDomain(iterator.next()));
          }
       }
       return list;
    }
    public WorkOrderPool editToEntry(WorkOrderPoolEditDto workOrderPoolEditDto){
       WorkOrderPool workOrderPoo;
       if (workOrderPoolEditDto == null) {
          workOrderPoo = null;
       }else {
          workOrderPoo = new WorkOrderPool();
          workOrderPoo.setId(workOrderPoolEditDto.getId());
          workOrderPoo.setRuleName(workOrderPoolEditDto.getRuleName());
          workOrderPoo.setRegion(workOrderPoolEditDto.getRegion());
          String removed = workOrderPoolEditDto.getRemoved();
          try{
             workOrderPoo.setRemoved(removed);
             if (workOrderPoolEditDto.getCreatedTime() != null) {
                workOrderPoo.setCreatedTime(new SimpleDateFormat().parse(workOrderPoolEditDto.getCreatedTime()));
             }
             workOrderPoo.setRuleState(workOrderPoolEditDto.getRuleState());
             workOrderPoo.setCreater(workOrderPoolEditDto.getCreater());
             workOrderPoo.setRuleLevel(workOrderPoolEditDto.getRuleLevel());
             workOrderPoo.setRuleCategory1(workOrderPoolEditDto.getRuleCategory1());
             workOrderPoo.setRuleCategory2(workOrderPoolEditDto.getRuleCategory2());
             workOrderPoo.setDiagnosisType(workOrderPoolEditDto.getDiagnosisType());
             workOrderPoo.setRuleDescribe(workOrderPoolEditDto.getRuleDescribe());
             workOrderPoo.setRuleParameter(workOrderPoolEditDto.getRuleParameter());
             workOrderPoo.setRedField1(workOrderPoolEditDto.getRedField1());
             workOrderPoo.setRedField2(workOrderPoolEditDto.getRedField2());
             workOrderPoo.setRedField3(workOrderPoolEditDto.getRedField3());
             workOrderPoo.setSqlName(workOrderPoolEditDto.getSqlName());
             workOrderPoo.setDemandSource(workOrderPoolEditDto.getDemandSource());
             workOrderPoo.setPolicyBasis(workOrderPoolEditDto.getPolicyBasis());
             workOrderPoo.setIsSpecial(workOrderPoolEditDto.getIsSpecial());
             workOrderPoo.setRuleDimension(workOrderPoolEditDto.getRuleDimension());
             workOrderPoo.setRuleSuitTimeEnd(DateUtils.parseDateSupportNull(workOrderPoolEditDto.getRuleSuitTimeEnd()));
             workOrderPoo.setRuleSuitTimeStart(DateUtils.parseDateSupportNull(workOrderPoolEditDto.getRuleSuitTimeStart()));
             workOrderPoo.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
             workOrderPoo.setOperateTime(new Date());
          }catch(java.text.ParseException e0){
             throw new RuntimeException(e0);
          }
       }
       return workOrderPoo;
    }
    public List entityToDtos(List workOrderPools){
       List list;
       if (workOrderPools == null) {
          list = null;
       }else {
          list = new ArrayList(workOrderPools.size());
          Iterator iterator = workOrderPools.iterator();
          while (iterator.hasNext()) {
             list.add(this.entryToDto(iterator.next()));
          }
       }
       return list;
    }
    public WorkOrderPoolRespDto entryToDto(WorkOrderPool workOrderPool){
       WorkOrderPoolRespDto workOrderPoo;
       if (workOrderPool == null) {
          workOrderPoo = null;
       }else {
          workOrderPoo = new WorkOrderPoolRespDto();
          workOrderPoo.setId(workOrderPool.getId());
          workOrderPoo.setRuleName(workOrderPool.getRuleName());
          workOrderPoo.setOperator(workOrderPool.getOperator());
          workOrderPoo.setRegion(workOrderPool.getRegion());
          workOrderPoo.setRemoved(workOrderPool.getRemoved());
          workOrderPoo.setRuleState(workOrderPool.getRuleState());
          workOrderPoo.setCreater(workOrderPool.getCreater());
          workOrderPoo.setRuleLevel(workOrderPool.getRuleLevel());
          workOrderPoo.setRuleCategory1(workOrderPool.getRuleCategory1());
          workOrderPoo.setRuleCategory2(workOrderPool.getRuleCategory2());
          workOrderPoo.setDiagnosisType(workOrderPool.getDiagnosisType());
          workOrderPoo.setRuleDescribe(workOrderPool.getRuleDescribe());
          workOrderPoo.setRuleParameter(workOrderPool.getRuleParameter());
          workOrderPoo.setRedField1(workOrderPool.getRedField1());
          workOrderPoo.setRedField2(workOrderPool.getRedField2());
          workOrderPoo.setRedField3(workOrderPool.getRedField3());
          workOrderPoo.setSqlName(workOrderPool.getSqlName());
          workOrderPoo.setDemandSource(workOrderPool.getDemandSource());
          workOrderPoo.setReceiver(workOrderPool.getReceiver());
          workOrderPoo.setSchedule(workOrderPool.getSchedule());
          workOrderPoo.setResultsEnforcement(workOrderPool.getResultsEnforcement());
          workOrderPoo.setIsSpecial(workOrderPool.getIsSpecial());
          workOrderPoo.setRuleScopeApply(workOrderPool.getRuleScopeApply());
          workOrderPoo.setRuleDimension(workOrderPool.getRuleDimension());
          workOrderPoo.setPolicyBasis(workOrderPool.getPolicyBasis());
          workOrderPoo.setCreatedTime(DateUtils.format(workOrderPool.getCreatedTime()));
          workOrderPoo.setRuleSuitTimeEnd(DateUtils.formatDefaultPatternDate(workOrderPool.getRuleSuitTimeEnd()));
          workOrderPoo.setRuleSuitTimeStart(DateUtils.formatDefaultPatternDate(workOrderPool.getRuleSuitTimeStart()));
          workOrderPoo.setOperateTime(DateUtils.format(workOrderPool.getOperateTime()));
       }
       return workOrderPoo;
    }
    public FlyRule workOrderPoolToFlyRule(WorkOrderPool workOrderPool){
       FlyRule uFlyRule;
       if (workOrderPool == null) {
          uFlyRule = null;
       }else {
          uFlyRule = new FlyRule();
          uFlyRule.setId(workOrderPool.getId());
          uFlyRule.setRuleName(workOrderPool.getRuleName());
          uFlyRule.setRegion(workOrderPool.getRegion());
          uFlyRule.setRemoved(workOrderPool.getRemoved());
          uFlyRule.setSqlName(workOrderPool.getSqlName());
          uFlyRule.setDataSources(workOrderPool.getDataSources());
          uFlyRule.setResultsEnforcement(workOrderPool.getResultsEnforcement());
          uFlyRule.setRuleCategory1(workOrderPool.getRuleCategory1());
          uFlyRule.setRuleCategory2(workOrderPool.getRuleCategory2());
          uFlyRule.setDiagnosisType(workOrderPool.getDiagnosisType());
          uFlyRule.setRuleDescribe(workOrderPool.getRuleDescribe());
          uFlyRule.setRuleLevel(workOrderPool.getRuleLevel());
          uFlyRule.setRuleParameter(workOrderPool.getRuleParameter());
          uFlyRule.setRedField1(workOrderPool.getRedField1());
          uFlyRule.setRedField2(workOrderPool.getRedField2());
          uFlyRule.setRedField3(workOrderPool.getRedField3());
          uFlyRule.setPolicyBasis(workOrderPool.getPolicyBasis());
          uFlyRule.setIsSpecial(workOrderPool.getIsSpecial());
          uFlyRule.setRuleSuitTimeStart(workOrderPool.getRuleSuitTimeStart());
          uFlyRule.setRuleSuitTimeEnd(workOrderPool.getRuleSuitTimeEnd());
          uFlyRule.setRuleScopeApply(workOrderPool.getRuleScopeApply());
          uFlyRule.setRuleDimension(workOrderPool.getRuleDimension());
          uFlyRule.setCreater(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRule.setCreatedTime(new Date());
          uFlyRule.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRule.setOperateTime(new Date());
       }
       return uFlyRule;
    }
}
