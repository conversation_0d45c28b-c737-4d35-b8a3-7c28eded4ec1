"""
计划执行器模块
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

from loguru import logger
from sqlalchemy.orm import Session

from flycheck.models import Plan, PlanRule, PlanLog, FlyRule
from flycheck.core.rule_engine import <PERSON><PERSON><PERSON><PERSON>, RuleResult, RuleStatus
from flycheck.config import get_settings

settings = get_settings()


class PlanExecutor:
    """计划执行器"""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.rule_engine = RuleEngine(db_session)
        self.max_workers = settings.max_concurrent_rules
    
    def execute_plan(self, plan_id: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行检查计划"""
        logger.info(f"开始执行计划: {plan_id}")
        
        # 获取计划信息
        plan = self.db_session.query(Plan).filter(Plan.id == plan_id).first()
        if not plan:
            raise ValueError(f"计划不存在: {plan_id}")
        
        if plan.status != "active":
            raise ValueError(f"计划状态不正确: {plan.status}")
        
        # 获取计划关联的规则
        plan_rules = (
            self.db_session.query(PlanRule)
            .filter(PlanRule.plan_id == plan_id)
            .order_by(PlanRule.execution_order)
            .all()
        )
        
        if not plan_rules:
            raise ValueError("计划中没有关联的规则")
        
        # 创建计划执行日志
        plan_log = PlanLog(
            plan_id=plan_id,
            execution_start=datetime.now(),
            execution_status="running",
            execution_detail="计划开始执行"
        )
        self.db_session.add(plan_log)
        self.db_session.commit()
        
        try:
            # 执行规则
            results = self._execute_rules(plan_rules, params or {})
            
            # 统计结果
            summary = self._summarize_results(results)
            
            # 更新计划状态
            plan.completed_rules = summary['completed_rules']
            plan.error_rules = summary['error_rules']
            
            # 更新计划日志
            plan_log.execution_end = datetime.now()
            plan_log.execution_status = "completed"
            plan_log.total_records = summary['total_records']
            plan_log.error_records = summary['error_records']
            plan_log.warning_records = summary['warning_records']
            plan_log.result_data = summary
            
            self.db_session.commit()
            
            logger.info(f"计划执行完成: {plan_id}")
            return summary
            
        except Exception as e:
            logger.error(f"计划执行失败: {plan_id}, 错误: {str(e)}")
            
            # 更新计划日志
            plan_log.execution_end = datetime.now()
            plan_log.execution_status = "error"
            plan_log.error_message = str(e)
            
            self.db_session.commit()
            raise
    
    def _execute_rules(self, plan_rules: List[PlanRule], params: Dict[str, Any]) -> List[RuleResult]:
        """执行规则列表"""
        results = []
        
        # 获取所有规则
        rule_ids = [pr.rule_id for pr in plan_rules]
        rules = (
            self.db_session.query(FlyRule)
            .filter(FlyRule.id.in_(rule_ids))
            .all()
        )
        
        rule_dict = {rule.id: rule for rule in rules}
        
        # 并发执行规则
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_rule = {}
            for plan_rule in plan_rules:
                rule = rule_dict.get(plan_rule.rule_id)
                if rule:
                    # 合并参数
                    rule_params = params.copy()
                    if plan_rule.rule_params:
                        rule_params.update(plan_rule.rule_params)
                    
                    future = executor.submit(self.rule_engine.execute_rule, rule, rule_params)
                    future_to_rule[future] = (plan_rule, rule)
            
            # 收集结果
            for future in as_completed(future_to_rule):
                plan_rule, rule = future_to_rule[future]
                try:
                    result = future.result(timeout=settings.rule_timeout)
                    results.append(result)
                    
                    # 更新计划规则状态
                    plan_rule.status = result.status.value
                    
                except Exception as e:
                    logger.error(f"规则执行异常: {rule.rule_name}, 错误: {str(e)}")
                    
                    error_result = RuleResult(
                        rule_id=rule.id,
                        status=RuleStatus.ERROR,
                        error_message=str(e)
                    )
                    results.append(error_result)
                    
                    # 更新计划规则状态
                    plan_rule.status = "error"
        
        self.db_session.commit()
        return results
    
    def _summarize_results(self, results: List[RuleResult]) -> Dict[str, Any]:
        """汇总执行结果"""
        summary = {
            'total_rules': len(results),
            'completed_rules': 0,
            'error_rules': 0,
            'total_records': 0,
            'error_records': 0,
            'warning_records': 0,
            'total_execution_time': 0.0,
            'rule_results': []
        }
        
        for result in results:
            if result.status == RuleStatus.COMPLETED:
                summary['completed_rules'] += 1
            elif result.status == RuleStatus.ERROR:
                summary['error_rules'] += 1
            
            summary['total_records'] += result.total_records
            summary['error_records'] += result.error_records
            summary['warning_records'] += result.warning_records
            summary['total_execution_time'] += result.execution_time
            
            summary['rule_results'].append({
                'rule_id': result.rule_id,
                'status': result.status.value,
                'total_records': result.total_records,
                'error_records': result.error_records,
                'execution_time': result.execution_time,
                'error_message': result.error_message,
                'result_file_path': result.result_file_path
            })
        
        return summary
    
    def stop_plan(self, plan_id: str) -> bool:
        """停止计划执行"""
        # 这里可以实现计划停止逻辑
        # 由于使用了ThreadPoolExecutor，可以通过设置标志位来实现
        logger.info(f"停止计划执行: {plan_id}")
        return True
    
    def get_plan_status(self, plan_id: str) -> Dict[str, Any]:
        """获取计划执行状态"""
        plan = self.db_session.query(Plan).filter(Plan.id == plan_id).first()
        if not plan:
            raise ValueError(f"计划不存在: {plan_id}")
        
        # 获取最新的执行日志
        latest_log = (
            self.db_session.query(PlanLog)
            .filter(PlanLog.plan_id == plan_id)
            .order_by(PlanLog.created_time.desc())
            .first()
        )
        
        # 获取规则执行状态
        plan_rules = (
            self.db_session.query(PlanRule)
            .filter(PlanRule.plan_id == plan_id)
            .all()
        )
        
        rule_status = {}
        for pr in plan_rules:
            rule_status[pr.rule_id] = pr.status
        
        return {
            'plan_id': plan_id,
            'plan_name': plan.plan_name,
            'plan_status': plan.status,
            'total_rules': plan.total_rules,
            'completed_rules': plan.completed_rules,
            'error_rules': plan.error_rules,
            'latest_execution': {
                'start_time': latest_log.execution_start if latest_log else None,
                'end_time': latest_log.execution_end if latest_log else None,
                'status': latest_log.execution_status if latest_log else None,
                'total_records': latest_log.total_records if latest_log else 0,
                'error_records': latest_log.error_records if latest_log else 0,
            },
            'rule_status': rule_status
        }
