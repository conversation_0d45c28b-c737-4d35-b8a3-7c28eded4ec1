package com.taikang.fly.check.utils.BigFileReader$StartEndPair;
import java.lang.Object;
import com.taikang.fly.check.utils.BigFileReader$1;
import java.lang.Class;
import java.lang.String;
import java.lang.StringBuilder;

class BigFileReader$StartEndPair	// class@00032a from classes.dex
{
    private long end;
    private long start;

    private void BigFileReader$StartEndPair(){
       super();
    }
    void BigFileReader$StartEndPair(BigFileReader$1 x0){
       super();
    }
    static long access$100(BigFileReader$StartEndPair x0){
       return x0.start;
    }
    static long access$102(BigFileReader$StartEndPair x0,long x1){
       x0.start = x1;
       return x1;
    }
    static long access$200(BigFileReader$StartEndPair x0){
       return x0.end;
    }
    static long access$202(BigFileReader$StartEndPair x0,long x1){
       x0.end = x1;
       return x1;
    }
    public boolean equals(Object obj){
       boolean b = true;
       if (this != obj) {
          if (obj == null) {
             b = false;
          }else if(this.getClass() != obj.getClass()){
             b = false;
          }else {
             BigFileReader$StartEndPair startEndPair = obj;
             if (this.end - startEndPair.end) {
                b = false;
             }else if(this.start - startEndPair.start){
                b = false;
             }
          }
       }
       return b;
    }
    public int hashCode(){
       int prime = 31;
       int result = 1;
       result = (int)(this.end ^ (this.end >> 32)) + 31;
       result = (result * 31) + (int)(this.start ^ (this.start >> 32));
       return result;
    }
    public String toString(){
       return "star="+this.start+";end="+this.end;
    }
}
