package com.taikang.fly.check.dto.mapstruct.ClickhouseFlyRuleMapping;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleAddDto;
import com.taikang.fly.check.mybatis.domain.ClickhouseFlyRule;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleAddTemplateDto;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleRespDto;
import java.util.List;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleEditDto;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseWorkOrderEditDto;
import com.taikang.fly.check.dto.clickhouseFlyRule.TemplateClickhouseEditDto;

public interface abstract ClickhouseFlyRuleMapping	// class@000140 from classes.dex
{

    ClickhouseFlyRule addDtoToDomain(ClickhouseFlyRuleAddDto p0);
    ClickhouseFlyRule addRuleDtoToDomain(ClickhouseFlyRuleAddDto p0);
    ClickhouseFlyRule addTemplateDtoToDomain(ClickhouseFlyRuleAddTemplateDto p0);
    ClickhouseFlyRuleRespDto domainToInfoDto(ClickhouseFlyRule p0);
    ClickhouseFlyRuleAddDto domainToaddDto(ClickhouseFlyRule p0);
    List dtoListToDomain(List p0);
    ClickhouseFlyRule editDtoToDomain(ClickhouseFlyRuleEditDto p0,ClickhouseFlyRule p1);
    ClickhouseFlyRule editWorkOrderDtoToDomain(ClickhouseWorkOrderEditDto p0,ClickhouseFlyRule p1);
    ClickhouseFlyRule templateEditDtoToDomain(TemplateClickhouseEditDto p0,ClickhouseFlyRule p1);
}
