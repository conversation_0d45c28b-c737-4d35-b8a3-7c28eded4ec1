package com.taikang.fly.check.dto.ProvincialPlatformDto;
import java.lang.String;
import java.lang.Object;
import java.lang.StringBuilder;

public class ProvincialPlatformDto	// class@0000a9 from classes.dex
{
    String flag;
    String recordNum;
    String tableName;

    public void ProvincialPlatformDto(String tableName,String recordNum,String flag){
       super();
       this.tableName = tableName;
       this.recordNum = recordNum;
       this.flag = flag;
    }
    protected boolean canEqual(Object other){
       return other instanceof ProvincialPlatformDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ProvincialPlatformDto) {
             b = false;
          }else {
             ProvincialPlatformDto provincialPl = o;
             if (!provincialPl.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = provincialPl.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String recordNum = this.getRecordNum();
                String recordNum1 = provincialPl.getRecordNum();
                if (recordNum == null) {
                   if (recordNum1 != null) {
                      b = false;
                   }
                }else if(recordNum.equals(recordNum1)){
                }
                String flag = this.getFlag();
                String flag1 = provincialPl.getFlag();
                if (flag == null) {
                   if (flag1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!flag.equals(flag1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFlag(){
       return this.flag;
    }
    public String getRecordNum(){
       return this.recordNum;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       String $recordNum = this.getRecordNum();
       int i2 = result * 59;
       i1 = ($recordNum == null)? i: $recordNum.hashCode();
       result = i2 + i1;
       String $flag = this.getFlag();
       i1 = result * 59;
       if ($flag != null) {
          i = $flag.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFlag(String flag){
       this.flag = flag;
    }
    public void setRecordNum(String recordNum){
       this.recordNum = recordNum;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "ProvincialPlatformDto\(tableName="+this.getTableName()+", recordNum="+this.getRecordNum()+", flag="+this.getFlag()+"\)";
    }
}
