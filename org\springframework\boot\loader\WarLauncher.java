package org.springframework.boot.loader.WarLauncher;
import org.springframework.boot.loader.ExecutableArchiveLauncher;
import org.springframework.boot.loader.archive.Archive;
import java.lang.String;
import org.springframework.boot.loader.archive.Archive$Entry;
import java.lang.Object;

public class WarLauncher extends ExecutableArchiveLauncher	// class@000538 from classes.dex
{
    private static final String WEB_INF = "WEB-INF/";
    private static final String WEB_INF_CLASSES = "WEB-INF/classes/";
    private static final String WEB_INF_LIB = "WEB-INF/lib/";
    private static final String WEB_INF_LIB_PROVIDED = "WEB-INF/lib-provided/";

    public void WarLauncher(){
       super();
    }
    protected void WarLauncher(Archive archive){
       super(archive);
    }
    public static void main(String[] args){
       new WarLauncher().launch(args);
    }
    public boolean isNestedArchive(Archive$Entry entry){
       boolean b;
       if (entry.isDirectory()) {
          b = entry.getName().equals("WEB-INF/classes/");
       }else if(!entry.getName().startsWith("WEB-INF/lib/") && !entry.getName().startsWith("WEB-INF/lib-provided/")){
          b = false;
       }else {
          b = true;
       }
       return b;
    }
}
