package com.taikang.fly.check.dto.templateInfo.TemplateInfoAddDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TemplateInfoAddDto	// class@0001bb from classes.dex
{
    private String content;
    private String explain;
    private String fieldItems;
    private String manufacturer;
    private String sourceTableName;
    private String tableName;
    private String whereCondition;

    public void TemplateInfoAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TemplateInfoAddDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof TemplateInfoAddDto){
          b = false;
       }else {
          TemplateInfoAddDto templateInfo = o;
          if (!templateInfo.canEqual(this)) {
             b = false;
          }else {
             String tableName = this.getTableName();
             String tableName1 = templateInfo.getTableName();
             if (tableName == null) {
                if (tableName1 != null) {
                   b = false;
                }
             }else if(tableName.equals(tableName1)){
             }
             String content = this.getContent();
             String content1 = templateInfo.getContent();
             if (content == null) {
                if (content1 != null) {
                   b = false;
                }
             }else if(content.equals(content1)){
             }
             String fieldItems = this.getFieldItems();
             String fieldItems1 = templateInfo.getFieldItems();
             if (fieldItems == null) {
                if (fieldItems1 != null) {
                   b = false;
                }
             }else if(fieldItems.equals(fieldItems1)){
             }
             String sourceTableN = this.getSourceTableName();
             String sourceTableN1 = templateInfo.getSourceTableName();
             if (sourceTableN == null) {
                if (sourceTableN1 != null) {
                   b = false;
                }
             }else if(sourceTableN.equals(sourceTableN1)){
             }
             String whereConditi = this.getWhereCondition();
             String whereConditi1 = templateInfo.getWhereCondition();
             if (whereConditi == null) {
                if (whereConditi1 != null) {
                   b = false;
                }
             }else if(whereConditi.equals(whereConditi1)){
             }
             String manufacturer = this.getManufacturer();
             String manufacturer1 = templateInfo.getManufacturer();
             if (manufacturer == null) {
                if (manufacturer1 != null) {
                   b = false;
                }
             }else if(manufacturer.equals(manufacturer1)){
             }
             String explain = this.getExplain();
             String explain1 = templateInfo.getExplain();
             if (explain == null) {
                if (explain1 != null) {
                label_00b0 :
                   b = false;
                }
             }else if(explain.equals(explain1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getContent(){
       return this.content;
    }
    public String getExplain(){
       return this.explain;
    }
    public String getFieldItems(){
       return this.fieldItems;
    }
    public String getManufacturer(){
       return this.manufacturer;
    }
    public String getSourceTableName(){
       return this.sourceTableName;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getWhereCondition(){
       return this.whereCondition;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       String $content = this.getContent();
       int i2 = result * 59;
       i1 = ($content == null)? i: $content.hashCode();
       result = i2 + i1;
       String $fieldItems = this.getFieldItems();
       i2 = result * 59;
       i1 = ($fieldItems == null)? i: $fieldItems.hashCode();
       result = i2 + i1;
       String $sourceTableName = this.getSourceTableName();
       i2 = result * 59;
       i1 = ($sourceTableName == null)? i: $sourceTableName.hashCode();
       result = i2 + i1;
       String $whereCondition = this.getWhereCondition();
       i2 = result * 59;
       i1 = ($whereCondition == null)? i: $whereCondition.hashCode();
       result = i2 + i1;
       String manufacturer = this.getManufacturer();
       i2 = result * 59;
       i1 = (manufacturer == null)? i: manufacturer.hashCode();
       String explain = this.getExplain();
       i1 = (i2 + i1) * 59;
       if (explain != null) {
          i = explain.hashCode();
       }
       return (i1 + i);
    }
    public void setContent(String content){
       this.content = content;
    }
    public void setExplain(String explain){
       this.explain = explain;
    }
    public void setFieldItems(String fieldItems){
       this.fieldItems = fieldItems;
    }
    public void setManufacturer(String manufacturer){
       this.manufacturer = manufacturer;
    }
    public void setSourceTableName(String sourceTableName){
       this.sourceTableName = sourceTableName;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public void setWhereCondition(String whereCondition){
       this.whereCondition = whereCondition;
    }
    public String toString(){
       return "TemplateInfoAddDto\(tableName="+this.getTableName()+", content="+this.getContent()+", fieldItems="+this.getFieldItems()+", sourceTableName="+this.getSourceTableName()+", whereCondition="+this.getWhereCondition()+", manufacturer="+this.getManufacturer()+", explain="+this.getExplain()+"\)";
    }
}
