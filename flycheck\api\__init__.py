"""
API路由模块
"""

from fastapi import APIRouter

from .fly_rule import router as fly_rule_router
from .plan import router as plan_router
from .medical import router as medical_router
from .system import router as system_router

# 创建主路由
api_router = APIRouter()

# 注册子路由
api_router.include_router(fly_rule_router, prefix="/rules", tags=["飞行检查规则"])
api_router.include_router(plan_router, prefix="/plans", tags=["检查计划"])
api_router.include_router(medical_router, prefix="/medical", tags=["医保基础数据"])
api_router.include_router(system_router, prefix="/system", tags=["系统管理"])

__all__ = ["api_router"]
