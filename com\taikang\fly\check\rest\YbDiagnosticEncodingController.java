package com.taikang.fly.check.rest.YbDiagnosticEncodingController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.diagnosticencoding.DiagnosticSearchDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.YbDiagnosticEncodingService;

public class YbDiagnosticEncodingController	// class@0002bc from classes.dex
{
    private YbDiagnosticEncodingService ybDiagnosticEncodingService;
    private static final Logger log;

    static {
       YbDiagnosticEncodingController.log = LoggerFactory.getLogger(YbDiagnosticEncodingController.class);
    }
    public void YbDiagnosticEncodingController(){
       super();
    }
    public RmpResponse queryByPid(Integer page,Integer size,DiagnosticSearchDto diagnosticSearchDto){
       return RmpResponse.success(this.ybDiagnosticEncodingService.queryList(page, size, diagnosticSearchDto));
    }
}
