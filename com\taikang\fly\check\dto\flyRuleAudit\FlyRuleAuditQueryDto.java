package com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditQueryDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleAuditQueryDto	// class@000110 from classes.dex
{
    private String diagnosisType;
    private String region;
    private String ruleLevel;
    private String ruleName;
    private String ruleScopeApply;
    private String updateStatus;

    public void FlyRuleAuditQueryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleAuditQueryDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleAuditQueryDto){
          b = false;
       }else {
          FlyRuleAuditQueryDto uFlyRuleAudi = o;
          if (!uFlyRuleAudi.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleAudi.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleAudi.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleAudi.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleAudi.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = uFlyRuleAudi.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String updateStatus = this.getUpdateStatus();
             String updateStatus1 = uFlyRuleAudi.getUpdateStatus();
             if (updateStatus == null) {
                if (updateStatus1 != null) {
                   b = false;
                }
             }else if(updateStatus.equals(updateStatus1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public String getUpdateStatus(){
       return this.updateStatus;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $region = this.getRegion();
       int i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $ruleLevel = this.getRuleLevel();
       i2 = result * 59;
       i1 = ($ruleLevel == null)? i: $ruleLevel.hashCode();
       result = i2 + i1;
       String $diagnosisType = this.getDiagnosisType();
       i2 = result * 59;
       i1 = ($diagnosisType == null)? i: $diagnosisType.hashCode();
       result = i2 + i1;
       String $ruleScopeApply = this.getRuleScopeApply();
       i2 = result * 59;
       i1 = ($ruleScopeApply == null)? i: $ruleScopeApply.hashCode();
       result = i2 + i1;
       String updateStatus = this.getUpdateStatus();
       i1 = result * 59;
       if (updateStatus != null) {
          i = updateStatus.hashCode();
       }
       return (i1 + i);
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setUpdateStatus(String updateStatus){
       this.updateStatus = updateStatus;
    }
    public String toString(){
       return "FlyRuleAuditQueryDto\(ruleName="+this.getRuleName()+", region="+this.getRegion()+", ruleLevel="+this.getRuleLevel()+", diagnosisType="+this.getDiagnosisType()+", ruleScopeApply="+this.getRuleScopeApply()+", updateStatus="+this.getUpdateStatus()+"\)";
    }
}
