package com.taikang.fly.check.dto.mapstruct.DiagnosticEncodingMappingImpl;
import com.taikang.fly.check.dto.mapstruct.DiagnosticEncodingMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.GlDiagnosticEncoding;
import com.taikang.fly.check.dto.diagnosticencoding.DiagnosticEncodingDto;
import java.util.Date;
import java.lang.String;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.mybatis.domain.YbDiagnosticEncoding;

public class DiagnosticEncodingMappingImpl implements DiagnosticEncodingMapping	// class@000149 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void DiagnosticEncodingMappingImpl(){
       super();
    }
    public DiagnosticEncodingDto toGlDto(GlDiagnosticEncoding glDiagnosticEncoding){
       DiagnosticEncodingDto uDiagnosticE;
       if (glDiagnosticEncoding == null) {
          uDiagnosticE = null;
       }else {
          uDiagnosticE = new DiagnosticEncodingDto();
          uDiagnosticE.setCreateTime(this.typeConversionMapper.DateTime2String(glDiagnosticEncoding.getCreateTime()));
          uDiagnosticE.setModifyTime(this.typeConversionMapper.DateTime2String(glDiagnosticEncoding.getModifyTime()));
          uDiagnosticE.setId(glDiagnosticEncoding.getId());
          uDiagnosticE.setDiagnosisCode(glDiagnosticEncoding.getDiagnosisCode());
          uDiagnosticE.setDiagnosisName(glDiagnosticEncoding.getDiagnosisName());
          uDiagnosticE.setCreator(glDiagnosticEncoding.getCreator());
          uDiagnosticE.setModby(glDiagnosticEncoding.getModby());
       }
       return uDiagnosticE;
    }
    public List toGlDtoList(List dictEntryDtos){
       List list;
       if (dictEntryDtos == null) {
          list = null;
       }else {
          list = new ArrayList(dictEntryDtos.size());
          Iterator iterator = dictEntryDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toGlDto(iterator.next()));
          }
       }
       return list;
    }
    public GlDiagnosticEncoding toGlEntity(DiagnosticEncodingDto diagnosticEncodingDto){
       GlDiagnosticEncoding glDiagnostic;
       if (diagnosticEncodingDto == null) {
          glDiagnostic = null;
       }else {
          glDiagnostic = new GlDiagnosticEncoding();
          glDiagnostic.setCreateTime(this.typeConversionMapper.String2Time(diagnosticEncodingDto.getCreateTime()));
          glDiagnostic.setModifyTime(this.typeConversionMapper.String2Time(diagnosticEncodingDto.getModifyTime()));
          glDiagnostic.setId(diagnosticEncodingDto.getId());
          glDiagnostic.setDiagnosisCode(diagnosticEncodingDto.getDiagnosisCode());
          glDiagnostic.setDiagnosisName(diagnosticEncodingDto.getDiagnosisName());
          glDiagnostic.setCreator(diagnosticEncodingDto.getCreator());
          glDiagnostic.setModby(diagnosticEncodingDto.getModby());
       }
       return glDiagnostic;
    }
    public List toGlEntityList(List diagnosticEncodingDtos){
       List list;
       if (diagnosticEncodingDtos == null) {
          list = null;
       }else {
          list = new ArrayList(diagnosticEncodingDtos.size());
          Iterator iterator = diagnosticEncodingDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toGlEntity(iterator.next()));
          }
       }
       return list;
    }
    public DiagnosticEncodingDto toYbDto(YbDiagnosticEncoding ybDiagnosticEncoding){
       DiagnosticEncodingDto uDiagnosticE;
       if (ybDiagnosticEncoding == null) {
          uDiagnosticE = null;
       }else {
          uDiagnosticE = new DiagnosticEncodingDto();
          uDiagnosticE.setCreateTime(this.typeConversionMapper.DateTime2String(ybDiagnosticEncoding.getCreateTime()));
          uDiagnosticE.setModifyTime(this.typeConversionMapper.DateTime2String(ybDiagnosticEncoding.getModifyTime()));
          uDiagnosticE.setId(ybDiagnosticEncoding.getId());
          uDiagnosticE.setDiagnosisCode(ybDiagnosticEncoding.getDiagnosisCode());
          uDiagnosticE.setDiagnosisName(ybDiagnosticEncoding.getDiagnosisName());
          uDiagnosticE.setCreator(ybDiagnosticEncoding.getCreator());
          uDiagnosticE.setModby(ybDiagnosticEncoding.getModby());
       }
       return uDiagnosticE;
    }
    public List toYbDtoList(List ybDiagnosticEncodings){
       List list;
       if (ybDiagnosticEncodings == null) {
          list = null;
       }else {
          list = new ArrayList(ybDiagnosticEncodings.size());
          Iterator iterator = ybDiagnosticEncodings.iterator();
          while (iterator.hasNext()) {
             list.add(this.toYbDto(iterator.next()));
          }
       }
       return list;
    }
    public YbDiagnosticEncoding toYbEntity(DiagnosticEncodingDto diagnosticEncodingDto){
       YbDiagnosticEncoding ybDiagnostic;
       if (diagnosticEncodingDto == null) {
          ybDiagnostic = null;
       }else {
          ybDiagnostic = new YbDiagnosticEncoding();
          ybDiagnostic.setCreateTime(this.typeConversionMapper.String2Time(diagnosticEncodingDto.getCreateTime()));
          ybDiagnostic.setModifyTime(this.typeConversionMapper.String2Time(diagnosticEncodingDto.getModifyTime()));
          ybDiagnostic.setId(diagnosticEncodingDto.getId());
          ybDiagnostic.setDiagnosisCode(diagnosticEncodingDto.getDiagnosisCode());
          ybDiagnostic.setDiagnosisName(diagnosticEncodingDto.getDiagnosisName());
          ybDiagnostic.setCreator(diagnosticEncodingDto.getCreator());
          ybDiagnostic.setModby(diagnosticEncodingDto.getModby());
       }
       return ybDiagnostic;
    }
    public List toYbEntityList(List diagnosticEncodingDtos){
       List list;
       if (diagnosticEncodingDtos == null) {
          list = null;
       }else {
          list = new ArrayList(diagnosticEncodingDtos.size());
          Iterator iterator = diagnosticEncodingDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toYbEntity(iterator.next()));
          }
       }
       return list;
    }
}
