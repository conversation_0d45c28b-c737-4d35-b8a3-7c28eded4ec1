package com.taikang.fly.check.vo.drg.DrgFeeOfDepartmentVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgFeeOfDepartmentVo	// class@000366 from classes.dex
{
    private String adrgCode;
    private String department;
    private String disease;
    private String drgCode;
    private String year;

    public void DrgFeeOfDepartmentVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgFeeOfDepartmentVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgFeeOfDepartmentVo) {
             b = false;
          }else {
             DrgFeeOfDepartmentVo uDrgFeeOfDep = o;
             if (!uDrgFeeOfDep.canEqual(this)) {
                b = false;
             }else {
                String drgCode = this.getDrgCode();
                String drgCode1 = uDrgFeeOfDep.getDrgCode();
                if (drgCode == null) {
                   if (drgCode1 != null) {
                      b = false;
                   }
                }else if(drgCode.equals(drgCode1)){
                }
                String year = this.getYear();
                String year1 = uDrgFeeOfDep.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String department = this.getDepartment();
                String department1 = uDrgFeeOfDep.getDepartment();
                if (department == null) {
                   if (department1 != null) {
                      b = false;
                   }
                }else if(department.equals(department1)){
                }
                String disease = this.getDisease();
                String disease1 = uDrgFeeOfDep.getDisease();
                if (disease == null) {
                   if (disease1 != null) {
                      b = false;
                   }
                }else if(disease.equals(disease1)){
                }
                String adrgCode = this.getAdrgCode();
                String adrgCode1 = uDrgFeeOfDep.getAdrgCode();
                if (adrgCode == null) {
                   if (adrgCode1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!adrgCode.equals(adrgCode1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getAdrgCode(){
       return this.adrgCode;
    }
    public String getDepartment(){
       return this.department;
    }
    public String getDisease(){
       return this.disease;
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $drgCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgCode = this.getDrgCode()) == null)? i: $drgCode.hashCode();
       result = i1 + 59;
       String $year = this.getYear();
       int i2 = result * 59;
       i1 = ($year == null)? i: $year.hashCode();
       result = i2 + i1;
       String $department = this.getDepartment();
       i2 = result * 59;
       i1 = ($department == null)? i: $department.hashCode();
       result = i2 + i1;
       String $disease = this.getDisease();
       i2 = result * 59;
       i1 = ($disease == null)? i: $disease.hashCode();
       result = i2 + i1;
       String $adrgCode = this.getAdrgCode();
       i1 = result * 59;
       if ($adrgCode != null) {
          i = $adrgCode.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setAdrgCode(String adrgCode){
       this.adrgCode = adrgCode;
    }
    public void setDepartment(String department){
       this.department = department;
    }
    public void setDisease(String disease){
       this.disease = disease;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "DrgFeeOfDepartmentVo\(drgCode="+this.getDrgCode()+", year="+this.getYear()+", department="+this.getDepartment()+", disease="+this.getDisease()+", adrgCode="+this.getAdrgCode()+"\)";
    }
}
