package com.taikang.fly.check.dto.hospitaldatapool.SubjectDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class SubjectDto implements Serializable	// class@000131 from classes.dex
{
    private String moneySubjName;
    private String numberSubjName;
    private String subjectCount;
    private String subjectSummary;
    private String year;
    private static final long serialVersionUID = 0x1;

    public void SubjectDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof SubjectDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof SubjectDto) {
             b = false;
          }else {
             SubjectDto subjectDto = o;
             if (!subjectDto.canEqual(this)) {
                b = false;
             }else {
                String year = this.getYear();
                String year1 = subjectDto.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String numberSubjNa = this.getNumberSubjName();
                String numberSubjNa1 = subjectDto.getNumberSubjName();
                if (numberSubjNa == null) {
                   if (numberSubjNa1 != null) {
                      b = false;
                   }
                }else if(numberSubjNa.equals(numberSubjNa1)){
                }
                String subjectCount = this.getSubjectCount();
                String subjectCount1 = subjectDto.getSubjectCount();
                if (subjectCount == null) {
                   if (subjectCount1 != null) {
                      b = false;
                   }
                }else if(subjectCount.equals(subjectCount1)){
                }
                String moneySubjNam = this.getMoneySubjName();
                String moneySubjNam1 = subjectDto.getMoneySubjName();
                if (moneySubjNam == null) {
                   if (moneySubjNam1 != null) {
                      b = false;
                   }
                }else if(moneySubjNam.equals(moneySubjNam1)){
                }
                String subjectSumma = this.getSubjectSummary();
                String subjectSumma1 = subjectDto.getSubjectSummary();
                if (subjectSumma == null) {
                   if (subjectSumma1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!subjectSumma.equals(subjectSumma1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getMoneySubjName(){
       return this.moneySubjName;
    }
    public String getNumberSubjName(){
       return this.numberSubjName;
    }
    public String getSubjectCount(){
       return this.subjectCount;
    }
    public String getSubjectSummary(){
       return this.subjectSummary;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $year;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($year = this.getYear()) == null)? i: $year.hashCode();
       result = i1 + 59;
       String $numberSubjName = this.getNumberSubjName();
       int i2 = result * 59;
       i1 = ($numberSubjName == null)? i: $numberSubjName.hashCode();
       result = i2 + i1;
       String $subjectCount = this.getSubjectCount();
       i2 = result * 59;
       i1 = ($subjectCount == null)? i: $subjectCount.hashCode();
       result = i2 + i1;
       String $moneySubjName = this.getMoneySubjName();
       i2 = result * 59;
       i1 = ($moneySubjName == null)? i: $moneySubjName.hashCode();
       result = i2 + i1;
       String $subjectSummary = this.getSubjectSummary();
       i1 = result * 59;
       if ($subjectSummary != null) {
          i = $subjectSummary.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setMoneySubjName(String moneySubjName){
       this.moneySubjName = moneySubjName;
    }
    public void setNumberSubjName(String numberSubjName){
       this.numberSubjName = numberSubjName;
    }
    public void setSubjectCount(String subjectCount){
       this.subjectCount = subjectCount;
    }
    public void setSubjectSummary(String subjectSummary){
       this.subjectSummary = subjectSummary;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "SubjectDto\(year="+this.getYear()+", numberSubjName="+this.getNumberSubjName()+", subjectCount="+this.getSubjectCount()+", moneySubjName="+this.getMoneySubjName()+", subjectSummary="+this.getSubjectSummary()+"\)";
    }
}
