package com.taikang.fly.check.dto.dictType.DictTypeDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DictTypeDto implements Serializable	// class@0000e7 from classes.dex
{
    private String createTime;
    private String creator;
    private String dictMark;
    private String dictTypeId;
    private String dictTypeName;
    private String id;
    private String modby;
    private String modifyTime;
    private String signature;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a1;

    public void DictTypeDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictTypeDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DictTypeDto){
          b = false;
       }else {
          DictTypeDto uDictTypeDto = o;
          if (!uDictTypeDto.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uDictTypeDto.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String dictTypeId = this.getDictTypeId();
             String dictTypeId1 = uDictTypeDto.getDictTypeId();
             if (dictTypeId == null) {
                if (dictTypeId1 != null) {
                   b = false;
                }
             }else if(dictTypeId.equals(dictTypeId1)){
             }
             String dictTypeName = this.getDictTypeName();
             String dictTypeName1 = uDictTypeDto.getDictTypeName();
             if (dictTypeName == null) {
                if (dictTypeName1 != null) {
                   b = false;
                }
             }else if(dictTypeName.equals(dictTypeName1)){
             }
             String creator = this.getCreator();
             String creator1 = uDictTypeDto.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = uDictTypeDto.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = uDictTypeDto.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_009d :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = uDictTypeDto.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = uDictTypeDto.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00cd :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String dictMark = this.getDictMark();
             String dictMark1 = uDictTypeDto.getDictMark();
             if (dictMark == null) {
                if (dictMark1 != null) {
                   b = false;
                }
             }else if(dictMark.equals(dictMark1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDictMark(){
       return this.dictMark;
    }
    public String getDictTypeId(){
       return this.dictTypeId;
    }
    public String getDictTypeName(){
       return this.dictTypeName;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getSignature(){
       return this.signature;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $dictTypeId = this.getDictTypeId();
       int i2 = result * 59;
       i1 = ($dictTypeId == null)? i: $dictTypeId.hashCode();
       result = i2 + i1;
       String $dictTypeName = this.getDictTypeName();
       i2 = result * 59;
       i1 = ($dictTypeName == null)? i: $dictTypeName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i2 = (i2 + i1) * 59;
       i1 = (signature == null)? i: signature.hashCode();
       String dictMark = this.getDictMark();
       i1 = (i2 + i1) * 59;
       if (dictMark != null) {
          i = dictMark.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDictMark(String dictMark){
       this.dictMark = dictMark;
    }
    public void setDictTypeId(String dictTypeId){
       this.dictTypeId = dictTypeId;
    }
    public void setDictTypeName(String dictTypeName){
       this.dictTypeName = dictTypeName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public String toString(){
       return "DictTypeDto\(id="+this.getId()+", dictTypeId="+this.getDictTypeId()+", dictTypeName="+this.getDictTypeName()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", dictMark="+this.getDictMark()+"\)";
    }
}
