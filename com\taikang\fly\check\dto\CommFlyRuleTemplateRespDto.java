package com.taikang.fly.check.dto.CommFlyRuleTemplateRespDto;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class CommFlyRuleTemplateRespDto	// class@000099 from classes.dex
{
    private String hospitalType;
    private String isSpecial;
    private String projectCode1;
    private String projectCode2;
    private String projectName1;
    private String projectName2;
    private String ruleDimension;
    private String ruleName;
    private String ruleScopeApply;
    private Date ruleSuitTimeEnd;
    private Date ruleSuitTimeStart;
    private String ruleType;
    private String rulerComment;

    public void CommFlyRuleTemplateRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CommFlyRuleTemplateRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof CommFlyRuleTemplateRespDto){
          b = false;
       }else {
          CommFlyRuleTemplateRespDto uCommFlyRule = o;
          if (!uCommFlyRule.canEqual(this)) {
             b = false;
          }else {
             String ruleType = this.getRuleType();
             String ruleType1 = uCommFlyRule.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uCommFlyRule.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String projectCode1 = this.getProjectCode1();
             String projectCode11 = uCommFlyRule.getProjectCode1();
             if (projectCode1 == null) {
                if (projectCode11 != null) {
                   b = false;
                }
             }else if(projectCode1.equals(projectCode11)){
             }
             String projectName1 = this.getProjectName1();
             String projectName11 = uCommFlyRule.getProjectName1();
             if (projectName1 == null) {
                if (projectName11 != null) {
                   b = false;
                }
             }else if(projectName1.equals(projectName11)){
             }
             String projectCode2 = this.getProjectCode2();
             String projectCode21 = uCommFlyRule.getProjectCode2();
             if (projectCode2 == null) {
                if (projectCode21 != null) {
                   b = false;
                }
             }else if(projectCode2.equals(projectCode21)){
             }
             String projectName2 = this.getProjectName2();
             String projectName21 = uCommFlyRule.getProjectName2();
             if (projectName2 == null) {
                if (projectName21 != null) {
                   b = false;
                }
             }else if(projectName2.equals(projectName21)){
             }
             String rulerComment = this.getRulerComment();
             String rulerComment1 = uCommFlyRule.getRulerComment();
             if (rulerComment == null) {
                if (rulerComment1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(rulerComment.equals(rulerComment1)){
             }
             String hospitalType = this.getHospitalType();
             String hospitalType1 = uCommFlyRule.getHospitalType();
             if (hospitalType == null) {
                if (hospitalType1 != null) {
                   b = false;
                }
             }else if(hospitalType.equals(hospitalType1)){
             }
             String isSpecial = this.getIsSpecial();
             String isSpecial1 = uCommFlyRule.getIsSpecial();
             if (isSpecial == null) {
                if (isSpecial1 != null) {
                label_00eb :
                   b = false;
                }
             }else if(isSpecial.equals(isSpecial1)){
             }
             Date ruleSuitTime = this.getRuleSuitTimeStart();
             Date ruleSuitTime1 = uCommFlyRule.getRuleSuitTimeStart();
             if (ruleSuitTime == null) {
                if (ruleSuitTime1 != null) {
                   b = false;
                }
             }else if(ruleSuitTime.equals(ruleSuitTime1)){
             }
             Date ruleSuitTime2 = this.getRuleSuitTimeEnd();
             Date ruleSuitTime3 = uCommFlyRule.getRuleSuitTimeEnd();
             if (ruleSuitTime2 == null) {
                if (ruleSuitTime3 != null) {
                label_011b :
                   b = false;
                }
             }else if(ruleSuitTime2.equals(ruleSuitTime3)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = uCommFlyRule.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String ruleDimensio = this.getRuleDimension();
             String ruleDimensio1 = uCommFlyRule.getRuleDimension();
             if (ruleDimensio == null) {
                if (ruleDimensio1 != null) {
                label_014b :
                   b = false;
                }
             }else if(ruleDimensio.equals(ruleDimensio1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getHospitalType(){
       return this.hospitalType;
    }
    public String getIsSpecial(){
       return this.isSpecial;
    }
    public String getProjectCode1(){
       return this.projectCode1;
    }
    public String getProjectCode2(){
       return this.projectCode2;
    }
    public String getProjectName1(){
       return this.projectName1;
    }
    public String getProjectName2(){
       return this.projectName2;
    }
    public String getRuleDimension(){
       return this.ruleDimension;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public Date getRuleSuitTimeEnd(){
       return this.ruleSuitTimeEnd;
    }
    public Date getRuleSuitTimeStart(){
       return this.ruleSuitTimeStart;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getRulerComment(){
       return this.rulerComment;
    }
    public int hashCode(){
       String $ruleType;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleType = this.getRuleType()) == null)? 43: $ruleType.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $projectCode1 = this.getProjectCode1();
       i1 = result * 59;
       i = ($projectCode1 == null)? 43: $projectCode1.hashCode();
       result = i1 + i;
       String $projectName1 = this.getProjectName1();
       i1 = result * 59;
       i = ($projectName1 == null)? 43: $projectName1.hashCode();
       result = i1 + i;
       String $projectCode2 = this.getProjectCode2();
       i1 = result * 59;
       i = ($projectCode2 == null)? 43: $projectCode2.hashCode();
       result = i1 + i;
       String projectName2 = this.getProjectName2();
       i1 = result * 59;
       i = (projectName2 == null)? 43: projectName2.hashCode();
       String rulerComment = this.getRulerComment();
       i1 = (i1 + i) * 59;
       i = (rulerComment == null)? 43: rulerComment.hashCode();
       String hospitalType = this.getHospitalType();
       i1 = (i1 + i) * 59;
       i = (hospitalType == null)? 43: hospitalType.hashCode();
       String isSpecial = this.getIsSpecial();
       i1 = (i1 + i) * 59;
       i = (isSpecial == null)? 43: isSpecial.hashCode();
       Date ruleSuitTime = this.getRuleSuitTimeStart();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime == null)? 43: ruleSuitTime.hashCode();
       Date ruleSuitTime1 = this.getRuleSuitTimeEnd();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime1 == null)? 43: ruleSuitTime1.hashCode();
       String ruleScopeApp = this.getRuleScopeApply();
       i1 = (i1 + i) * 59;
       i = (ruleScopeApp == null)? 43: ruleScopeApp.hashCode();
       String ruleDimensio = this.getRuleDimension();
       i1 = (i1 + i) * 59;
       i = (ruleDimensio == null)? 43: ruleDimensio.hashCode();
       return (i1 + i);
    }
    public void setHospitalType(String hospitalType){
       this.hospitalType = hospitalType;
    }
    public void setIsSpecial(String isSpecial){
       this.isSpecial = isSpecial;
    }
    public void setProjectCode1(String projectCode1){
       this.projectCode1 = projectCode1;
    }
    public void setProjectCode2(String projectCode2){
       this.projectCode2 = projectCode2;
    }
    public void setProjectName1(String projectName1){
       this.projectName1 = projectName1;
    }
    public void setProjectName2(String projectName2){
       this.projectName2 = projectName2;
    }
    public void setRuleDimension(String ruleDimension){
       this.ruleDimension = ruleDimension;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setRuleSuitTimeEnd(Date ruleSuitTimeEnd){
       this.ruleSuitTimeEnd = ruleSuitTimeEnd;
    }
    public void setRuleSuitTimeStart(Date ruleSuitTimeStart){
       this.ruleSuitTimeStart = ruleSuitTimeStart;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setRulerComment(String rulerComment){
       this.rulerComment = rulerComment;
    }
    public String toString(){
       return "CommFlyRuleTemplateRespDto\(ruleType="+this.getRuleType()+", ruleName="+this.getRuleName()+", projectCode1="+this.getProjectCode1()+", projectName1="+this.getProjectName1()+", projectCode2="+this.getProjectCode2()+", projectName2="+this.getProjectName2()+", rulerComment="+this.getRulerComment()+", hospitalType="+this.getHospitalType()+", isSpecial="+this.getIsSpecial()+", ruleSuitTimeStart="+this.getRuleSuitTimeStart()+", ruleSuitTimeEnd="+this.getRuleSuitTimeEnd()+", ruleScopeApply="+this.getRuleScopeApply()+", ruleDimension="+this.getRuleDimension()+"\)";
    }
}
