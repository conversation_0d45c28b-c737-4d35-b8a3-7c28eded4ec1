package com.taikang.fly.check.dto.usermodule.UserModuleRespEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserModuleRespEditDto implements Serializable	// class@0001c7 from classes.dex
{
    private String id;
    private String moduleCode;
    private String signature;
    private String userCode;
    private static final long serialVersionUID = 0x349d06aa3e3fa7be;

    public void UserModuleRespEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserModuleRespEditDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserModuleRespEditDto) {
             b = false;
          }else {
             UserModuleRespEditDto userModuleRe = o;
             if (!userModuleRe.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = userModuleRe.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String userCode = this.getUserCode();
                String userCode1 = userModuleRe.getUserCode();
                if (userCode == null) {
                   if (userCode1 != null) {
                      b = false;
                   }
                }else if(userCode.equals(userCode1)){
                }
                String moduleCode = this.getModuleCode();
                String moduleCode1 = userModuleRe.getModuleCode();
                if (moduleCode == null) {
                   if (moduleCode1 != null) {
                      b = false;
                   }
                }else if(moduleCode.equals(moduleCode1)){
                }
                String signature = this.getSignature();
                String signature1 = userModuleRe.getSignature();
                if (signature == null) {
                   if (signature1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!signature.equals(signature1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $userCode = this.getUserCode();
       int i2 = result * 59;
       i1 = ($userCode == null)? i: $userCode.hashCode();
       result = i2 + i1;
       String $moduleCode = this.getModuleCode();
       i2 = result * 59;
       i1 = ($moduleCode == null)? i: $moduleCode.hashCode();
       result = i2 + i1;
       String $signature = this.getSignature();
       i1 = result * 59;
       if ($signature != null) {
          i = $signature.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserModuleRespEditDto\(id="+this.getId()+", userCode="+this.getUserCode()+", moduleCode="+this.getModuleCode()+", signature="+this.getSignature()+"\)";
    }
}
