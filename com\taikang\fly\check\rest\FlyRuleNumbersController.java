package com.taikang.fly.check.rest.FlyRuleNumbersController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.service.FlyRuleNumbersService;

public class FlyRuleNumbersController	// class@000293 from classes.dex
{
    private FlyRuleNumbersService flyRuleNumbersService;
    private static final Logger log;

    static {
       FlyRuleNumbersController.log = LoggerFactory.getLogger(FlyRuleNumbersController.class);
    }
    public void FlyRuleNumbersController(){
       super();
    }
    public CommResponse getFlyRuleNumbersList(Integer page,Integer size,String ruleName){
       return this.flyRuleNumbersService.getFlyRuleNumbersList(page, size, ruleName);
    }
}
