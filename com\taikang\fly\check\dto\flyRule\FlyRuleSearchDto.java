package com.taikang.fly.check.dto.flyRule.FlyRuleSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleSearchDto implements Serializable	// class@00010a from classes.dex
{
    private String creater;
    private String diagnosisType;
    private String executionDate;
    private String feedbackStatus;
    private String ids;
    private String isSpecial;
    private String operateTimeEnd;
    private String operateTimeStart;
    private String operator;
    private String policyBasis;
    private String region;
    private String resultFlag;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleDimension;
    private String ruleLevel;
    private String ruleName;
    private String ruleScopeApply;
    private String ruleSuitTimeEnd;
    private String ruleSuitTimeStart;
    private String ruleType;
    private String sqlName;
    private String state;
    private String submitState;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleSearchDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleSearchDto){
          b = false;
       }else {
          FlyRuleSearchDto uFlyRuleSear = o;
          if (!uFlyRuleSear.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleSear.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String operator = this.getOperator();
             String operator1 = uFlyRuleSear.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             String operateTimeS = this.getOperateTimeStart();
             String operateTimeS1 = uFlyRuleSear.getOperateTimeStart();
             if (operateTimeS == null) {
                if (operateTimeS1 != null) {
                   b = false;
                }
             }else if(operateTimeS.equals(operateTimeS1)){
             }
             String operateTimeE = this.getOperateTimeEnd();
             String operateTimeE1 = uFlyRuleSear.getOperateTimeEnd();
             if (operateTimeE == null) {
                if (operateTimeE1 != null) {
                   b = false;
                }
             }else if(operateTimeE.equals(operateTimeE1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uFlyRuleSear.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleSear.getRegion();
             if (region == null) {
                if (region1 != null) {
                label_00a7 :
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String state = this.getState();
             String state1 = uFlyRuleSear.getState();
             if (state == null) {
                if (state1 != null) {
                   b = false;
                }
             }else if(state.equals(state1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uFlyRuleSear.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String submitState = this.getSubmitState();
             String submitState1 = uFlyRuleSear.getSubmitState();
             if (submitState == null) {
                if (submitState1 != null) {
                label_00f3 :
                   b = false;
                }
             }else if(submitState.equals(submitState1)){
             }
             String executionDat = this.getExecutionDate();
             String executionDat1 = uFlyRuleSear.getExecutionDate();
             if (executionDat == null) {
                if (executionDat1 != null) {
                   b = false;
                }
             }else if(executionDat.equals(executionDat1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleSear.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                label_0125 :
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uFlyRuleSear.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uFlyRuleSear.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                label_0157 :
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleSear.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uFlyRuleSear.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                label_0189 :
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String creater = this.getCreater();
             String creater1 = uFlyRuleSear.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = uFlyRuleSear.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_01bb :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String isSpecial = this.getIsSpecial();
             String isSpecial1 = uFlyRuleSear.getIsSpecial();
             if (isSpecial == null) {
                if (isSpecial1 != null) {
                   b = false;
                }
             }else if(isSpecial.equals(isSpecial1)){
             }
             String ruleSuitTime = this.getRuleSuitTimeStart();
             String ruleSuitTime1 = uFlyRuleSear.getRuleSuitTimeStart();
             if (ruleSuitTime == null) {
                if (ruleSuitTime1 != null) {
                label_01eb :
                   b = false;
                }
             }else if(ruleSuitTime.equals(ruleSuitTime1)){
             }
             String ruleSuitTime2 = this.getRuleSuitTimeEnd();
             String ruleSuitTime3 = uFlyRuleSear.getRuleSuitTimeEnd();
             if (ruleSuitTime2 == null) {
                if (ruleSuitTime3 != null) {
                   b = false;
                }
             }else if(ruleSuitTime2.equals(ruleSuitTime3)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = uFlyRuleSear.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String ruleDimensio = this.getRuleDimension();
             String ruleDimensio1 = uFlyRuleSear.getRuleDimension();
             if (ruleDimensio == null) {
                if (ruleDimensio1 != null) {
                label_0239 :
                   b = false;
                }
             }else if(ruleDimensio.equals(ruleDimensio1)){
             }
             String resultFlag = this.getResultFlag();
             String resultFlag1 = uFlyRuleSear.getResultFlag();
             if (resultFlag == null) {
                if (resultFlag1 != null) {
                   b = false;
                }
             }else if(resultFlag.equals(resultFlag1)){
             }
             String feedbackStat = this.getFeedbackStatus();
             String feedbackStat1 = uFlyRuleSear.getFeedbackStatus();
             if (feedbackStat == null) {
                if (feedbackStat1 != null) {
                label_026b :
                   b = false;
                }
             }else if(feedbackStat.equals(feedbackStat1)){
             }
             String ids = this.getIds();
             String ids1 = uFlyRuleSear.getIds();
             if (ids == null) {
                if (ids1 != null) {
                   b = false;
                }
             }else if(ids.equals(ids1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getExecutionDate(){
       return this.executionDate;
    }
    public String getFeedbackStatus(){
       return this.feedbackStatus;
    }
    public String getIds(){
       return this.ids;
    }
    public String getIsSpecial(){
       return this.isSpecial;
    }
    public String getOperateTimeEnd(){
       return this.operateTimeEnd;
    }
    public String getOperateTimeStart(){
       return this.operateTimeStart;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRegion(){
       return this.region;
    }
    public String getResultFlag(){
       return this.resultFlag;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleDimension(){
       return this.ruleDimension;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public String getRuleSuitTimeEnd(){
       return this.ruleSuitTimeEnd;
    }
    public String getRuleSuitTimeStart(){
       return this.ruleSuitTimeStart;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public String getState(){
       return this.state;
    }
    public String getSubmitState(){
       return this.submitState;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $operator = this.getOperator();
       int i1 = result * 59;
       i = ($operator == null)? 43: $operator.hashCode();
       result = i1 + i;
       String $operateTimeStart = this.getOperateTimeStart();
       i1 = result * 59;
       i = ($operateTimeStart == null)? 43: $operateTimeStart.hashCode();
       result = i1 + i;
       String $operateTimeEnd = this.getOperateTimeEnd();
       i1 = result * 59;
       i = ($operateTimeEnd == null)? 43: $operateTimeEnd.hashCode();
       result = i1 + i;
       String $sqlName = this.getSqlName();
       i1 = result * 59;
       i = ($sqlName == null)? 43: $sqlName.hashCode();
       result = i1 + i;
       String region = this.getRegion();
       i1 = result * 59;
       i = (region == null)? 43: region.hashCode();
       String state = this.getState();
       i1 = (i1 + i) * 59;
       i = (state == null)? 43: state.hashCode();
       String ruleType = this.getRuleType();
       i1 = (i1 + i) * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String submitState = this.getSubmitState();
       i1 = (i1 + i) * 59;
       i = (submitState == null)? 43: submitState.hashCode();
       String executionDat = this.getExecutionDate();
       i1 = (i1 + i) * 59;
       i = (executionDat == null)? 43: executionDat.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String creater = this.getCreater();
       i1 = (i1 + i) * 59;
       i = (creater == null)? 43: creater.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String isSpecial = this.getIsSpecial();
       i1 = (i1 + i) * 59;
       i = (isSpecial == null)? 43: isSpecial.hashCode();
       String ruleSuitTime = this.getRuleSuitTimeStart();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime == null)? 43: ruleSuitTime.hashCode();
       String ruleSuitTime1 = this.getRuleSuitTimeEnd();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime1 == null)? 43: ruleSuitTime1.hashCode();
       String ruleScopeApp = this.getRuleScopeApply();
       i1 = (i1 + i) * 59;
       i = (ruleScopeApp == null)? 43: ruleScopeApp.hashCode();
       String ruleDimensio = this.getRuleDimension();
       i1 = (i1 + i) * 59;
       i = (ruleDimensio == null)? 43: ruleDimensio.hashCode();
       String resultFlag = this.getResultFlag();
       i1 = (i1 + i) * 59;
       i = (resultFlag == null)? 43: resultFlag.hashCode();
       String feedbackStat = this.getFeedbackStatus();
       i1 = (i1 + i) * 59;
       i = (feedbackStat == null)? 43: feedbackStat.hashCode();
       String ids = this.getIds();
       i1 = (i1 + i) * 59;
       i = (ids == null)? 43: ids.hashCode();
       return (i1 + i);
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setExecutionDate(String executionDate){
       this.executionDate = executionDate;
    }
    public void setFeedbackStatus(String feedbackStatus){
       this.feedbackStatus = feedbackStatus;
    }
    public void setIds(String ids){
       this.ids = ids;
    }
    public void setIsSpecial(String isSpecial){
       this.isSpecial = isSpecial;
    }
    public void setOperateTimeEnd(String operateTimeEnd){
       this.operateTimeEnd = operateTimeEnd;
    }
    public void setOperateTimeStart(String operateTimeStart){
       this.operateTimeStart = operateTimeStart;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setResultFlag(String resultFlag){
       this.resultFlag = resultFlag;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleDimension(String ruleDimension){
       this.ruleDimension = ruleDimension;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setRuleSuitTimeEnd(String ruleSuitTimeEnd){
       this.ruleSuitTimeEnd = ruleSuitTimeEnd;
    }
    public void setRuleSuitTimeStart(String ruleSuitTimeStart){
       this.ruleSuitTimeStart = ruleSuitTimeStart;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public void setState(String state){
       this.state = state;
    }
    public void setSubmitState(String submitState){
       this.submitState = submitState;
    }
    public String toString(){
       return "FlyRuleSearchDto\(ruleName="+this.getRuleName()+", operator="+this.getOperator()+", operateTimeStart="+this.getOperateTimeStart()+", operateTimeEnd="+this.getOperateTimeEnd()+", sqlName="+this.getSqlName()+", region="+this.getRegion()+", state="+this.getState()+", ruleType="+this.getRuleType()+", submitState="+this.getSubmitState()+", executionDate="+this.getExecutionDate()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", creater="+this.getCreater()+", policyBasis="+this.getPolicyBasis()+", isSpecial="+this.getIsSpecial()+", ruleSuitTimeStart="+this.getRuleSuitTimeStart()+", ruleSuitTimeEnd="+this.getRuleSuitTimeEnd()+", ruleScopeApply="+this.getRuleScopeApply()+", ruleDimension="+this.getRuleDimension()+", resultFlag="+this.getResultFlag()+", feedbackStatus="+this.getFeedbackStatus()+", ids="+this.getIds()+"\)";
    }
}
