package com.taikang.fly.check.service.CsvImportService$1;
import java.util.Comparator;
import com.taikang.fly.check.service.CsvImportService;
import java.lang.Object;
import com.taikang.fly.check.dto.csventry.CSVHeaderEntry;
import java.lang.Integer;

class CsvImportService$1 implements Comparator	// class@0002ce from classes.dex
{
    final CsvImportService this$0;

    void CsvImportService$1(CsvImportService this$0){
       this.this$0 = this$0;
       super();
    }
    public int compare(CSVHeaderEntry o1,CSVHeaderEntry o2){
       return o1.getOldIndex().compareTo(o2.getOldIndex());
    }
    public int compare(Object p0,Object p1){
       return this.compare(p0, p1);
    }
}
