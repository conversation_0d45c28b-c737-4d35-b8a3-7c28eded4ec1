package com.taikang.fly.check.dto.drg.DrgApplyHighCostOutputOneDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgApplyHighCostOutputOneDto implements Serializable	// class@0000ea from classes.dex
{
    private String drgCode;
    private String suborderCode;
    private static final long serialVersionUID = 0x1;

    public void DrgApplyHighCostOutputOneDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgApplyHighCostOutputOneDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgApplyHighCostOutputOneDto) {
             b = false;
          }else {
             DrgApplyHighCostOutputOneDto uDrgApplyHig = o;
             if (!uDrgApplyHig.canEqual(this)) {
                b = false;
             }else {
                String drgCode = this.getDrgCode();
                String drgCode1 = uDrgApplyHig.getDrgCode();
                if (drgCode == null) {
                   if (drgCode1 != null) {
                      b = false;
                   }
                }else if(drgCode.equals(drgCode1)){
                }
                String suborderCode = this.getSuborderCode();
                String suborderCode1 = uDrgApplyHig.getSuborderCode();
                if (suborderCode == null) {
                   if (suborderCode1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!suborderCode.equals(suborderCode1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getSuborderCode(){
       return this.suborderCode;
    }
    public int hashCode(){
       String $drgCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgCode = this.getDrgCode()) == null)? i: $drgCode.hashCode();
       result = i1 + 59;
       String $suborderCode = this.getSuborderCode();
       i1 = result * 59;
       if ($suborderCode != null) {
          i = $suborderCode.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setSuborderCode(String suborderCode){
       this.suborderCode = suborderCode;
    }
    public String toString(){
       return "DrgApplyHighCostOutputOneDto\(drgCode="+this.getDrgCode()+", suborderCode="+this.getSuborderCode()+"\)";
    }
}
