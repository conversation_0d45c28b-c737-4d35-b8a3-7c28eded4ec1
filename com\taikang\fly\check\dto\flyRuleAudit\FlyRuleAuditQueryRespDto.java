package com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditQueryRespDto;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class FlyRuleAuditQueryRespDto	// class@000111 from classes.dex
{
    private String auditRejectReason;
    private String diagnosisType;
    private String id;
    private LocalDateTime operateTime;
    private String ruleLevel;
    private String ruleName;
    private String ruleScopeApply;
    private String updateStatus;

    public void FlyRuleAuditQueryRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleAuditQueryRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleAuditQueryRespDto){
          b = false;
       }else {
          FlyRuleAuditQueryRespDto uFlyRuleAudi = o;
          if (!uFlyRuleAudi.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleAudi.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String id = this.getId();
             String id1 = uFlyRuleAudi.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleAudi.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleAudi.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = uFlyRuleAudi.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String auditRejectR = this.getAuditRejectReason();
             String auditRejectR1 = uFlyRuleAudi.getAuditRejectReason();
             if (auditRejectR == null) {
                if (auditRejectR1 != null) {
                   b = false;
                }
             }else if(auditRejectR.equals(auditRejectR1)){
             }
             String updateStatus = this.getUpdateStatus();
             String updateStatus1 = uFlyRuleAudi.getUpdateStatus();
             if (updateStatus == null) {
                if (updateStatus1 != null) {
                label_00b2 :
                   b = false;
                }
             }else if(updateStatus.equals(updateStatus1)){
             }
             LocalDateTime operateTime = this.getOperateTime();
             LocalDateTime operateTime1 = uFlyRuleAudi.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAuditRejectReason(){
       return this.auditRejectReason;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public LocalDateTime getOperateTime(){
       return this.operateTime;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public String getUpdateStatus(){
       return this.updateStatus;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $id = this.getId();
       int i2 = result * 59;
       i1 = ($id == null)? i: $id.hashCode();
       result = i2 + i1;
       String $ruleLevel = this.getRuleLevel();
       i2 = result * 59;
       i1 = ($ruleLevel == null)? i: $ruleLevel.hashCode();
       result = i2 + i1;
       String $diagnosisType = this.getDiagnosisType();
       i2 = result * 59;
       i1 = ($diagnosisType == null)? i: $diagnosisType.hashCode();
       result = i2 + i1;
       String $ruleScopeApply = this.getRuleScopeApply();
       i2 = result * 59;
       i1 = ($ruleScopeApply == null)? i: $ruleScopeApply.hashCode();
       result = i2 + i1;
       String auditRejectR = this.getAuditRejectReason();
       i2 = result * 59;
       i1 = (auditRejectR == null)? i: auditRejectR.hashCode();
       String updateStatus = this.getUpdateStatus();
       i2 = (i2 + i1) * 59;
       i1 = (updateStatus == null)? i: updateStatus.hashCode();
       LocalDateTime operateTime = this.getOperateTime();
       i1 = (i2 + i1) * 59;
       if (operateTime != null) {
          i = operateTime.hashCode();
       }
       return (i1 + i);
    }
    public void setAuditRejectReason(String auditRejectReason){
       this.auditRejectReason = auditRejectReason;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setOperateTime(LocalDateTime operateTime){
       this.operateTime = operateTime;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setUpdateStatus(String updateStatus){
       this.updateStatus = updateStatus;
    }
    public String toString(){
       return "FlyRuleAuditQueryRespDto\(ruleName="+this.getRuleName()+", id="+this.getId()+", ruleLevel="+this.getRuleLevel()+", diagnosisType="+this.getDiagnosisType()+", ruleScopeApply="+this.getRuleScopeApply()+", auditRejectReason="+this.getAuditRejectReason()+", updateStatus="+this.getUpdateStatus()+", operateTime="+this.getOperateTime()+"\)";
    }
}
