version: '3.8'

services:
  flycheck:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLYCHECK_DEBUG=false
      - FLYCHECK_DATABASE_URL=duckdb:///./data/flycheck.db
      - FLYCHECK_REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./results:/app/results
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
