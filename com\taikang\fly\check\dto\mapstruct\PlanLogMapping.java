package com.taikang.fly.check.dto.mapstruct.PlanLogMapping;
import com.taikang.fly.check.dto.planLog.AddPlanLogDto;
import com.taikang.fly.check.mybatis.domain.PlanLog;
import com.taikang.fly.check.dto.plan.PlanLogRespDto;
import com.taikang.fly.check.dto.plan.PlanLogRespsDto;
import java.util.List;

public interface abstract PlanLogMapping	// class@000171 from classes.dex
{

    PlanLog AddPlanLogTOPlanLog(AddPlanLogDto p0);
    PlanLogRespsDto PlanLogDtoToDto(PlanLogRespDto p0);
    List PlanLogDtoToDtos(List p0);
}
