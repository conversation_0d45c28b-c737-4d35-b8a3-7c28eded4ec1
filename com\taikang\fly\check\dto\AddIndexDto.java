package com.taikang.fly.check.dto.AddIndexDto;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class AddIndexDto	// class@000098 from classes.dex
{
    private List columnList;
    private String tableName;

    public void AddIndexDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof AddIndexDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof AddIndexDto) {
             b = false;
          }else {
             AddIndexDto uAddIndexDto = o;
             if (!uAddIndexDto.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = uAddIndexDto.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                List columnList = this.getColumnList();
                List columnList1 = uAddIndexDto.getColumnList();
                if (columnList == null) {
                   if (columnList1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!columnList.equals(columnList1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getColumnList(){
       return this.columnList;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       List $columnList = this.getColumnList();
       i1 = result * 59;
       if ($columnList != null) {
          i = $columnList.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumnList(List columnList){
       this.columnList = columnList;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "AddIndexDto\(tableName="+this.getTableName()+", columnList="+this.getColumnList()+"\)";
    }
}
