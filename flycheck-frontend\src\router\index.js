import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表板', icon: 'DataBoard' }
      }
    ]
  },
  {
    path: '/rules',
    component: Layout,
    redirect: '/rules/list',
    meta: { title: '规则管理', icon: 'Document' },
    children: [
      {
        path: 'list',
        name: 'RuleList',
        component: () => import('@/views/rules/list.vue'),
        meta: { title: '规则列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'RuleCreate',
        component: () => import('@/views/rules/form.vue'),
        meta: { title: '创建规则', icon: 'Plus' }
      },
      {
        path: 'edit/:id',
        name: 'RuleEdit',
        component: () => import('@/views/rules/form.vue'),
        meta: { title: '编辑规则', icon: 'Edit' },
        hidden: true
      }
    ]
  },
  {
    path: '/plans',
    component: Layout,
    redirect: '/plans/list',
    meta: { title: '计划管理', icon: 'Calendar' },
    children: [
      {
        path: 'list',
        name: 'PlanList',
        component: () => import('@/views/plans/list.vue'),
        meta: { title: '计划列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'PlanCreate',
        component: () => import('@/views/plans/form.vue'),
        meta: { title: '创建计划', icon: 'Plus' }
      },
      {
        path: 'edit/:id',
        name: 'PlanEdit',
        component: () => import('@/views/plans/form.vue'),
        meta: { title: '编辑计划', icon: 'Edit' },
        hidden: true
      }
    ]
  },
  {
    path: '/medical',
    component: Layout,
    redirect: '/medical/drugs',
    meta: { title: '医保数据', icon: 'Files' },
    children: [
      {
        path: 'drugs',
        name: 'DrugList',
        component: () => import('@/views/medical/drugs.vue'),
        meta: { title: '药品目录', icon: 'Medicine' }
      },
      {
        path: 'treatments',
        name: 'TreatmentList',
        component: () => import('@/views/medical/treatments.vue'),
        meta: { title: '诊疗项目', icon: 'FirstAidKit' }
      },
      {
        path: 'consumables',
        name: 'ConsumableList',
        component: () => import('@/views/medical/consumables.vue'),
        meta: { title: '医用耗材', icon: 'Box' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/config',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/config.vue'),
        meta: { title: '系统配置', icon: 'Tools' }
      },
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/system/logs.vue'),
        meta: { title: '系统日志', icon: 'Document' }
      }
    ]
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/test.vue'),
    meta: { title: '集成测试' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
