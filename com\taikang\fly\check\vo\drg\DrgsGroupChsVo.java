package com.taikang.fly.check.vo.drg.DrgsGroupChsVo;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class DrgsGroupChsVo implements Serializable	// class@00037d from classes.dex
{
    private String adrgCode;
    private String adrgFlag;
    private String adrgName;
    private String age;
    private String ageFlag;
    private String ageMatch;
    private String ccFlag;
    private String ccMatch;
    private String ccResult;
    private String chsAdrgCode;
    private String chsAdrgName;
    private String createdTime;
    private String dfxcsFlag;
    private String dfxcsMatch;
    private String drgFlag;
    private String drgType;
    private String drgsAge;
    private String drgsCode;
    private String drgsCodeFinal;
    private String drgsDfxcs;
    private String drgsName;
    private String drgsNameFinal;
    private String drgsOutcome;
    private String drgsSex;
    private String drgsTrtime;
    private String drgsWeight;
    private String factor1Match;
    private String factor2Match;
    private String factor3Match;
    private String factor4Match;
    private String factor5Match;
    private String factorMatch;
    private Integer factorMatchN;
    private String groupCodeM;
    private String groupCodeS;
    private Integer matchN;
    private String mdcGroup1;
    private String mdcGroup2;
    private String message;
    private String nl;
    private String otherFactorId;
    private String otherFactorId1;
    private String otherFactorId1Result;
    private String otherFactorId2;
    private String otherFactorId2Result;
    private String otherFactorId3;
    private String otherFactorId3Result;
    private String otherFactorId4;
    private String otherFactorId4Result;
    private String otherFactorId5;
    private String otherFactorId5Result;
    private String otherFactorResult;
    private String outcomeFlag;
    private String outcomeMatch;
    private String projectId;
    private String reasonFlag;
    private String trtimeMatch;
    private String typeMatch;
    private String userid;
    private String version;
    private String xb;
    private String xbFlag;
    private String xbMatch;
    private String xseWeightFlag;
    private String xseWeightMatch;
    private String zylsh;
    private String zyts;

    public void DrgsGroupChsVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgsGroupChsVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgsGroupChsVo){
          b = false;
       }else {
          DrgsGroupChsVo uDrgsGroupCh = o;
          if (!uDrgsGroupCh.canEqual(this)) {
             b = false;
          }else {
             String drgsCode = this.getDrgsCode();
             String drgsCode1 = uDrgsGroupCh.getDrgsCode();
             if (drgsCode == null) {
                if (drgsCode1 != null) {
                   b = false;
                }
             }else if(drgsCode.equals(drgsCode1)){
             }
             String drgsName = this.getDrgsName();
             String drgsName1 = uDrgsGroupCh.getDrgsName();
             if (drgsName == null) {
                if (drgsName1 != null) {
                   b = false;
                }
             }else if(drgsName.equals(drgsName1)){
             }
             String adrgCode = this.getAdrgCode();
             String adrgCode1 = uDrgsGroupCh.getAdrgCode();
             if (adrgCode == null) {
                if (adrgCode1 != null) {
                   b = false;
                }
             }else if(adrgCode.equals(adrgCode1)){
             }
             String chsAdrgName = this.getChsAdrgName();
             String chsAdrgName1 = uDrgsGroupCh.getChsAdrgName();
             if (chsAdrgName == null) {
                if (chsAdrgName1 != null) {
                   b = false;
                }
             }else if(chsAdrgName.equals(chsAdrgName1)){
             }
             String chsAdrgCode = this.getChsAdrgCode();
             String chsAdrgCode1 = uDrgsGroupCh.getChsAdrgCode();
             if (chsAdrgCode == null) {
                if (chsAdrgCode1 != null) {
                   b = false;
                }
             }else if(chsAdrgCode.equals(chsAdrgCode1)){
             }
             String adrgName = this.getAdrgName();
             String adrgName1 = uDrgsGroupCh.getAdrgName();
             if (adrgName == null) {
                if (adrgName1 != null) {
                label_00a7 :
                   b = false;
                }
             }else if(adrgName.equals(adrgName1)){
             }
             String drgType = this.getDrgType();
             String drgType1 = uDrgsGroupCh.getDrgType();
             if (drgType == null) {
                if (drgType1 != null) {
                   b = false;
                }
             }else if(drgType.equals(drgType1)){
             }
             String message = this.getMessage();
             String message1 = uDrgsGroupCh.getMessage();
             if (message == null) {
                if (message1 != null) {
                label_00d9 :
                   b = false;
                }
             }else if(message.equals(message1)){
             }
             String drgsCodeFina = this.getDrgsCodeFinal();
             String drgsCodeFina1 = uDrgsGroupCh.getDrgsCodeFinal();
             if (drgsCodeFina == null) {
                if (drgsCodeFina1 != null) {
                   b = false;
                }
             }else if(drgsCodeFina.equals(drgsCodeFina1)){
             }
             String drgsNameFina = this.getDrgsNameFinal();
             String drgsNameFina1 = uDrgsGroupCh.getDrgsNameFinal();
             if (drgsNameFina == null) {
                if (drgsNameFina1 != null) {
                   b = false;
                }
             }else if(drgsNameFina.equals(drgsNameFina1)){
             }
             String drgFlag = this.getDrgFlag();
             String drgFlag1 = uDrgsGroupCh.getDrgFlag();
             if (drgFlag == null) {
                if (drgFlag1 != null) {
                label_0127 :
                   b = false;
                }
             }else if(drgFlag.equals(drgFlag1)){
             }
             String zylsh = this.getZylsh();
             String zylsh1 = uDrgsGroupCh.getZylsh();
             if (zylsh == null) {
                if (zylsh1 != null) {
                   b = false;
                }
             }else if(zylsh.equals(zylsh1)){
             }
             String userid = this.getUserid();
             String userid1 = uDrgsGroupCh.getUserid();
             if (userid == null) {
                if (userid1 != null) {
                label_015b :
                   b = false;
                }
             }else if(userid.equals(userid1)){
             }
             String xb = this.getXb();
             String xb1 = uDrgsGroupCh.getXb();
             if (xb == null) {
                if (xb1 != null) {
                   b = false;
                }
             }else if(xb.equals(xb1)){
             }
             String xbFlag = this.getXbFlag();
             String xbFlag1 = uDrgsGroupCh.getXbFlag();
             if (xbFlag == null) {
                if (xbFlag1 != null) {
                label_018f :
                   b = false;
                }
             }else if(xbFlag.equals(xbFlag1)){
             }
             String xseWeightFla = this.getXseWeightFlag();
             String xseWeightFla1 = uDrgsGroupCh.getXseWeightFlag();
             if (xseWeightFla == null) {
                if (xseWeightFla1 != null) {
                   b = false;
                }
             }else if(xseWeightFla.equals(xseWeightFla1)){
             }
             String age = this.getAge();
             String age1 = uDrgsGroupCh.getAge();
             if (age == null) {
                if (age1 != null) {
                label_01c3 :
                   b = false;
                }
             }else if(age.equals(age1)){
             }
             String nl = this.getNl();
             String nl1 = uDrgsGroupCh.getNl();
             if (nl == null) {
                if (nl1 != null) {
                   b = false;
                }
             }else if(nl.equals(nl1)){
             }
             String ageFlag = this.getAgeFlag();
             String ageFlag1 = uDrgsGroupCh.getAgeFlag();
             if (ageFlag == null) {
                if (ageFlag1 != null) {
                label_01f5 :
                   b = false;
                }
             }else if(ageFlag.equals(ageFlag1)){
             }
             String zyts = this.getZyts();
             String zyts1 = uDrgsGroupCh.getZyts();
             if (zyts == null) {
                if (zyts1 != null) {
                   b = false;
                }
             }else if(zyts.equals(zyts1)){
             }
             String outcomeFlag = this.getOutcomeFlag();
             String outcomeFlag1 = uDrgsGroupCh.getOutcomeFlag();
             if (outcomeFlag == null) {
                if (outcomeFlag1 != null) {
                label_0227 :
                   b = false;
                }
             }else if(outcomeFlag.equals(outcomeFlag1)){
             }
             String adrgFlag = this.getAdrgFlag();
             String adrgFlag1 = uDrgsGroupCh.getAdrgFlag();
             if (adrgFlag == null) {
                if (adrgFlag1 != null) {
                   b = false;
                }
             }else if(adrgFlag.equals(adrgFlag1)){
             }
             String ccResult = this.getCcResult();
             String ccResult1 = uDrgsGroupCh.getCcResult();
             if (ccResult == null) {
                if (ccResult1 != null) {
                   b = false;
                }
             }else if(ccResult.equals(ccResult1)){
             }
             String otherFactorI = this.getOtherFactorId1Result();
             String otherFactorI1 = uDrgsGroupCh.getOtherFactorId1Result();
             if (otherFactorI == null) {
                if (otherFactorI1 != null) {
                label_0271 :
                   b = false;
                }
             }else if(otherFactorI.equals(otherFactorI1)){
             }
             String otherFactorI2 = this.getOtherFactorId2Result();
             String otherFactorI3 = uDrgsGroupCh.getOtherFactorId2Result();
             if (otherFactorI2 == null) {
                if (otherFactorI3 != null) {
                   b = false;
                }
             }else if(otherFactorI2.equals(otherFactorI3)){
             }
             String otherFactorI4 = this.getOtherFactorId3Result();
             String otherFactorI5 = uDrgsGroupCh.getOtherFactorId3Result();
             if (otherFactorI4 == null) {
                if (otherFactorI5 != null) {
                label_02a5 :
                   b = false;
                }
             }else if(otherFactorI4.equals(otherFactorI5)){
             }
             String otherFactorI6 = this.getOtherFactorId4Result();
             String otherFactorI7 = uDrgsGroupCh.getOtherFactorId4Result();
             if (otherFactorI6 == null) {
                if (otherFactorI7 != null) {
                   b = false;
                }
             }else if(otherFactorI6.equals(otherFactorI7)){
             }
             String otherFactorI8 = this.getOtherFactorId5Result();
             String otherFactorI9 = uDrgsGroupCh.getOtherFactorId5Result();
             if (otherFactorI8 == null) {
                if (otherFactorI9 != null) {
                label_02d9 :
                   b = false;
                }
             }else if(otherFactorI8.equals(otherFactorI9)){
             }
             String otherFactorR = this.getOtherFactorResult();
             String otherFactorR1 = uDrgsGroupCh.getOtherFactorResult();
             if (otherFactorR == null) {
                if (otherFactorR1 != null) {
                   b = false;
                }
             }else if(otherFactorR.equals(otherFactorR1)){
             }
             String dfxcsFlag = this.getDfxcsFlag();
             String dfxcsFlag1 = uDrgsGroupCh.getDfxcsFlag();
             if (dfxcsFlag == null) {
                if (dfxcsFlag1 != null) {
                label_030d :
                   b = false;
                }
             }else if(dfxcsFlag.equals(dfxcsFlag1)){
             }
             String ccFlag = this.getCcFlag();
             String ccFlag1 = uDrgsGroupCh.getCcFlag();
             if (ccFlag == null) {
                if (ccFlag1 != null) {
                   b = false;
                }
             }else if(ccFlag.equals(ccFlag1)){
             }
             String otherFactorI10 = this.getOtherFactorId1();
             String otherFactorI11 = uDrgsGroupCh.getOtherFactorId1();
             if (otherFactorI10 == null) {
                if (otherFactorI11 != null) {
                label_033d :
                   b = false;
                }
             }else if(otherFactorI10.equals(otherFactorI11)){
             }
             String otherFactorI12 = this.getOtherFactorId2();
             String otherFactorI13 = uDrgsGroupCh.getOtherFactorId2();
             if (otherFactorI12 == null) {
                if (otherFactorI13 != null) {
                   b = false;
                }
             }else if(otherFactorI12.equals(otherFactorI13)){
             }
             String otherFactorI14 = this.getOtherFactorId3();
             String otherFactorI15 = uDrgsGroupCh.getOtherFactorId3();
             if (otherFactorI14 == null) {
                if (otherFactorI15 != null) {
                label_0371 :
                   b = false;
                }
             }else if(otherFactorI14.equals(otherFactorI15)){
             }
             String otherFactorI16 = this.getOtherFactorId4();
             String otherFactorI17 = uDrgsGroupCh.getOtherFactorId4();
             if (otherFactorI16 == null) {
                if (otherFactorI17 != null) {
                   b = false;
                }
             }else if(otherFactorI16.equals(otherFactorI17)){
             }
             String otherFactorI18 = this.getOtherFactorId5();
             String otherFactorI19 = uDrgsGroupCh.getOtherFactorId5();
             if (otherFactorI18 == null) {
                if (otherFactorI19 != null) {
                   b = false;
                }
             }else if(otherFactorI18.equals(otherFactorI19)){
             }
             String otherFactorI20 = this.getOtherFactorId();
             String otherFactorI21 = uDrgsGroupCh.getOtherFactorId();
             if (otherFactorI20 == null) {
                if (otherFactorI21 != null) {
                label_03bf :
                   b = false;
                }
             }else if(otherFactorI20.equals(otherFactorI21)){
             }
             String drgsSex = this.getDrgsSex();
             String drgsSex1 = uDrgsGroupCh.getDrgsSex();
             if (drgsSex == null) {
                if (drgsSex1 != null) {
                   b = false;
                }
             }else if(drgsSex.equals(drgsSex1)){
             }
             String drgsAge = this.getDrgsAge();
             String drgsAge1 = uDrgsGroupCh.getDrgsAge();
             if (drgsAge == null) {
                if (drgsAge1 != null) {
                label_03f3 :
                   b = false;
                }
             }else if(drgsAge.equals(drgsAge1)){
             }
             String drgsOutcome = this.getDrgsOutcome();
             String drgsOutcome1 = uDrgsGroupCh.getDrgsOutcome();
             if (drgsOutcome == null) {
                if (drgsOutcome1 != null) {
                   b = false;
                }
             }else if(drgsOutcome.equals(drgsOutcome1)){
             }
             String drgsWeight = this.getDrgsWeight();
             String drgsWeight1 = uDrgsGroupCh.getDrgsWeight();
             if (drgsWeight == null) {
                if (drgsWeight1 != null) {
                label_0427 :
                   b = false;
                }
             }else if(drgsWeight.equals(drgsWeight1)){
             }
             String drgsTrtime = this.getDrgsTrtime();
             String drgsTrtime1 = uDrgsGroupCh.getDrgsTrtime();
             if (drgsTrtime == null) {
                if (drgsTrtime1 != null) {
                   b = false;
                }
             }else if(drgsTrtime.equals(drgsTrtime1)){
             }
             String drgsDfxcs = this.getDrgsDfxcs();
             String drgsDfxcs1 = uDrgsGroupCh.getDrgsDfxcs();
             if (drgsDfxcs == null) {
                if (drgsDfxcs1 != null) {
                label_045b :
                   b = false;
                }
             }else if(drgsDfxcs.equals(drgsDfxcs1)){
             }
             Integer matchN = this.getMatchN();
             Integer matchN1 = uDrgsGroupCh.getMatchN();
             if (matchN == null) {
                if (matchN1 != null) {
                   b = false;
                }
             }else if(matchN.equals(matchN1)){
             }
             String createdTime = this.getCreatedTime();
             String createdTime1 = uDrgsGroupCh.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                label_048f :
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String typeMatch = this.getTypeMatch();
             String typeMatch1 = uDrgsGroupCh.getTypeMatch();
             if (typeMatch == null) {
                if (typeMatch1 != null) {
                   b = false;
                }
             }else if(typeMatch.equals(typeMatch1)){
             }
             String ccMatch = this.getCcMatch();
             String ccMatch1 = uDrgsGroupCh.getCcMatch();
             if (ccMatch == null) {
                if (ccMatch1 != null) {
                   b = false;
                }
             }else if(ccMatch.equals(ccMatch1)){
             }
             String factor1Match = this.getFactor1Match();
             String factor1Match1 = uDrgsGroupCh.getFactor1Match();
             if (factor1Match == null) {
                if (factor1Match1 != null) {
                label_04d9 :
                   b = false;
                }
             }else if(factor1Match.equals(factor1Match1)){
             }
             String factor2Match = this.getFactor2Match();
             String factor2Match1 = uDrgsGroupCh.getFactor2Match();
             if (factor2Match == null) {
                if (factor2Match1 != null) {
                   b = false;
                }
             }else if(factor2Match.equals(factor2Match1)){
             }
             String factor3Match = this.getFactor3Match();
             String factor3Match1 = uDrgsGroupCh.getFactor3Match();
             if (factor3Match == null) {
                if (factor3Match1 != null) {
                label_050d :
                   b = false;
                }
             }else if(factor3Match.equals(factor3Match1)){
             }
             String factor4Match = this.getFactor4Match();
             String factor4Match1 = uDrgsGroupCh.getFactor4Match();
             if (factor4Match == null) {
                if (factor4Match1 != null) {
                   b = false;
                }
             }else if(factor4Match.equals(factor4Match1)){
             }
             String factor5Match = this.getFactor5Match();
             String factor5Match1 = uDrgsGroupCh.getFactor5Match();
             if (factor5Match == null) {
                if (factor5Match1 != null) {
                label_0541 :
                   b = false;
                }
             }else if(factor5Match.equals(factor5Match1)){
             }
             String xbMatch = this.getXbMatch();
             String xbMatch1 = uDrgsGroupCh.getXbMatch();
             if (xbMatch == null) {
                if (xbMatch1 != null) {
                   b = false;
                }
             }else if(xbMatch.equals(xbMatch1)){
             }
             String ageMatch = this.getAgeMatch();
             String ageMatch1 = uDrgsGroupCh.getAgeMatch();
             if (ageMatch == null) {
                if (ageMatch1 != null) {
                label_0575 :
                   b = false;
                }
             }else if(ageMatch.equals(ageMatch1)){
             }
             String outcomeMatch = this.getOutcomeMatch();
             String outcomeMatch1 = uDrgsGroupCh.getOutcomeMatch();
             if (outcomeMatch == null) {
                if (outcomeMatch1 != null) {
                   b = false;
                }
             }else if(outcomeMatch.equals(outcomeMatch1)){
             }
             String xseWeightMat = this.getXseWeightMatch();
             String xseWeightMat1 = uDrgsGroupCh.getXseWeightMatch();
             if (xseWeightMat == null) {
                if (xseWeightMat1 != null) {
                label_05a7 :
                   b = false;
                }
             }else if(xseWeightMat.equals(xseWeightMat1)){
             }
             String trtimeMatch = this.getTrtimeMatch();
             String trtimeMatch1 = uDrgsGroupCh.getTrtimeMatch();
             if (trtimeMatch == null) {
                if (trtimeMatch1 != null) {
                   b = false;
                }
             }else if(trtimeMatch.equals(trtimeMatch1)){
             }
             String dfxcsMatch = this.getDfxcsMatch();
             String dfxcsMatch1 = uDrgsGroupCh.getDfxcsMatch();
             if (dfxcsMatch == null) {
                if (dfxcsMatch1 != null) {
                label_05db :
                   b = false;
                }
             }else if(dfxcsMatch.equals(dfxcsMatch1)){
             }
             String factorMatch = this.getFactorMatch();
             String factorMatch1 = uDrgsGroupCh.getFactorMatch();
             if (factorMatch == null) {
                if (factorMatch1 != null) {
                   b = false;
                }
             }else if(factorMatch.equals(factorMatch1)){
             }
             Integer factorMatchN = this.getFactorMatchN();
             Integer factorMatchN1 = uDrgsGroupCh.getFactorMatchN();
             if (factorMatchN == null) {
                if (factorMatchN1 != null) {
                   b = false;
                }
             }else if(factorMatchN.equals(factorMatchN1)){
             }
             String mdcGroup1 = this.getMdcGroup1();
             String mdcGroup11 = uDrgsGroupCh.getMdcGroup1();
             if (mdcGroup1 == null) {
                if (mdcGroup11 != null) {
                label_0629 :
                   b = false;
                }
             }else if(mdcGroup1.equals(mdcGroup11)){
             }
             String mdcGroup2 = this.getMdcGroup2();
             String mdcGroup21 = uDrgsGroupCh.getMdcGroup2();
             if (mdcGroup2 == null) {
                if (mdcGroup21 != null) {
                   b = false;
                }
             }else if(mdcGroup2.equals(mdcGroup21)){
             }
             String projectId = this.getProjectId();
             String projectId1 = uDrgsGroupCh.getProjectId();
             if (projectId == null) {
                if (projectId1 != null) {
                label_065d :
                   b = false;
                }
             }else if(projectId.equals(projectId1)){
             }
             String version = this.getVersion();
             String version1 = uDrgsGroupCh.getVersion();
             if (version == null) {
                if (version1 != null) {
                   b = false;
                }
             }else if(version.equals(version1)){
             }
             String groupCodeM = this.getGroupCodeM();
             String groupCodeM1 = uDrgsGroupCh.getGroupCodeM();
             if (groupCodeM == null) {
                if (groupCodeM1 != null) {
                label_0691 :
                   b = false;
                }
             }else if(groupCodeM.equals(groupCodeM1)){
             }
             String groupCodeS = this.getGroupCodeS();
             String groupCodeS1 = uDrgsGroupCh.getGroupCodeS();
             if (groupCodeS == null) {
                if (groupCodeS1 != null) {
                   b = false;
                }
             }else if(groupCodeS.equals(groupCodeS1)){
             }
             String reasonFlag = this.getReasonFlag();
             String reasonFlag1 = uDrgsGroupCh.getReasonFlag();
             if (reasonFlag == null) {
                if (reasonFlag1 != null) {
                label_06c5 :
                   b = false;
                }
             }else if(reasonFlag.equals(reasonFlag1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAdrgCode(){
       return this.adrgCode;
    }
    public String getAdrgFlag(){
       return this.adrgFlag;
    }
    public String getAdrgName(){
       return this.adrgName;
    }
    public String getAge(){
       return this.age;
    }
    public String getAgeFlag(){
       return this.ageFlag;
    }
    public String getAgeMatch(){
       return this.ageMatch;
    }
    public String getCcFlag(){
       return this.ccFlag;
    }
    public String getCcMatch(){
       return this.ccMatch;
    }
    public String getCcResult(){
       return this.ccResult;
    }
    public String getChsAdrgCode(){
       return this.chsAdrgCode;
    }
    public String getChsAdrgName(){
       return this.chsAdrgName;
    }
    public String getCreatedTime(){
       return this.createdTime;
    }
    public String getDfxcsFlag(){
       return this.dfxcsFlag;
    }
    public String getDfxcsMatch(){
       return this.dfxcsMatch;
    }
    public String getDrgFlag(){
       return this.drgFlag;
    }
    public String getDrgType(){
       return this.drgType;
    }
    public String getDrgsAge(){
       return this.drgsAge;
    }
    public String getDrgsCode(){
       return this.drgsCode;
    }
    public String getDrgsCodeFinal(){
       return this.drgsCodeFinal;
    }
    public String getDrgsDfxcs(){
       return this.drgsDfxcs;
    }
    public String getDrgsName(){
       return this.drgsName;
    }
    public String getDrgsNameFinal(){
       return this.drgsNameFinal;
    }
    public String getDrgsOutcome(){
       return this.drgsOutcome;
    }
    public String getDrgsSex(){
       return this.drgsSex;
    }
    public String getDrgsTrtime(){
       return this.drgsTrtime;
    }
    public String getDrgsWeight(){
       return this.drgsWeight;
    }
    public String getFactor1Match(){
       return this.factor1Match;
    }
    public String getFactor2Match(){
       return this.factor2Match;
    }
    public String getFactor3Match(){
       return this.factor3Match;
    }
    public String getFactor4Match(){
       return this.factor4Match;
    }
    public String getFactor5Match(){
       return this.factor5Match;
    }
    public String getFactorMatch(){
       return this.factorMatch;
    }
    public Integer getFactorMatchN(){
       return this.factorMatchN;
    }
    public String getGroupCodeM(){
       return this.groupCodeM;
    }
    public String getGroupCodeS(){
       return this.groupCodeS;
    }
    public Integer getMatchN(){
       return this.matchN;
    }
    public String getMdcGroup1(){
       return this.mdcGroup1;
    }
    public String getMdcGroup2(){
       return this.mdcGroup2;
    }
    public String getMessage(){
       return this.message;
    }
    public String getNl(){
       return this.nl;
    }
    public String getOtherFactorId(){
       return this.otherFactorId;
    }
    public String getOtherFactorId1(){
       return this.otherFactorId1;
    }
    public String getOtherFactorId1Result(){
       return this.otherFactorId1Result;
    }
    public String getOtherFactorId2(){
       return this.otherFactorId2;
    }
    public String getOtherFactorId2Result(){
       return this.otherFactorId2Result;
    }
    public String getOtherFactorId3(){
       return this.otherFactorId3;
    }
    public String getOtherFactorId3Result(){
       return this.otherFactorId3Result;
    }
    public String getOtherFactorId4(){
       return this.otherFactorId4;
    }
    public String getOtherFactorId4Result(){
       return this.otherFactorId4Result;
    }
    public String getOtherFactorId5(){
       return this.otherFactorId5;
    }
    public String getOtherFactorId5Result(){
       return this.otherFactorId5Result;
    }
    public String getOtherFactorResult(){
       return this.otherFactorResult;
    }
    public String getOutcomeFlag(){
       return this.outcomeFlag;
    }
    public String getOutcomeMatch(){
       return this.outcomeMatch;
    }
    public String getProjectId(){
       return this.projectId;
    }
    public String getReasonFlag(){
       return this.reasonFlag;
    }
    public String getTrtimeMatch(){
       return this.trtimeMatch;
    }
    public String getTypeMatch(){
       return this.typeMatch;
    }
    public String getUserid(){
       return this.userid;
    }
    public String getVersion(){
       return this.version;
    }
    public String getXb(){
       return this.xb;
    }
    public String getXbFlag(){
       return this.xbFlag;
    }
    public String getXbMatch(){
       return this.xbMatch;
    }
    public String getXseWeightFlag(){
       return this.xseWeightFlag;
    }
    public String getXseWeightMatch(){
       return this.xseWeightMatch;
    }
    public String getZylsh(){
       return this.zylsh;
    }
    public String getZyts(){
       return this.zyts;
    }
    public int hashCode(){
       String $drgsCode;
       int PRIME = 59;
       int result = 1;
       int i = (($drgsCode = this.getDrgsCode()) == null)? 43: $drgsCode.hashCode();
       result = i + 59;
       String $drgsName = this.getDrgsName();
       int i1 = result * 59;
       i = ($drgsName == null)? 43: $drgsName.hashCode();
       result = i1 + i;
       String $adrgCode = this.getAdrgCode();
       i1 = result * 59;
       i = ($adrgCode == null)? 43: $adrgCode.hashCode();
       result = i1 + i;
       String $chsAdrgName = this.getChsAdrgName();
       i1 = result * 59;
       i = ($chsAdrgName == null)? 43: $chsAdrgName.hashCode();
       result = i1 + i;
       String $chsAdrgCode = this.getChsAdrgCode();
       i1 = result * 59;
       i = ($chsAdrgCode == null)? 43: $chsAdrgCode.hashCode();
       result = i1 + i;
       String adrgName = this.getAdrgName();
       i1 = result * 59;
       i = (adrgName == null)? 43: adrgName.hashCode();
       String drgType = this.getDrgType();
       i1 = (i1 + i) * 59;
       i = (drgType == null)? 43: drgType.hashCode();
       String message = this.getMessage();
       i1 = (i1 + i) * 59;
       i = (message == null)? 43: message.hashCode();
       String drgsCodeFina = this.getDrgsCodeFinal();
       i1 = (i1 + i) * 59;
       i = (drgsCodeFina == null)? 43: drgsCodeFina.hashCode();
       String drgsNameFina = this.getDrgsNameFinal();
       i1 = (i1 + i) * 59;
       i = (drgsNameFina == null)? 43: drgsNameFina.hashCode();
       String drgFlag = this.getDrgFlag();
       i1 = (i1 + i) * 59;
       i = (drgFlag == null)? 43: drgFlag.hashCode();
       String zylsh = this.getZylsh();
       i1 = (i1 + i) * 59;
       i = (zylsh == null)? 43: zylsh.hashCode();
       String userid = this.getUserid();
       i1 = (i1 + i) * 59;
       i = (userid == null)? 43: userid.hashCode();
       String xb = this.getXb();
       i1 = (i1 + i) * 59;
       i = (xb == null)? 43: xb.hashCode();
       String xbFlag = this.getXbFlag();
       i1 = (i1 + i) * 59;
       i = (xbFlag == null)? 43: xbFlag.hashCode();
       String xseWeightFla = this.getXseWeightFlag();
       i1 = (i1 + i) * 59;
       i = (xseWeightFla == null)? 43: xseWeightFla.hashCode();
       String age = this.getAge();
       i1 = (i1 + i) * 59;
       i = (age == null)? 43: age.hashCode();
       String nl = this.getNl();
       i1 = (i1 + i) * 59;
       i = (nl == null)? 43: nl.hashCode();
       String ageFlag = this.getAgeFlag();
       i1 = (i1 + i) * 59;
       i = (ageFlag == null)? 43: ageFlag.hashCode();
       String zyts = this.getZyts();
       i1 = (i1 + i) * 59;
       i = (zyts == null)? 43: zyts.hashCode();
       String outcomeFlag = this.getOutcomeFlag();
       i1 = (i1 + i) * 59;
       i = (outcomeFlag == null)? 43: outcomeFlag.hashCode();
       String adrgFlag = this.getAdrgFlag();
       i1 = (i1 + i) * 59;
       i = (adrgFlag == null)? 43: adrgFlag.hashCode();
       String ccResult = this.getCcResult();
       i1 = (i1 + i) * 59;
       i = (ccResult == null)? 43: ccResult.hashCode();
       String otherFactorI = this.getOtherFactorId1Result();
       i1 = (i1 + i) * 59;
       i = (otherFactorI == null)? 43: otherFactorI.hashCode();
       String otherFactorI1 = this.getOtherFactorId2Result();
       i1 = (i1 + i) * 59;
       i = (otherFactorI1 == null)? 43: otherFactorI1.hashCode();
       String otherFactorI2 = this.getOtherFactorId3Result();
       i1 = (i1 + i) * 59;
       i = (otherFactorI2 == null)? 43: otherFactorI2.hashCode();
       String otherFactorI3 = this.getOtherFactorId4Result();
       i1 = (i1 + i) * 59;
       i = (otherFactorI3 == null)? 43: otherFactorI3.hashCode();
       String otherFactorI4 = this.getOtherFactorId5Result();
       i1 = (i1 + i) * 59;
       i = (otherFactorI4 == null)? 43: otherFactorI4.hashCode();
       String otherFactorR = this.getOtherFactorResult();
       i1 = (i1 + i) * 59;
       i = (otherFactorR == null)? 43: otherFactorR.hashCode();
       String dfxcsFlag = this.getDfxcsFlag();
       i1 = (i1 + i) * 59;
       i = (dfxcsFlag == null)? 43: dfxcsFlag.hashCode();
       String ccFlag = this.getCcFlag();
       i1 = (i1 + i) * 59;
       i = (ccFlag == null)? 43: ccFlag.hashCode();
       String otherFactorI5 = this.getOtherFactorId1();
       i1 = (i1 + i) * 59;
       i = (otherFactorI5 == null)? 43: otherFactorI5.hashCode();
       String otherFactorI6 = this.getOtherFactorId2();
       i1 = (i1 + i) * 59;
       i = (otherFactorI6 == null)? 43: otherFactorI6.hashCode();
       String otherFactorI7 = this.getOtherFactorId3();
       i1 = (i1 + i) * 59;
       i = (otherFactorI7 == null)? 43: otherFactorI7.hashCode();
       String otherFactorI8 = this.getOtherFactorId4();
       i1 = (i1 + i) * 59;
       i = (otherFactorI8 == null)? 43: otherFactorI8.hashCode();
       String otherFactorI9 = this.getOtherFactorId5();
       i1 = (i1 + i) * 59;
       i = (otherFactorI9 == null)? 43: otherFactorI9.hashCode();
       String otherFactorI10 = this.getOtherFactorId();
       i1 = (i1 + i) * 59;
       i = (otherFactorI10 == null)? 43: otherFactorI10.hashCode();
       String drgsSex = this.getDrgsSex();
       i1 = (i1 + i) * 59;
       i = (drgsSex == null)? 43: drgsSex.hashCode();
       String drgsAge = this.getDrgsAge();
       i1 = (i1 + i) * 59;
       i = (drgsAge == null)? 43: drgsAge.hashCode();
       String drgsOutcome = this.getDrgsOutcome();
       i1 = (i1 + i) * 59;
       i = (drgsOutcome == null)? 43: drgsOutcome.hashCode();
       String drgsWeight = this.getDrgsWeight();
       i1 = (i1 + i) * 59;
       i = (drgsWeight == null)? 43: drgsWeight.hashCode();
       String drgsTrtime = this.getDrgsTrtime();
       i1 = (i1 + i) * 59;
       i = (drgsTrtime == null)? 43: drgsTrtime.hashCode();
       String drgsDfxcs = this.getDrgsDfxcs();
       i1 = (i1 + i) * 59;
       i = (drgsDfxcs == null)? 43: drgsDfxcs.hashCode();
       Integer matchN = this.getMatchN();
       i1 = (i1 + i) * 59;
       i = (matchN == null)? 43: matchN.hashCode();
       String createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String typeMatch = this.getTypeMatch();
       i1 = (i1 + i) * 59;
       i = (typeMatch == null)? 43: typeMatch.hashCode();
       String ccMatch = this.getCcMatch();
       i1 = (i1 + i) * 59;
       i = (ccMatch == null)? 43: ccMatch.hashCode();
       String factor1Match = this.getFactor1Match();
       i1 = (i1 + i) * 59;
       i = (factor1Match == null)? 43: factor1Match.hashCode();
       String factor2Match = this.getFactor2Match();
       i1 = (i1 + i) * 59;
       i = (factor2Match == null)? 43: factor2Match.hashCode();
       String factor3Match = this.getFactor3Match();
       i1 = (i1 + i) * 59;
       i = (factor3Match == null)? 43: factor3Match.hashCode();
       String factor4Match = this.getFactor4Match();
       i1 = (i1 + i) * 59;
       i = (factor4Match == null)? 43: factor4Match.hashCode();
       String factor5Match = this.getFactor5Match();
       i1 = (i1 + i) * 59;
       i = (factor5Match == null)? 43: factor5Match.hashCode();
       String xbMatch = this.getXbMatch();
       i1 = (i1 + i) * 59;
       i = (xbMatch == null)? 43: xbMatch.hashCode();
       String ageMatch = this.getAgeMatch();
       i1 = (i1 + i) * 59;
       i = (ageMatch == null)? 43: ageMatch.hashCode();
       String outcomeMatch = this.getOutcomeMatch();
       i1 = (i1 + i) * 59;
       i = (outcomeMatch == null)? 43: outcomeMatch.hashCode();
       String xseWeightMat = this.getXseWeightMatch();
       i1 = (i1 + i) * 59;
       i = (xseWeightMat == null)? 43: xseWeightMat.hashCode();
       String trtimeMatch = this.getTrtimeMatch();
       i1 = (i1 + i) * 59;
       i = (trtimeMatch == null)? 43: trtimeMatch.hashCode();
       String dfxcsMatch = this.getDfxcsMatch();
       i1 = (i1 + i) * 59;
       i = (dfxcsMatch == null)? 43: dfxcsMatch.hashCode();
       String factorMatch = this.getFactorMatch();
       i1 = (i1 + i) * 59;
       i = (factorMatch == null)? 43: factorMatch.hashCode();
       Integer factorMatchN = this.getFactorMatchN();
       i1 = (i1 + i) * 59;
       i = (factorMatchN == null)? 43: factorMatchN.hashCode();
       String mdcGroup1 = this.getMdcGroup1();
       i1 = (i1 + i) * 59;
       i = (mdcGroup1 == null)? 43: mdcGroup1.hashCode();
       String mdcGroup2 = this.getMdcGroup2();
       i1 = (i1 + i) * 59;
       i = (mdcGroup2 == null)? 43: mdcGroup2.hashCode();
       String projectId = this.getProjectId();
       i1 = (i1 + i) * 59;
       i = (projectId == null)? 43: projectId.hashCode();
       String version = this.getVersion();
       i1 = (i1 + i) * 59;
       i = (version == null)? 43: version.hashCode();
       String groupCodeM = this.getGroupCodeM();
       i1 = (i1 + i) * 59;
       i = (groupCodeM == null)? 43: groupCodeM.hashCode();
       String groupCodeS = this.getGroupCodeS();
       i1 = (i1 + i) * 59;
       i = (groupCodeS == null)? 43: groupCodeS.hashCode();
       String reasonFlag = this.getReasonFlag();
       i1 = (i1 + i) * 59;
       i = (reasonFlag == null)? 43: reasonFlag.hashCode();
       return (i1 + i);
    }
    public void setAdrgCode(String adrgCode){
       this.adrgCode = adrgCode;
    }
    public void setAdrgFlag(String adrgFlag){
       this.adrgFlag = adrgFlag;
    }
    public void setAdrgName(String adrgName){
       this.adrgName = adrgName;
    }
    public void setAge(String age){
       this.age = age;
    }
    public void setAgeFlag(String ageFlag){
       this.ageFlag = ageFlag;
    }
    public void setAgeMatch(String ageMatch){
       this.ageMatch = ageMatch;
    }
    public void setCcFlag(String ccFlag){
       this.ccFlag = ccFlag;
    }
    public void setCcMatch(String ccMatch){
       this.ccMatch = ccMatch;
    }
    public void setCcResult(String ccResult){
       this.ccResult = ccResult;
    }
    public void setChsAdrgCode(String chsAdrgCode){
       this.chsAdrgCode = chsAdrgCode;
    }
    public void setChsAdrgName(String chsAdrgName){
       this.chsAdrgName = chsAdrgName;
    }
    public void setCreatedTime(String createdTime){
       this.createdTime = createdTime;
    }
    public void setDfxcsFlag(String dfxcsFlag){
       this.dfxcsFlag = dfxcsFlag;
    }
    public void setDfxcsMatch(String dfxcsMatch){
       this.dfxcsMatch = dfxcsMatch;
    }
    public void setDrgFlag(String drgFlag){
       this.drgFlag = drgFlag;
    }
    public void setDrgType(String drgType){
       this.drgType = drgType;
    }
    public void setDrgsAge(String drgsAge){
       this.drgsAge = drgsAge;
    }
    public void setDrgsCode(String drgsCode){
       this.drgsCode = drgsCode;
    }
    public void setDrgsCodeFinal(String drgsCodeFinal){
       this.drgsCodeFinal = drgsCodeFinal;
    }
    public void setDrgsDfxcs(String drgsDfxcs){
       this.drgsDfxcs = drgsDfxcs;
    }
    public void setDrgsName(String drgsName){
       this.drgsName = drgsName;
    }
    public void setDrgsNameFinal(String drgsNameFinal){
       this.drgsNameFinal = drgsNameFinal;
    }
    public void setDrgsOutcome(String drgsOutcome){
       this.drgsOutcome = drgsOutcome;
    }
    public void setDrgsSex(String drgsSex){
       this.drgsSex = drgsSex;
    }
    public void setDrgsTrtime(String drgsTrtime){
       this.drgsTrtime = drgsTrtime;
    }
    public void setDrgsWeight(String drgsWeight){
       this.drgsWeight = drgsWeight;
    }
    public void setFactor1Match(String factor1Match){
       this.factor1Match = factor1Match;
    }
    public void setFactor2Match(String factor2Match){
       this.factor2Match = factor2Match;
    }
    public void setFactor3Match(String factor3Match){
       this.factor3Match = factor3Match;
    }
    public void setFactor4Match(String factor4Match){
       this.factor4Match = factor4Match;
    }
    public void setFactor5Match(String factor5Match){
       this.factor5Match = factor5Match;
    }
    public void setFactorMatch(String factorMatch){
       this.factorMatch = factorMatch;
    }
    public void setFactorMatchN(Integer factorMatchN){
       this.factorMatchN = factorMatchN;
    }
    public void setGroupCodeM(String groupCodeM){
       this.groupCodeM = groupCodeM;
    }
    public void setGroupCodeS(String groupCodeS){
       this.groupCodeS = groupCodeS;
    }
    public void setMatchN(Integer matchN){
       this.matchN = matchN;
    }
    public void setMdcGroup1(String mdcGroup1){
       this.mdcGroup1 = mdcGroup1;
    }
    public void setMdcGroup2(String mdcGroup2){
       this.mdcGroup2 = mdcGroup2;
    }
    public void setMessage(String message){
       this.message = message;
    }
    public void setNl(String nl){
       this.nl = nl;
    }
    public void setOtherFactorId(String otherFactorId){
       this.otherFactorId = otherFactorId;
    }
    public void setOtherFactorId1(String otherFactorId1){
       this.otherFactorId1 = otherFactorId1;
    }
    public void setOtherFactorId1Result(String otherFactorId1Result){
       this.otherFactorId1Result = otherFactorId1Result;
    }
    public void setOtherFactorId2(String otherFactorId2){
       this.otherFactorId2 = otherFactorId2;
    }
    public void setOtherFactorId2Result(String otherFactorId2Result){
       this.otherFactorId2Result = otherFactorId2Result;
    }
    public void setOtherFactorId3(String otherFactorId3){
       this.otherFactorId3 = otherFactorId3;
    }
    public void setOtherFactorId3Result(String otherFactorId3Result){
       this.otherFactorId3Result = otherFactorId3Result;
    }
    public void setOtherFactorId4(String otherFactorId4){
       this.otherFactorId4 = otherFactorId4;
    }
    public void setOtherFactorId4Result(String otherFactorId4Result){
       this.otherFactorId4Result = otherFactorId4Result;
    }
    public void setOtherFactorId5(String otherFactorId5){
       this.otherFactorId5 = otherFactorId5;
    }
    public void setOtherFactorId5Result(String otherFactorId5Result){
       this.otherFactorId5Result = otherFactorId5Result;
    }
    public void setOtherFactorResult(String otherFactorResult){
       this.otherFactorResult = otherFactorResult;
    }
    public void setOutcomeFlag(String outcomeFlag){
       this.outcomeFlag = outcomeFlag;
    }
    public void setOutcomeMatch(String outcomeMatch){
       this.outcomeMatch = outcomeMatch;
    }
    public void setProjectId(String projectId){
       this.projectId = projectId;
    }
    public void setReasonFlag(String reasonFlag){
       this.reasonFlag = reasonFlag;
    }
    public void setTrtimeMatch(String trtimeMatch){
       this.trtimeMatch = trtimeMatch;
    }
    public void setTypeMatch(String typeMatch){
       this.typeMatch = typeMatch;
    }
    public void setUserid(String userid){
       this.userid = userid;
    }
    public void setVersion(String version){
       this.version = version;
    }
    public void setXb(String xb){
       this.xb = xb;
    }
    public void setXbFlag(String xbFlag){
       this.xbFlag = xbFlag;
    }
    public void setXbMatch(String xbMatch){
       this.xbMatch = xbMatch;
    }
    public void setXseWeightFlag(String xseWeightFlag){
       this.xseWeightFlag = xseWeightFlag;
    }
    public void setXseWeightMatch(String xseWeightMatch){
       this.xseWeightMatch = xseWeightMatch;
    }
    public void setZylsh(String zylsh){
       this.zylsh = zylsh;
    }
    public void setZyts(String zyts){
       this.zyts = zyts;
    }
    public String toString(){
       return "DrgsGroupChsVo\(drgsCode="+this.getDrgsCode()+", drgsName="+this.getDrgsName()+", adrgCode="+this.getAdrgCode()+", chsAdrgName="+this.getChsAdrgName()+", chsAdrgCode="+this.getChsAdrgCode()+", adrgName="+this.getAdrgName()+", drgType="+this.getDrgType()+", message="+this.getMessage()+", drgsCodeFinal="+this.getDrgsCodeFinal()+", drgsNameFinal="+this.getDrgsNameFinal()+", drgFlag="+this.getDrgFlag()+", zylsh="+this.getZylsh()+", userid="+this.getUserid()+", xb="+this.getXb()+", xbFlag="+this.getXbFlag()+", xseWeightFlag="+this.getXseWeightFlag()+", age="+this.getAge()+", nl="+this.getNl()+", ageFlag="+this.getAgeFlag()+", zyts="+this.getZyts()+", outcomeFlag="+this.getOutcomeFlag()+", adrgFlag="+this.getAdrgFlag()+", ccResult="+this.getCcResult()+", otherFactorId1Result="+this.getOtherFactorId1Result()+", otherFactorId2Result="+this.getOtherFactorId2Result()+", otherFactorId3Result="+this.getOtherFactorId3Result()+", otherFactorId4Result="+this.getOtherFactorId4Result()+", otherFactorId5Result="+this.getOtherFactorId5Result()+", otherFactorResult="+this.getOtherFactorResult()+", dfxcsFlag="+this.getDfxcsFlag()+", ccFlag="+this.getCcFlag()+", otherFactorId1="+this.getOtherFactorId1()+", otherFactorId2="+this.getOtherFactorId2()+", otherFactorId3="+this.getOtherFactorId3()+", otherFactorId4="+this.getOtherFactorId4()+", otherFactorId5="+this.getOtherFactorId5()+", otherFactorId="+this.getOtherFactorId()+", drgsSex="+this.getDrgsSex()+", drgsAge="+this.getDrgsAge()+", drgsOutcome="+this.getDrgsOutcome()+", drgsWeight="+this.getDrgsWeight()+", drgsTrtime="+this.getDrgsTrtime()+", drgsDfxcs="+this.getDrgsDfxcs()+", matchN="+this.getMatchN()+", createdTime="+this.getCreatedTime()+", typeMatch="+this.getTypeMatch()+", ccMatch="+this.getCcMatch()+", factor1Match="+this.getFactor1Match()+", factor2Match="+this.getFactor2Match()+", factor3Match="+this.getFactor3Match()+", factor4Match="+this.getFactor4Match()+", factor5Match="+this.getFactor5Match()+", xbMatch="+this.getXbMatch()+"
    , ageMatch="+this.getAgeMatch()+", outcomeMatch="+this.getOutcomeMatch()+", xseWeightMatch="+this.getXseWeightMatch()+", trtimeMatch="+this.getTrtimeMatch()+", dfxcsMatch="+this.getDfxcsMatch()+", factorMatch="+this.getFactorMatch()+", factorMatchN="+this.getFactorMatchN()+", mdcGroup1="+this.getMdcGroup1()+", mdcGroup2="+this.getMdcGroup2()+", projectId="+this.getProjectId()+", version="+this.getVersion()+", groupCodeM="+this.getGroupCodeM()+", groupCodeS="+this.getGroupCodeS()+", reasonFlag="+this.getReasonFlag()+"\)";
    }
}
