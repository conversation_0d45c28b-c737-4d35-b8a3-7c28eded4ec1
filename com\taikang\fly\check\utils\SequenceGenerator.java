package com.taikang.fly.check.utils.SequenceGenerator;
import java.lang.Object;
import java.lang.String;
import com.fasterxml.uuid.impl.TimeBasedGenerator;
import com.fasterxml.uuid.Generators;
import java.util.UUID;

public class SequenceGenerator	// class@00034a from classes.dex
{

    public void SequenceGenerator(){
       super();
    }
    public static String getId(){
       return Generators.timeBasedGenerator().generate().toString();
    }
}
