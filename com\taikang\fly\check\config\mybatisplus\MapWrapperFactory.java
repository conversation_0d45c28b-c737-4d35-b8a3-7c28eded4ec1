package com.taikang.fly.check.config.mybatisplus.MapWrapperFactory;
import org.apache.ibatis.reflection.wrapper.ObjectWrapperFactory;
import java.lang.Object;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.wrapper.ObjectWrapper;
import com.taikang.fly.check.config.mybatisplus.CustomWrapper;
import java.util.Map;

public class MapWrapperFactory implements ObjectWrapperFactory	// class@000096 from classes.dex
{

    public void MapWrapperFactory(){
       super();
    }
    public ObjectWrapper getWrapperFor(MetaObject metaObject,Object object){
       return new CustomWrapper(metaObject, object);
    }
    public boolean hasWrapperFor(Object object){
       return object instanceof Map;
    }
}
