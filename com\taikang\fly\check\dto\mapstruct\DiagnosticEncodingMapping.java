package com.taikang.fly.check.dto.mapstruct.DiagnosticEncodingMapping;
import com.taikang.fly.check.mybatis.domain.GlDiagnosticEncoding;
import com.taikang.fly.check.dto.diagnosticencoding.DiagnosticEncodingDto;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.YbDiagnosticEncoding;

public interface abstract DiagnosticEncodingMapping	// class@000148 from classes.dex
{

    DiagnosticEncodingDto toGlDto(GlDiagnosticEncoding p0);
    List toGlDtoList(List p0);
    GlDiagnosticEncoding toGlEntity(DiagnosticEncodingDto p0);
    List toGlEntityList(List p0);
    DiagnosticEncodingDto toYbDto(YbDiagnosticEncoding p0);
    List toYbDtoList(List p0);
    YbDiagnosticEncoding toYbEntity(DiagnosticEncodingDto p0);
    List toYbEntityList(List p0);
}
