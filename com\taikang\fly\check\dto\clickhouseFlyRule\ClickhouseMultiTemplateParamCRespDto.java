package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseMultiTemplateParamCRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ClickhouseMultiTemplateParamCRespDto implements Serializable	// class@0000cc from classes.dex
{
    private String paramCodeOne;
    private String paramCodeTwo;
    private String paramDesc;
    private String paramNameOne;
    private String paramNameTwo;
    private String paramRuleName;
    private String paramType;
    private String ruleSql;
    private String ruleparam;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseMultiTemplateParamCRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseMultiTemplateParamCRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseMultiTemplateParamCRespDto){
          b = false;
       }else {
          ClickhouseMultiTemplateParamCRespDto uClickhouseM = o;
          if (!uClickhouseM.canEqual(this)) {
             b = false;
          }else {
             String paramCodeOne = this.getParamCodeOne();
             String paramCodeOne1 = uClickhouseM.getParamCodeOne();
             if (paramCodeOne == null) {
                if (paramCodeOne1 != null) {
                   b = false;
                }
             }else if(paramCodeOne.equals(paramCodeOne1)){
             }
             String paramNameOne = this.getParamNameOne();
             String paramNameOne1 = uClickhouseM.getParamNameOne();
             if (paramNameOne == null) {
                if (paramNameOne1 != null) {
                   b = false;
                }
             }else if(paramNameOne.equals(paramNameOne1)){
             }
             String paramCodeTwo = this.getParamCodeTwo();
             String paramCodeTwo1 = uClickhouseM.getParamCodeTwo();
             if (paramCodeTwo == null) {
                if (paramCodeTwo1 != null) {
                   b = false;
                }
             }else if(paramCodeTwo.equals(paramCodeTwo1)){
             }
             String paramNameTwo = this.getParamNameTwo();
             String paramNameTwo1 = uClickhouseM.getParamNameTwo();
             if (paramNameTwo == null) {
                if (paramNameTwo1 != null) {
                   b = false;
                }
             }else if(paramNameTwo.equals(paramNameTwo1)){
             }
             String paramDesc = this.getParamDesc();
             String paramDesc1 = uClickhouseM.getParamDesc();
             if (paramDesc == null) {
                if (paramDesc1 != null) {
                   b = false;
                }
             }else if(paramDesc.equals(paramDesc1)){
             }
             String paramType = this.getParamType();
             String paramType1 = uClickhouseM.getParamType();
             if (paramType == null) {
                if (paramType1 != null) {
                   b = false;
                }
             }else if(paramType.equals(paramType1)){
             }
             String paramRuleNam = this.getParamRuleName();
             String paramRuleNam1 = uClickhouseM.getParamRuleName();
             if (paramRuleNam == null) {
                if (paramRuleNam1 != null) {
                   b = false;
                }
             }else if(paramRuleNam.equals(paramRuleNam1)){
             }
             String ruleSql = this.getRuleSql();
             String ruleSql1 = uClickhouseM.getRuleSql();
             if (ruleSql == null) {
                if (ruleSql1 != null) {
                label_00ca :
                   b = false;
                }
             }else if(ruleSql.equals(ruleSql1)){
             }
             String ruleparam = this.getRuleparam();
             String ruleparam1 = uClickhouseM.getRuleparam();
             if (ruleparam == null) {
                if (ruleparam1 != null) {
                   b = false;
                }
             }else if(ruleparam.equals(ruleparam1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getParamCodeOne(){
       return this.paramCodeOne;
    }
    public String getParamCodeTwo(){
       return this.paramCodeTwo;
    }
    public String getParamDesc(){
       return this.paramDesc;
    }
    public String getParamNameOne(){
       return this.paramNameOne;
    }
    public String getParamNameTwo(){
       return this.paramNameTwo;
    }
    public String getParamRuleName(){
       return this.paramRuleName;
    }
    public String getParamType(){
       return this.paramType;
    }
    public String getRuleSql(){
       return this.ruleSql;
    }
    public String getRuleparam(){
       return this.ruleparam;
    }
    public int hashCode(){
       String $paramCodeOne;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($paramCodeOne = this.getParamCodeOne()) == null)? i: $paramCodeOne.hashCode();
       result = i1 + 59;
       String $paramNameOne = this.getParamNameOne();
       int i2 = result * 59;
       i1 = ($paramNameOne == null)? i: $paramNameOne.hashCode();
       result = i2 + i1;
       String $paramCodeTwo = this.getParamCodeTwo();
       i2 = result * 59;
       i1 = ($paramCodeTwo == null)? i: $paramCodeTwo.hashCode();
       result = i2 + i1;
       String $paramNameTwo = this.getParamNameTwo();
       i2 = result * 59;
       i1 = ($paramNameTwo == null)? i: $paramNameTwo.hashCode();
       result = i2 + i1;
       String $paramDesc = this.getParamDesc();
       i2 = result * 59;
       i1 = ($paramDesc == null)? i: $paramDesc.hashCode();
       result = i2 + i1;
       String paramType = this.getParamType();
       i2 = result * 59;
       i1 = (paramType == null)? i: paramType.hashCode();
       String paramRuleNam = this.getParamRuleName();
       i2 = (i2 + i1) * 59;
       i1 = (paramRuleNam == null)? i: paramRuleNam.hashCode();
       String ruleSql = this.getRuleSql();
       i2 = (i2 + i1) * 59;
       i1 = (ruleSql == null)? i: ruleSql.hashCode();
       String ruleparam = this.getRuleparam();
       i1 = (i2 + i1) * 59;
       if (ruleparam != null) {
          i = ruleparam.hashCode();
       }
       return (i1 + i);
    }
    public void setParamCodeOne(String paramCodeOne){
       this.paramCodeOne = paramCodeOne;
    }
    public void setParamCodeTwo(String paramCodeTwo){
       this.paramCodeTwo = paramCodeTwo;
    }
    public void setParamDesc(String paramDesc){
       this.paramDesc = paramDesc;
    }
    public void setParamNameOne(String paramNameOne){
       this.paramNameOne = paramNameOne;
    }
    public void setParamNameTwo(String paramNameTwo){
       this.paramNameTwo = paramNameTwo;
    }
    public void setParamRuleName(String paramRuleName){
       this.paramRuleName = paramRuleName;
    }
    public void setParamType(String paramType){
       this.paramType = paramType;
    }
    public void setRuleSql(String ruleSql){
       this.ruleSql = ruleSql;
    }
    public void setRuleparam(String ruleparam){
       this.ruleparam = ruleparam;
    }
    public String toString(){
       return "ClickhouseMultiTemplateParamCRespDto\(paramCodeOne="+this.getParamCodeOne()+", paramNameOne="+this.getParamNameOne()+", paramCodeTwo="+this.getParamCodeTwo()+", paramNameTwo="+this.getParamNameTwo()+", paramDesc="+this.getParamDesc()+", paramType="+this.getParamType()+", paramRuleName="+this.getParamRuleName()+", ruleSql="+this.getRuleSql()+", ruleparam="+this.getRuleparam()+"\)";
    }
}
