package com.taikang.fly.check.vo.drg.DrgPreGroupQueryVo;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.math.BigDecimal;
import java.lang.StringBuilder;

public class DrgPreGroupQueryVo implements Serializable	// class@000373 from classes.dex
{
    private String drgsCode;
    private String drgsName;
    private String newDrgsCode;
    private String newDrgsName;
    private String newWeight;
    private BigDecimal weight;
    private String ywlsh;
    private String zylsh;

    public void DrgPreGroupQueryVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgPreGroupQueryVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgPreGroupQueryVo){
          b = false;
       }else {
          DrgPreGroupQueryVo uDrgPreGroup = o;
          if (!uDrgPreGroup.canEqual(this)) {
             b = false;
          }else {
             String zylsh = this.getZylsh();
             String zylsh1 = uDrgPreGroup.getZylsh();
             if (zylsh == null) {
                if (zylsh1 != null) {
                   b = false;
                }
             }else if(zylsh.equals(zylsh1)){
             }
             String ywlsh = this.getYwlsh();
             String ywlsh1 = uDrgPreGroup.getYwlsh();
             if (ywlsh == null) {
                if (ywlsh1 != null) {
                   b = false;
                }
             }else if(ywlsh.equals(ywlsh1)){
             }
             String drgsCode = this.getDrgsCode();
             String drgsCode1 = uDrgPreGroup.getDrgsCode();
             if (drgsCode == null) {
                if (drgsCode1 != null) {
                   b = false;
                }
             }else if(drgsCode.equals(drgsCode1)){
             }
             String drgsName = this.getDrgsName();
             String drgsName1 = uDrgPreGroup.getDrgsName();
             if (drgsName == null) {
                if (drgsName1 != null) {
                   b = false;
                }
             }else if(drgsName.equals(drgsName1)){
             }
             BigDecimal weight = this.getWeight();
             BigDecimal weight1 = uDrgPreGroup.getWeight();
             if (weight == null) {
                if (weight1 != null) {
                   b = false;
                }
             }else if(weight.equals(weight1)){
             }
             String newDrgsCode = this.getNewDrgsCode();
             String newDrgsCode1 = uDrgPreGroup.getNewDrgsCode();
             if (newDrgsCode == null) {
                if (newDrgsCode1 != null) {
                   b = false;
                }
             }else if(newDrgsCode.equals(newDrgsCode1)){
             }
             String newDrgsName = this.getNewDrgsName();
             String newDrgsName1 = uDrgPreGroup.getNewDrgsName();
             if (newDrgsName == null) {
                if (newDrgsName1 != null) {
                label_00b5 :
                   b = false;
                }
             }else if(newDrgsName.equals(newDrgsName1)){
             }
             String newWeight = this.getNewWeight();
             String newWeight1 = uDrgPreGroup.getNewWeight();
             if (newWeight == null) {
                if (newWeight1 != null) {
                   b = false;
                }
             }else if(newWeight.equals(newWeight1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDrgsCode(){
       return this.drgsCode;
    }
    public String getDrgsName(){
       return this.drgsName;
    }
    public String getNewDrgsCode(){
       return this.newDrgsCode;
    }
    public String getNewDrgsName(){
       return this.newDrgsName;
    }
    public String getNewWeight(){
       return this.newWeight;
    }
    public BigDecimal getWeight(){
       return this.weight;
    }
    public String getYwlsh(){
       return this.ywlsh;
    }
    public String getZylsh(){
       return this.zylsh;
    }
    public int hashCode(){
       String $zylsh;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($zylsh = this.getZylsh()) == null)? i: $zylsh.hashCode();
       result = i1 + 59;
       String $ywlsh = this.getYwlsh();
       int i2 = result * 59;
       i1 = ($ywlsh == null)? i: $ywlsh.hashCode();
       result = i2 + i1;
       String $drgsCode = this.getDrgsCode();
       i2 = result * 59;
       i1 = ($drgsCode == null)? i: $drgsCode.hashCode();
       result = i2 + i1;
       String $drgsName = this.getDrgsName();
       i2 = result * 59;
       i1 = ($drgsName == null)? i: $drgsName.hashCode();
       result = i2 + i1;
       BigDecimal $weight = this.getWeight();
       i2 = result * 59;
       i1 = ($weight == null)? i: $weight.hashCode();
       result = i2 + i1;
       String newDrgsCode = this.getNewDrgsCode();
       i2 = result * 59;
       i1 = (newDrgsCode == null)? i: newDrgsCode.hashCode();
       String newDrgsName = this.getNewDrgsName();
       i2 = (i2 + i1) * 59;
       i1 = (newDrgsName == null)? i: newDrgsName.hashCode();
       String newWeight = this.getNewWeight();
       i1 = (i2 + i1) * 59;
       if (newWeight != null) {
          i = newWeight.hashCode();
       }
       return (i1 + i);
    }
    public void setDrgsCode(String drgsCode){
       this.drgsCode = drgsCode;
    }
    public void setDrgsName(String drgsName){
       this.drgsName = drgsName;
    }
    public void setNewDrgsCode(String newDrgsCode){
       this.newDrgsCode = newDrgsCode;
    }
    public void setNewDrgsName(String newDrgsName){
       this.newDrgsName = newDrgsName;
    }
    public void setNewWeight(String newWeight){
       this.newWeight = newWeight;
    }
    public void setWeight(BigDecimal weight){
       this.weight = weight;
    }
    public void setYwlsh(String ywlsh){
       this.ywlsh = ywlsh;
    }
    public void setZylsh(String zylsh){
       this.zylsh = zylsh;
    }
    public String toString(){
       return "DrgPreGroupQueryVo\(zylsh="+this.getZylsh()+", ywlsh="+this.getYwlsh()+", drgsCode="+this.getDrgsCode()+", drgsName="+this.getDrgsName()+", weight="+this.getWeight()+", newDrgsCode="+this.getNewDrgsCode()+", newDrgsName="+this.getNewDrgsName()+", newWeight="+this.getNewWeight()+"\)";
    }
}
