package org.springframework.boot.loader.jar.JarFileEntries$EntryIterator;
import java.util.Iterator;
import org.springframework.boot.loader.jar.JarFileEntries;
import java.lang.Object;
import org.springframework.boot.loader.jar.JarFileEntries$1;
import org.springframework.boot.loader.jar.JarEntry;
import java.util.NoSuchElementException;
import java.lang.Class;
import org.springframework.boot.loader.jar.AsciiBytes;
import org.springframework.boot.loader.jar.FileHeader;

class JarFileEntries$EntryIterator implements Iterator	// class@000558 from classes.dex
{
    private int index;
    final JarFileEntries this$0;

    private void JarFileEntries$EntryIterator(JarFileEntries p0){
       this.this$0 = p0;
       super();
       this.index = 0;
    }
    void JarFileEntries$EntryIterator(JarFileEntries x0,JarFileEntries$1 x1){
       super(x0);
    }
    public boolean hasNext(){
       boolean b = (this.index < JarFileEntries.access$200(this.this$0))? true: false;
       return b;
    }
    public Object next(){
       return this.next();
    }
    public JarEntry next(){
       if (!this.hasNext()) {
          throw new NoSuchElementException();
       }
       this.index = this.index + 1;
       return JarFileEntries.access$400(this.this$0, JarFileEntries.access$300(this.this$0)[this.index], JarEntry.class, false, null);
    }
}
