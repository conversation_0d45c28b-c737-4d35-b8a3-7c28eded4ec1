package com.taikang.fly.check.mybatis.dao.PlanRuleMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.PlanRule;
import com.alibaba.fastjson.JSONObject;
import java.lang.Integer;

public interface abstract PlanRuleMapper implements BaseMapper	// class@000217 from classes.dex
{

    void deleteByRuleIds(List p0);
    PlanRule selectByIdAndStatus(String p0);
    List selectPlanRuleByPage(JSONObject p0);
    Integer updatePlanRuleStatus(String p0,String p1);
}
