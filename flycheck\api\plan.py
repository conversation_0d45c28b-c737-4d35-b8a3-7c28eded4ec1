"""
检查计划API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session

from flycheck.database import get_db
from flycheck.models import Plan, PlanLog
from flycheck.schemas.plan import (
    PlanCreate, 
    PlanUpdate, 
    PlanResponse,
    PlanLogResponse
)
from flycheck.services.plan_service import PlanService
from flycheck.core.plan_executor import PlanExecutor

router = APIRouter()


@router.get("/", response_model=List[PlanResponse])
async def get_plans(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    plan_name: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取计划列表"""
    service = PlanService(db)
    return service.get_plans(
        skip=skip, 
        limit=limit,
        plan_name=plan_name,
        status=status
    )


@router.get("/{plan_id}", response_model=PlanResponse)
async def get_plan(plan_id: str, db: Session = Depends(get_db)):
    """获取单个计划"""
    service = PlanService(db)
    plan = service.get_plan_by_id(plan_id)
    if not plan:
        raise HTTPException(status_code=404, detail="计划不存在")
    return plan


@router.post("/", response_model=PlanResponse)
async def create_plan(plan_data: PlanCreate, db: Session = Depends(get_db)):
    """创建计划"""
    service = PlanService(db)
    return service.create_plan(plan_data)


@router.put("/{plan_id}", response_model=PlanResponse)
async def update_plan(
    plan_id: str, 
    plan_data: PlanUpdate, 
    db: Session = Depends(get_db)
):
    """更新计划"""
    service = PlanService(db)
    plan = service.update_plan(plan_id, plan_data)
    if not plan:
        raise HTTPException(status_code=404, detail="计划不存在")
    return plan


@router.delete("/{plan_id}")
async def delete_plan(plan_id: str, db: Session = Depends(get_db)):
    """删除计划"""
    service = PlanService(db)
    success = service.delete_plan(plan_id)
    if not success:
        raise HTTPException(status_code=404, detail="计划不存在")
    return {"message": "计划删除成功"}


@router.post("/{plan_id}/execute")
async def execute_plan(
    plan_id: str,
    background_tasks: BackgroundTasks,
    params: dict = None,
    db: Session = Depends(get_db)
):
    """执行计划"""
    service = PlanService(db)
    plan = service.get_plan_by_id(plan_id)
    if not plan:
        raise HTTPException(status_code=404, detail="计划不存在")
    
    if plan.status != "active":
        raise HTTPException(status_code=400, detail="计划状态不正确，无法执行")
    
    # 在后台执行计划
    def execute_plan_task():
        executor = PlanExecutor(db)
        try:
            result = executor.execute_plan(plan_id, params or {})
            return result
        except Exception as e:
            print(f"计划执行失败: {e}")
            raise
    
    background_tasks.add_task(execute_plan_task)
    
    return {"message": "计划已开始执行", "plan_id": plan_id}


@router.post("/{plan_id}/stop")
async def stop_plan(plan_id: str, db: Session = Depends(get_db)):
    """停止计划执行"""
    executor = PlanExecutor(db)
    success = executor.stop_plan(plan_id)
    
    if success:
        return {"message": "计划已停止执行"}
    else:
        raise HTTPException(status_code=400, detail="停止计划失败")


@router.get("/{plan_id}/status")
async def get_plan_status(plan_id: str, db: Session = Depends(get_db)):
    """获取计划执行状态"""
    executor = PlanExecutor(db)
    try:
        status = executor.get_plan_status(plan_id)
        return status
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/{plan_id}/logs", response_model=List[PlanLogResponse])
async def get_plan_logs(
    plan_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取计划执行日志"""
    logs = (
        db.query(PlanLog)
        .filter(PlanLog.plan_id == plan_id)
        .order_by(PlanLog.created_time.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )
    return logs


@router.post("/{plan_id}/activate")
async def activate_plan(plan_id: str, db: Session = Depends(get_db)):
    """激活计划"""
    service = PlanService(db)
    success = service.activate_plan(plan_id)
    if not success:
        raise HTTPException(status_code=404, detail="计划不存在")
    return {"message": "计划已激活"}


@router.post("/{plan_id}/deactivate")
async def deactivate_plan(plan_id: str, db: Session = Depends(get_db)):
    """停用计划"""
    service = PlanService(db)
    success = service.deactivate_plan(plan_id)
    if not success:
        raise HTTPException(status_code=404, detail="计划不存在")
    return {"message": "计划已停用"}
