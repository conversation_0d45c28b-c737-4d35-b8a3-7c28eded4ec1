package com.taikang.fly.check.vo.drg.DrgCheckAuditTaskResultVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgCheckAuditTaskResultVo	// class@00035f from classes.dex
{
    private String chkTaskBchno;
    private String createTimeEnd;
    private String createTimeStart;
    private String fixmedinsName;
    private String ruleName;
    private String ruleNatu;

    public void DrgCheckAuditTaskResultVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgCheckAuditTaskResultVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgCheckAuditTaskResultVo){
          b = false;
       }else {
          DrgCheckAuditTaskResultVo uDrgCheckAud = o;
          if (!uDrgCheckAud.canEqual(this)) {
             b = false;
          }else {
             String fixmedinsNam = this.getFixmedinsName();
             String fixmedinsNam1 = uDrgCheckAud.getFixmedinsName();
             if (fixmedinsNam == null) {
                if (fixmedinsNam1 != null) {
                   b = false;
                }
             }else if(fixmedinsNam.equals(fixmedinsNam1)){
             }
             String chkTaskBchno = this.getChkTaskBchno();
             String chkTaskBchno1 = uDrgCheckAud.getChkTaskBchno();
             if (chkTaskBchno == null) {
                if (chkTaskBchno1 != null) {
                   b = false;
                }
             }else if(chkTaskBchno.equals(chkTaskBchno1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uDrgCheckAud.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleNatu = this.getRuleNatu();
             String ruleNatu1 = uDrgCheckAud.getRuleNatu();
             if (ruleNatu == null) {
                if (ruleNatu1 != null) {
                   b = false;
                }
             }else if(ruleNatu.equals(ruleNatu1)){
             }
             String createTimeSt = this.getCreateTimeStart();
             String createTimeSt1 = uDrgCheckAud.getCreateTimeStart();
             if (createTimeSt == null) {
                if (createTimeSt1 != null) {
                   b = false;
                }
             }else if(createTimeSt.equals(createTimeSt1)){
             }
             String createTimeEn = this.getCreateTimeEnd();
             String createTimeEn1 = uDrgCheckAud.getCreateTimeEnd();
             if (createTimeEn == null) {
                if (createTimeEn1 != null) {
                   b = false;
                }
             }else if(createTimeEn.equals(createTimeEn1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getChkTaskBchno(){
       return this.chkTaskBchno;
    }
    public String getCreateTimeEnd(){
       return this.createTimeEnd;
    }
    public String getCreateTimeStart(){
       return this.createTimeStart;
    }
    public String getFixmedinsName(){
       return this.fixmedinsName;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleNatu(){
       return this.ruleNatu;
    }
    public int hashCode(){
       String $fixmedinsName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($fixmedinsName = this.getFixmedinsName()) == null)? i: $fixmedinsName.hashCode();
       result = i1 + 59;
       String $chkTaskBchno = this.getChkTaskBchno();
       int i2 = result * 59;
       i1 = ($chkTaskBchno == null)? i: $chkTaskBchno.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $ruleNatu = this.getRuleNatu();
       i2 = result * 59;
       i1 = ($ruleNatu == null)? i: $ruleNatu.hashCode();
       result = i2 + i1;
       String $createTimeStart = this.getCreateTimeStart();
       i2 = result * 59;
       i1 = ($createTimeStart == null)? i: $createTimeStart.hashCode();
       result = i2 + i1;
       String createTimeEn = this.getCreateTimeEnd();
       i1 = result * 59;
       if (createTimeEn != null) {
          i = createTimeEn.hashCode();
       }
       return (i1 + i);
    }
    public void setChkTaskBchno(String chkTaskBchno){
       this.chkTaskBchno = chkTaskBchno;
    }
    public void setCreateTimeEnd(String createTimeEnd){
       this.createTimeEnd = createTimeEnd;
    }
    public void setCreateTimeStart(String createTimeStart){
       this.createTimeStart = createTimeStart;
    }
    public void setFixmedinsName(String fixmedinsName){
       this.fixmedinsName = fixmedinsName;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleNatu(String ruleNatu){
       this.ruleNatu = ruleNatu;
    }
    public String toString(){
       return "DrgCheckAuditTaskResultVo\(fixmedinsName="+this.getFixmedinsName()+", chkTaskBchno="+this.getChkTaskBchno()+", ruleName="+this.getRuleName()+", ruleNatu="+this.getRuleNatu()+", createTimeStart="+this.getCreateTimeStart()+", createTimeEnd="+this.getCreateTimeEnd()+"\)";
    }
}
