package com.taikang.fly.check.dto.menu.MenuIdDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MenuIdDto implements Serializable	// class@000190 from classes.dex
{
    private int level;
    private String menuId;
    private String parentId;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void MenuIdDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MenuIdDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MenuIdDto) {
             b = false;
          }else {
             MenuIdDto menuIdDto = o;
             if (!menuIdDto.canEqual(this)) {
                b = false;
             }else {
                String menuId = this.getMenuId();
                String menuId1 = menuIdDto.getMenuId();
                if (menuId == null) {
                   if (menuId1 != null) {
                      b = false;
                   }
                }else if(menuId.equals(menuId1)){
                }
                if (this.getLevel() != menuIdDto.getLevel()) {
                   b = false;
                }else {
                   String parentId = this.getParentId();
                   String parentId1 = menuIdDto.getParentId();
                   if (parentId == null) {
                      if (parentId1 == null) {
                      label_0004 :
                         return b;
                      }
                   }else if(!parentId.equals(parentId1)){
                   }
                   b = false;
                   goto label_0004 ;
                }
             }
          }
       }
    }
    public int getLevel(){
       return this.level;
    }
    public String getMenuId(){
       return this.menuId;
    }
    public String getParentId(){
       return this.parentId;
    }
    public int hashCode(){
       String $menuId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($menuId = this.getMenuId()) == null)? i: $menuId.hashCode();
       result = i1 + 59;
       result = (result * 59) + this.getLevel();
       String $parentId = this.getParentId();
       i1 = result * 59;
       if ($parentId != null) {
          i = $parentId.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setLevel(int level){
       this.level = level;
    }
    public void setMenuId(String menuId){
       this.menuId = menuId;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public String toString(){
       return "MenuIdDto\(menuId="+this.getMenuId()+", level="+this.getLevel()+", parentId="+this.getParentId()+"\)";
    }
}
