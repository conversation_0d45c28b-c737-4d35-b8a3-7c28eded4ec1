"""
数据库连接和会话管理
"""

from typing import AsyncGenerator
import duckdb
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase, sessionmaker

from flycheck.config import get_settings

settings = get_settings()

# 创建DuckDB引擎
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 元数据
metadata = MetaData()


class Base(DeclarativeBase):
    """数据库模型基类"""
    metadata = metadata


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """初始化数据库"""
    # 导入所有模型以确保它们被注册到Base.metadata
    from flycheck import models  # noqa

    # 创建所有表
    Base.metadata.create_all(bind=engine)

    # 创建DuckDB扩展
    with engine.connect() as conn:
        try:
            # 安装并加载必要的扩展
            conn.execute("INSTALL httpfs;")
            conn.execute("LOAD httpfs;")
            conn.execute("INSTALL json;")
            conn.execute("LOAD json;")
        except Exception as e:
            # 如果扩展已经安装，忽略错误
            print(f"扩展安装警告: {e}")


def get_duckdb_connection():
    """获取原生DuckDB连接（用于复杂查询）"""
    return duckdb.connect(settings.database_url.replace("duckdb:///", ""))


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = SessionLocal
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
    
    def drop_tables(self):
        """删除所有表"""
        Base.metadata.drop_all(bind=self.engine)
    
    def get_session(self):
        """获取数据库会话"""
        return self.session_factory()
    
    def execute_raw_sql(self, sql: str, params: dict = None):
        """执行原生SQL"""
        with self.engine.connect() as conn:
            return conn.execute(sql, params or {})
    
    def bulk_insert_from_dataframe(self, df, table_name: str):
        """从DataFrame批量插入数据"""
        df.to_sql(table_name, self.engine, if_exists='append', index=False)
    
    def export_to_csv(self, sql: str, file_path: str):
        """导出查询结果到CSV"""
        conn = get_duckdb_connection()
        conn.execute(f"COPY ({sql}) TO '{file_path}' (FORMAT CSV, HEADER)")
        conn.close()
    
    def import_from_csv(self, file_path: str, table_name: str):
        """从CSV导入数据"""
        conn = get_duckdb_connection()
        conn.execute(f"""
            CREATE TABLE IF NOT EXISTS {table_name} AS 
            SELECT * FROM read_csv_auto('{file_path}')
        """)
        conn.close()


# 全局数据库管理器实例
db_manager = DatabaseManager()
