package com.taikang.fly.check.dto.exportReport.DataSPTExportEntry;
import java.lang.Object;
import java.lang.Integer;
import java.lang.Double;
import java.lang.String;
import java.lang.StringBuilder;

public class DataSPTExportEntry	// class@0000f0 from classes.dex
{
    private Integer MDLOCOUNT;
    private Integer MDLOSICOUNT;
    private Integer MFSLDCOUNT;
    private Integer MFSLDSICOUNT;
    private double avgFeeZY;
    private Integer drgCount;
    private Integer mfsldICode;
    private Integer mfsldZCode;
    private Integer mfsloCount;
    private Integer mfsloICode;
    private Integer mfsloZCode;
    private Integer multiMdtrtIdMZ;
    private Integer multiMdtrtMId;
    private Integer mzmxCount;
    private Integer mzzdCount;
    private Integer negativeFeeZY;
    private Integer negativeMedfeeSumamtZY;
    private Integer notRelatedHC;
    private Integer notRelatedMS;
    private Integer notRelatedMZ;
    private Integer notRelatedZY;
    private Integer numOfPatientsMZ;
    private Integer numOfPatientsZY;
    private Integer total;
    private Integer zymxCount;
    private Integer zyzdCount;

    public void DataSPTExportEntry(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataSPTExportEntry;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DataSPTExportEntry){
          b = false;
       }else {
          DataSPTExportEntry uDataSPTExpo = o;
          if (!uDataSPTExpo.canEqual(this)) {
             b = false;
          }else {
             Integer zymxCount = this.getZymxCount();
             Integer zymxCount1 = uDataSPTExpo.getZymxCount();
             if (zymxCount == null) {
                if (zymxCount1 != null) {
                   b = false;
                }
             }else if(zymxCount.equals(zymxCount1)){
             }
             Integer zyzdCount = this.getZyzdCount();
             Integer zyzdCount1 = uDataSPTExpo.getZyzdCount();
             if (zyzdCount == null) {
                if (zyzdCount1 != null) {
                   b = false;
                }
             }else if(zyzdCount.equals(zyzdCount1)){
             }
             Integer mzmxCount = this.getMzmxCount();
             Integer mzmxCount1 = uDataSPTExpo.getMzmxCount();
             if (mzmxCount == null) {
                if (mzmxCount1 != null) {
                   b = false;
                }
             }else if(mzmxCount.equals(mzmxCount1)){
             }
             Integer mzzdCount = this.getMzzdCount();
             Integer mzzdCount1 = uDataSPTExpo.getMzzdCount();
             if (mzzdCount == null) {
                if (mzzdCount1 != null) {
                   b = false;
                }
             }else if(mzzdCount.equals(mzzdCount1)){
             }
             Integer mfsloCount = this.getMfsloCount();
             Integer mfsloCount1 = uDataSPTExpo.getMfsloCount();
             if (mfsloCount == null) {
                if (mfsloCount1 != null) {
                   b = false;
                }
             }else if(mfsloCount.equals(mfsloCount1)){
             }
             Integer drgCount = this.getDrgCount();
             Integer drgCount1 = uDataSPTExpo.getDrgCount();
             if (drgCount == null) {
                if (drgCount1 != null) {
                   b = false;
                }
             }else if(drgCount.equals(drgCount1)){
             }
             Integer total = this.getTotal();
             Integer total1 = uDataSPTExpo.getTotal();
             if (total == null) {
                if (total1 != null) {
                   b = false;
                }
             }else if(total.equals(total1)){
             }
             Integer numOfPatient = this.getNumOfPatientsMZ();
             Integer numOfPatient1 = uDataSPTExpo.getNumOfPatientsMZ();
             if (numOfPatient == null) {
                if (numOfPatient1 != null) {
                label_00db :
                   b = false;
                }
             }else if(numOfPatient.equals(numOfPatient1)){
             }
             Integer numOfPatient2 = this.getNumOfPatientsZY();
             Integer numOfPatient3 = uDataSPTExpo.getNumOfPatientsZY();
             if (numOfPatient2 == null) {
                if (numOfPatient3 != null) {
                   b = false;
                }
             }else if(numOfPatient2.equals(numOfPatient3)){
             }
             if (Double.compare(this.getAvgFeeZY(), uDataSPTExpo.getAvgFeeZY())) {
                b = false;
             }else {
                Integer notRelatedMS = this.getNotRelatedMS();
                Integer notRelatedMS1 = uDataSPTExpo.getNotRelatedMS();
                if (notRelatedMS == null) {
                   if (notRelatedMS1 != null) {
                      b = false;
                   }
                }else if(notRelatedMS.equals(notRelatedMS1)){
                }
                Integer mFSLDCOUNT = this.getMFSLDCOUNT();
                Integer mFSLDCOUNT1 = uDataSPTExpo.getMFSLDCOUNT();
                if (mFSLDCOUNT == null) {
                   if (mFSLDCOUNT1 != null) {
                   label_013b :
                      b = false;
                   }
                }else if(mFSLDCOUNT.equals(mFSLDCOUNT1)){
                }
                Integer mFSLDSICOUNT = this.getMFSLDSICOUNT();
                Integer mFSLDSICOUNT1 = uDataSPTExpo.getMFSLDSICOUNT();
                if (mFSLDSICOUNT == null) {
                   if (mFSLDSICOUNT1 != null) {
                      b = false;
                   }
                }else if(mFSLDSICOUNT.equals(mFSLDSICOUNT1)){
                }
                Integer mDLOCOUNT = this.getMDLOCOUNT();
                Integer mDLOCOUNT1 = uDataSPTExpo.getMDLOCOUNT();
                if (mDLOCOUNT == null) {
                   if (mDLOCOUNT1 != null) {
                   label_016b :
                      b = false;
                   }
                }else if(mDLOCOUNT.equals(mDLOCOUNT1)){
                }
                Integer mDLOSICOUNT = this.getMDLOSICOUNT();
                Integer mDLOSICOUNT1 = uDataSPTExpo.getMDLOSICOUNT();
                if (mDLOSICOUNT == null) {
                   if (mDLOSICOUNT1 != null) {
                      b = false;
                   }
                }else if(mDLOSICOUNT.equals(mDLOSICOUNT1)){
                }
                Integer notRelatedZY = this.getNotRelatedZY();
                Integer notRelatedZY1 = uDataSPTExpo.getNotRelatedZY();
                if (notRelatedZY == null) {
                   if (notRelatedZY1 != null) {
                   label_019b :
                      b = false;
                   }
                }else if(notRelatedZY.equals(notRelatedZY1)){
                }
                Integer notRelatedMZ = this.getNotRelatedMZ();
                Integer notRelatedMZ1 = uDataSPTExpo.getNotRelatedMZ();
                if (notRelatedMZ == null) {
                   if (notRelatedMZ1 != null) {
                      b = false;
                   }
                }else if(notRelatedMZ.equals(notRelatedMZ1)){
                }
                Integer negativeFeeZ = this.getNegativeFeeZY();
                Integer negativeFeeZ1 = uDataSPTExpo.getNegativeFeeZY();
                if (negativeFeeZ == null) {
                   if (negativeFeeZ1 != null) {
                   label_01cf :
                      b = false;
                   }
                }else if(negativeFeeZ.equals(negativeFeeZ1)){
                }
                Integer negativeMedf = this.getNegativeMedfeeSumamtZY();
                Integer negativeMedf1 = uDataSPTExpo.getNegativeMedfeeSumamtZY();
                if (negativeMedf == null) {
                   if (negativeMedf1 != null) {
                      b = false;
                   }
                }else if(negativeMedf.equals(negativeMedf1)){
                }
                Integer multiMdtrtId = this.getMultiMdtrtIdMZ();
                Integer multiMdtrtId1 = uDataSPTExpo.getMultiMdtrtIdMZ();
                if (multiMdtrtId == null) {
                   if (multiMdtrtId1 != null) {
                      b = false;
                   }
                }else if(multiMdtrtId.equals(multiMdtrtId1)){
                }
                Integer multiMdtrtMI = this.getMultiMdtrtMId();
                Integer multiMdtrtMI1 = uDataSPTExpo.getMultiMdtrtMId();
                if (multiMdtrtMI == null) {
                   if (multiMdtrtMI1 != null) {
                   label_021b :
                      b = false;
                   }
                }else if(multiMdtrtMI.equals(multiMdtrtMI1)){
                }
                Integer mfsldZCode = this.getMfsldZCode();
                Integer mfsldZCode1 = uDataSPTExpo.getMfsldZCode();
                if (mfsldZCode == null) {
                   if (mfsldZCode1 != null) {
                      b = false;
                   }
                }else if(mfsldZCode.equals(mfsldZCode1)){
                }
                Integer mfsldICode = this.getMfsldICode();
                Integer mfsldICode1 = uDataSPTExpo.getMfsldICode();
                if (mfsldICode == null) {
                   if (mfsldICode1 != null) {
                   label_024b :
                      b = false;
                   }
                }else if(mfsldICode.equals(mfsldICode1)){
                }
                Integer mfsloZCode = this.getMfsloZCode();
                Integer mfsloZCode1 = uDataSPTExpo.getMfsloZCode();
                if (mfsloZCode == null) {
                   if (mfsloZCode1 != null) {
                      b = false;
                   }
                }else if(mfsloZCode.equals(mfsloZCode1)){
                }
                Integer mfsloICode = this.getMfsloICode();
                Integer mfsloICode1 = uDataSPTExpo.getMfsloICode();
                if (mfsloICode == null) {
                   if (mfsloICode1 != null) {
                   label_027b :
                      b = false;
                   }
                }else if(mfsloICode.equals(mfsloICode1)){
                }
                Integer notRelatedHC = this.getNotRelatedHC();
                Integer notRelatedHC1 = uDataSPTExpo.getNotRelatedHC();
                if (notRelatedHC == null) {
                   if (notRelatedHC1 != null) {
                      b = false;
                   }
                }else if(notRelatedHC.equals(notRelatedHC1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public double getAvgFeeZY(){
       return this.avgFeeZY;
    }
    public Integer getDrgCount(){
       return this.drgCount;
    }
    public Integer getMDLOCOUNT(){
       return this.MDLOCOUNT;
    }
    public Integer getMDLOSICOUNT(){
       return this.MDLOSICOUNT;
    }
    public Integer getMFSLDCOUNT(){
       return this.MFSLDCOUNT;
    }
    public Integer getMFSLDSICOUNT(){
       return this.MFSLDSICOUNT;
    }
    public Integer getMfsldICode(){
       return this.mfsldICode;
    }
    public Integer getMfsldZCode(){
       return this.mfsldZCode;
    }
    public Integer getMfsloCount(){
       return this.mfsloCount;
    }
    public Integer getMfsloICode(){
       return this.mfsloICode;
    }
    public Integer getMfsloZCode(){
       return this.mfsloZCode;
    }
    public Integer getMultiMdtrtIdMZ(){
       return this.multiMdtrtIdMZ;
    }
    public Integer getMultiMdtrtMId(){
       return this.multiMdtrtMId;
    }
    public Integer getMzmxCount(){
       return this.mzmxCount;
    }
    public Integer getMzzdCount(){
       return this.mzzdCount;
    }
    public Integer getNegativeFeeZY(){
       return this.negativeFeeZY;
    }
    public Integer getNegativeMedfeeSumamtZY(){
       return this.negativeMedfeeSumamtZY;
    }
    public Integer getNotRelatedHC(){
       return this.notRelatedHC;
    }
    public Integer getNotRelatedMS(){
       return this.notRelatedMS;
    }
    public Integer getNotRelatedMZ(){
       return this.notRelatedMZ;
    }
    public Integer getNotRelatedZY(){
       return this.notRelatedZY;
    }
    public Integer getNumOfPatientsMZ(){
       return this.numOfPatientsMZ;
    }
    public Integer getNumOfPatientsZY(){
       return this.numOfPatientsZY;
    }
    public Integer getTotal(){
       return this.total;
    }
    public Integer getZymxCount(){
       return this.zymxCount;
    }
    public Integer getZyzdCount(){
       return this.zyzdCount;
    }
    public int hashCode(){
       Integer $zymxCount;
       int PRIME = 59;
       int result = 1;
       int i = (($zymxCount = this.getZymxCount()) == null)? 43: $zymxCount.hashCode();
       result = i + 59;
       Integer $zyzdCount = this.getZyzdCount();
       int i1 = result * 59;
       i = ($zyzdCount == null)? 43: $zyzdCount.hashCode();
       result = i1 + i;
       Integer $mzmxCount = this.getMzmxCount();
       i1 = result * 59;
       i = ($mzmxCount == null)? 43: $mzmxCount.hashCode();
       result = i1 + i;
       Integer $mzzdCount = this.getMzzdCount();
       i1 = result * 59;
       i = ($mzzdCount == null)? 43: $mzzdCount.hashCode();
       result = i1 + i;
       Integer $mfsloCount = this.getMfsloCount();
       i1 = result * 59;
       i = ($mfsloCount == null)? 43: $mfsloCount.hashCode();
       result = i1 + i;
       Integer drgCount = this.getDrgCount();
       i1 = result * 59;
       i = (drgCount == null)? 43: drgCount.hashCode();
       Integer total = this.getTotal();
       i1 = (i1 + i) * 59;
       i = (total == null)? 43: total.hashCode();
       Integer numOfPatient = this.getNumOfPatientsMZ();
       i1 = (i1 + i) * 59;
       i = (numOfPatient == null)? 43: numOfPatient.hashCode();
       Integer numOfPatient1 = this.getNumOfPatientsZY();
       i1 = (i1 + i) * 59;
       i = (numOfPatient1 == null)? 43: numOfPatient1.hashCode();
       long l = Double.doubleToLongBits(this.getAvgFeeZY());
       Integer notRelatedMS = this.getNotRelatedMS();
       i1 = (((i1 + i) * 59) + (int)((l >> 32) ^ l)) * 59;
       i = (notRelatedMS == null)? 43: notRelatedMS.hashCode();
       Integer mFSLDCOUNT = this.getMFSLDCOUNT();
       i1 = (i1 + i) * 59;
       i = (mFSLDCOUNT == null)? 43: mFSLDCOUNT.hashCode();
       Integer mFSLDSICOUNT = this.getMFSLDSICOUNT();
       i1 = (i1 + i) * 59;
       i = (mFSLDSICOUNT == null)? 43: mFSLDSICOUNT.hashCode();
       Integer mDLOCOUNT = this.getMDLOCOUNT();
       i1 = (i1 + i) * 59;
       i = (mDLOCOUNT == null)? 43: mDLOCOUNT.hashCode();
       Integer mDLOSICOUNT = this.getMDLOSICOUNT();
       i1 = (i1 + i) * 59;
       i = (mDLOSICOUNT == null)? 43: mDLOSICOUNT.hashCode();
       Integer notRelatedZY = this.getNotRelatedZY();
       i1 = (i1 + i) * 59;
       i = (notRelatedZY == null)? 43: notRelatedZY.hashCode();
       Integer notRelatedMZ = this.getNotRelatedMZ();
       i1 = (i1 + i) * 59;
       i = (notRelatedMZ == null)? 43: notRelatedMZ.hashCode();
       Integer negativeFeeZ = this.getNegativeFeeZY();
       i1 = (i1 + i) * 59;
       i = (negativeFeeZ == null)? 43: negativeFeeZ.hashCode();
       Integer negativeMedf = this.getNegativeMedfeeSumamtZY();
       i1 = (i1 + i) * 59;
       i = (negativeMedf == null)? 43: negativeMedf.hashCode();
       Integer multiMdtrtId = this.getMultiMdtrtIdMZ();
       i1 = (i1 + i) * 59;
       i = (multiMdtrtId == null)? 43: multiMdtrtId.hashCode();
       Integer multiMdtrtMI = this.getMultiMdtrtMId();
       i1 = (i1 + i) * 59;
       i = (multiMdtrtMI == null)? 43: multiMdtrtMI.hashCode();
       Integer mfsldZCode = this.getMfsldZCode();
       i1 = (i1 + i) * 59;
       i = (mfsldZCode == null)? 43: mfsldZCode.hashCode();
       Integer mfsldICode = this.getMfsldICode();
       i1 = (i1 + i) * 59;
       i = (mfsldICode == null)? 43: mfsldICode.hashCode();
       Integer mfsloZCode = this.getMfsloZCode();
       i1 = (i1 + i) * 59;
       i = (mfsloZCode == null)? 43: mfsloZCode.hashCode();
       Integer mfsloICode = this.getMfsloICode();
       i1 = (i1 + i) * 59;
       i = (mfsloICode == null)? 43: mfsloICode.hashCode();
       Integer notRelatedHC = this.getNotRelatedHC();
       i1 = (i1 + i) * 59;
       i = (notRelatedHC == null)? 43: notRelatedHC.hashCode();
       return (i1 + i);
    }
    public void setAvgFeeZY(double avgFeeZY){
       this.avgFeeZY = avgFeeZY;
    }
    public void setDrgCount(Integer drgCount){
       this.drgCount = drgCount;
    }
    public void setMDLOCOUNT(Integer MDLOCOUNT){
       this.MDLOCOUNT = MDLOCOUNT;
    }
    public void setMDLOSICOUNT(Integer MDLOSICOUNT){
       this.MDLOSICOUNT = MDLOSICOUNT;
    }
    public void setMFSLDCOUNT(Integer MFSLDCOUNT){
       this.MFSLDCOUNT = MFSLDCOUNT;
    }
    public void setMFSLDSICOUNT(Integer MFSLDSICOUNT){
       this.MFSLDSICOUNT = MFSLDSICOUNT;
    }
    public void setMfsldICode(Integer mfsldICode){
       this.mfsldICode = mfsldICode;
    }
    public void setMfsldZCode(Integer mfsldZCode){
       this.mfsldZCode = mfsldZCode;
    }
    public void setMfsloCount(Integer mfsloCount){
       this.mfsloCount = mfsloCount;
    }
    public void setMfsloICode(Integer mfsloICode){
       this.mfsloICode = mfsloICode;
    }
    public void setMfsloZCode(Integer mfsloZCode){
       this.mfsloZCode = mfsloZCode;
    }
    public void setMultiMdtrtIdMZ(Integer multiMdtrtIdMZ){
       this.multiMdtrtIdMZ = multiMdtrtIdMZ;
    }
    public void setMultiMdtrtMId(Integer multiMdtrtMId){
       this.multiMdtrtMId = multiMdtrtMId;
    }
    public void setMzmxCount(Integer mzmxCount){
       this.mzmxCount = mzmxCount;
    }
    public void setMzzdCount(Integer mzzdCount){
       this.mzzdCount = mzzdCount;
    }
    public void setNegativeFeeZY(Integer negativeFeeZY){
       this.negativeFeeZY = negativeFeeZY;
    }
    public void setNegativeMedfeeSumamtZY(Integer negativeMedfeeSumamtZY){
       this.negativeMedfeeSumamtZY = negativeMedfeeSumamtZY;
    }
    public void setNotRelatedHC(Integer notRelatedHC){
       this.notRelatedHC = notRelatedHC;
    }
    public void setNotRelatedMS(Integer notRelatedMS){
       this.notRelatedMS = notRelatedMS;
    }
    public void setNotRelatedMZ(Integer notRelatedMZ){
       this.notRelatedMZ = notRelatedMZ;
    }
    public void setNotRelatedZY(Integer notRelatedZY){
       this.notRelatedZY = notRelatedZY;
    }
    public void setNumOfPatientsMZ(Integer numOfPatientsMZ){
       this.numOfPatientsMZ = numOfPatientsMZ;
    }
    public void setNumOfPatientsZY(Integer numOfPatientsZY){
       this.numOfPatientsZY = numOfPatientsZY;
    }
    public void setTotal(Integer total){
       this.total = total;
    }
    public void setZymxCount(Integer zymxCount){
       this.zymxCount = zymxCount;
    }
    public void setZyzdCount(Integer zyzdCount){
       this.zyzdCount = zyzdCount;
    }
    public String toString(){
       return "DataSPTExportEntry\(zymxCount="+this.getZymxCount()+", zyzdCount="+this.getZyzdCount()+", mzmxCount="+this.getMzmxCount()+", mzzdCount="+this.getMzzdCount()+", mfsloCount="+this.getMfsloCount()+", drgCount="+this.getDrgCount()+", total="+this.getTotal()+", numOfPatientsMZ="+this.getNumOfPatientsMZ()+", numOfPatientsZY="+this.getNumOfPatientsZY()+", avgFeeZY="+this.getAvgFeeZY()+", notRelatedMS="+this.getNotRelatedMS()+", MFSLDCOUNT="+this.getMFSLDCOUNT()+", MFSLDSICOUNT="+this.getMFSLDSICOUNT()+", MDLOCOUNT="+this.getMDLOCOUNT()+", MDLOSICOUNT="+this.getMDLOSICOUNT()+", notRelatedZY="+this.getNotRelatedZY()+", notRelatedMZ="+this.getNotRelatedMZ()+", negativeFeeZY="+this.getNegativeFeeZY()+", negativeMedfeeSumamtZY="+this.getNegativeMedfeeSumamtZY()+", multiMdtrtIdMZ="+this.getMultiMdtrtIdMZ()+", multiMdtrtMId="+this.getMultiMdtrtMId()+", mfsldZCode="+this.getMfsldZCode()+", mfsldICode="+this.getMfsldICode()+", mfsloZCode="+this.getMfsloZCode()+", mfsloICode="+this.getMfsloICode()+", notRelatedHC="+this.getNotRelatedHC()+"\)";
    }
}
