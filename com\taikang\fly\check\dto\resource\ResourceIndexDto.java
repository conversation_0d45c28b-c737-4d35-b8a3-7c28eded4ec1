package com.taikang.fly.check.dto.resource.ResourceIndexDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class ResourceIndexDto implements Serializable	// class@0001ae from classes.dex
{
    private String aclass;
    private String description;
    private String expanded;
    private String iclass;
    private String icon;
    private String isLeaf;
    private int level;
    private String parentId;
    private String permission;
    private String resourceId;
    private String resourceName;
    private Integer resourceOrder;
    private String resourceType;
    private String signature;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void ResourceIndexDto(){
       super();
       this.expanded = "false";
       this.isLeaf = "false";
    }
    protected boolean canEqual(Object other){
       return other instanceof ResourceIndexDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ResourceIndexDto){
          b = false;
       }else {
          ResourceIndexDto resourceInde = o;
          if (!resourceInde.canEqual(this)) {
             b = false;
          }else {
             String resourceId = this.getResourceId();
             String resourceId1 = resourceInde.getResourceId();
             if (resourceId == null) {
                if (resourceId1 != null) {
                   b = false;
                }
             }else if(resourceId.equals(resourceId1)){
             }
             String resourceName = this.getResourceName();
             String resourceName1 = resourceInde.getResourceName();
             if (resourceName == null) {
                if (resourceName1 != null) {
                   b = false;
                }
             }else if(resourceName.equals(resourceName1)){
             }
             String parentId = this.getParentId();
             String parentId1 = resourceInde.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             String url = this.getUrl();
             String url1 = resourceInde.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             Integer resourceOrde = this.getResourceOrder();
             Integer resourceOrde1 = resourceInde.getResourceOrder();
             if (resourceOrde == null) {
                if (resourceOrde1 != null) {
                   b = false;
                }
             }else if(resourceOrde.equals(resourceOrde1)){
             }
             String icon = this.getIcon();
             String icon1 = resourceInde.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String signature = this.getSignature();
             String signature1 = resourceInde.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00bd :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String description = this.getDescription();
             String description1 = resourceInde.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String iclass = this.getIclass();
             String iclass1 = resourceInde.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                label_00ed :
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String resourceType = this.getResourceType();
             String resourceType1 = resourceInde.getResourceType();
             if (resourceType == null) {
                if (resourceType1 != null) {
                   b = false;
                }
             }else if(resourceType.equals(resourceType1)){
             }
             String aclass = this.getAclass();
             String aclass1 = resourceInde.getAclass();
             if (aclass == null) {
                if (aclass1 != null) {
                label_011d :
                   b = false;
                }
             }else if(aclass.equals(aclass1)){
             }
             String expanded = this.getExpanded();
             String expanded1 = resourceInde.getExpanded();
             if (expanded == null) {
                if (expanded1 != null) {
                   b = false;
                }
             }else if(expanded.equals(expanded1)){
             }
             String isLeaf = this.getIsLeaf();
             String isLeaf1 = resourceInde.getIsLeaf();
             if (isLeaf == null) {
                if (isLeaf1 != null) {
                label_014d :
                   b = false;
                }
             }else if(isLeaf.equals(isLeaf1)){
             }
             if (this.getLevel() != resourceInde.getLevel()) {
                b = false;
             }else {
                String permission = this.getPermission();
                String permission1 = resourceInde.getPermission();
                if (permission == null) {
                   if (permission1 != null) {
                      b = false;
                   }
                }else if(permission.equals(permission1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getAclass(){
       return this.aclass;
    }
    public String getDescription(){
       return this.description;
    }
    public String getExpanded(){
       return this.expanded;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getIsLeaf(){
       return this.isLeaf;
    }
    public int getLevel(){
       return this.level;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getPermission(){
       return this.permission;
    }
    public String getResourceId(){
       return this.resourceId;
    }
    public String getResourceName(){
       return this.resourceName;
    }
    public Integer getResourceOrder(){
       return this.resourceOrder;
    }
    public String getResourceType(){
       return this.resourceType;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $resourceId;
       int PRIME = 59;
       int result = 1;
       int i = (($resourceId = this.getResourceId()) == null)? 43: $resourceId.hashCode();
       result = i + 59;
       String $resourceName = this.getResourceName();
       int i1 = result * 59;
       i = ($resourceName == null)? 43: $resourceName.hashCode();
       result = i1 + i;
       String $parentId = this.getParentId();
       i1 = result * 59;
       i = ($parentId == null)? 43: $parentId.hashCode();
       result = i1 + i;
       String $url = this.getUrl();
       i1 = result * 59;
       i = ($url == null)? 43: $url.hashCode();
       result = i1 + i;
       Integer $resourceOrder = this.getResourceOrder();
       i1 = result * 59;
       i = ($resourceOrder == null)? 43: $resourceOrder.hashCode();
       result = i1 + i;
       String icon = this.getIcon();
       i1 = result * 59;
       i = (icon == null)? 43: icon.hashCode();
       String signature = this.getSignature();
       i1 = (i1 + i) * 59;
       i = (signature == null)? 43: signature.hashCode();
       String description = this.getDescription();
       i1 = (i1 + i) * 59;
       i = (description == null)? 43: description.hashCode();
       String iclass = this.getIclass();
       i1 = (i1 + i) * 59;
       i = (iclass == null)? 43: iclass.hashCode();
       String resourceType = this.getResourceType();
       i1 = (i1 + i) * 59;
       i = (resourceType == null)? 43: resourceType.hashCode();
       String aclass = this.getAclass();
       i1 = (i1 + i) * 59;
       i = (aclass == null)? 43: aclass.hashCode();
       String expanded = this.getExpanded();
       i1 = (i1 + i) * 59;
       i = (expanded == null)? 43: expanded.hashCode();
       String isLeaf = this.getIsLeaf();
       i1 = (i1 + i) * 59;
       i = (isLeaf == null)? 43: isLeaf.hashCode();
       String permission = this.getPermission();
       i1 = (((i1 + i) * 59) + this.getLevel()) * 59;
       i = (permission == null)? 43: permission.hashCode();
       return (i1 + i);
    }
    public void setAclass(String aclass){
       this.aclass = aclass;
    }
    public void setDescription(String description){
       this.description = description;
    }
    public void setExpanded(String expanded){
       this.expanded = expanded;
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setIcon(String icon){
       this.icon = icon;
    }
    public void setIsLeaf(String isLeaf){
       this.isLeaf = isLeaf;
    }
    public void setLevel(int level){
       this.level = level;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setPermission(String permission){
       this.permission = permission;
    }
    public void setResourceId(String resourceId){
       this.resourceId = resourceId;
    }
    public void setResourceName(String resourceName){
       this.resourceName = resourceName;
    }
    public void setResourceOrder(Integer resourceOrder){
       this.resourceOrder = resourceOrder;
    }
    public void setResourceType(String resourceType){
       this.resourceType = resourceType;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ResourceIndexDto\(resourceId="+this.getResourceId()+", resourceName="+this.getResourceName()+", parentId="+this.getParentId()+", url="+this.getUrl()+", resourceOrder="+this.getResourceOrder()+", icon="+this.getIcon()+", signature="+this.getSignature()+", description="+this.getDescription()+", iclass="+this.getIclass()+", resourceType="+this.getResourceType()+", aclass="+this.getAclass()+", expanded="+this.getExpanded()+", isLeaf="+this.getIsLeaf()+", level="+this.getLevel()+", permission="+this.getPermission()+"\)";
    }
}
