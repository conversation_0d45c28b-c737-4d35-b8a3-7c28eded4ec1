package com.taikang.fly.check.rest.KettleJobController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.comm.CommResponse;
import java.lang.String;
import com.taikang.fly.check.service.CsvOutput;

public class KettleJobController	// class@00029a from classes.dex
{
    CsvOutput csvOutput;
    private static final Logger log;

    static {
       KettleJobController.log = LoggerFactory.getLogger(KettleJobController.class);
    }
    public void KettleJobController(){
       super();
    }
    public CommResponse csvOut(HttpServletResponse response){
       KettleJobController.log.info("JobController.extractData start");
       this.csvOutput.csvOutput(response);
       KettleJobController.log.info("JobController.extractData end");
       return CommResponse.success();
    }
}
