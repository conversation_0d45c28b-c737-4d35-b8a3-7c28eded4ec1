package com.taikang.fly.check.dto.FlyTabInfoDTO;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class FlyTabInfoDTO implements Serializable	// class@0000a3 from classes.dex
{
    private List flyTabInfoList;
    private String tabCode;
    private String tabName;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void FlyTabInfoDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyTabInfoDTO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyTabInfoDTO) {
             b = false;
          }else {
             FlyTabInfoDTO uFlyTabInfoD = o;
             if (!uFlyTabInfoD.canEqual(this)) {
                b = false;
             }else {
                String tabCode = this.getTabCode();
                String tabCode1 = uFlyTabInfoD.getTabCode();
                if (tabCode == null) {
                   if (tabCode1 != null) {
                      b = false;
                   }
                }else if(tabCode.equals(tabCode1)){
                }
                String tabName = this.getTabName();
                String tabName1 = uFlyTabInfoD.getTabName();
                if (tabName == null) {
                   if (tabName1 != null) {
                      b = false;
                   }
                }else if(tabName.equals(tabName1)){
                }
                List flyTabInfoLi = this.getFlyTabInfoList();
                List flyTabInfoLi1 = uFlyTabInfoD.getFlyTabInfoList();
                if (flyTabInfoLi == null) {
                   if (flyTabInfoLi1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!flyTabInfoLi.equals(flyTabInfoLi1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getFlyTabInfoList(){
       return this.flyTabInfoList;
    }
    public String getTabCode(){
       return this.tabCode;
    }
    public String getTabName(){
       return this.tabName;
    }
    public int hashCode(){
       String $tabCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tabCode = this.getTabCode()) == null)? i: $tabCode.hashCode();
       result = i1 + 59;
       String $tabName = this.getTabName();
       int i2 = result * 59;
       i1 = ($tabName == null)? i: $tabName.hashCode();
       result = i2 + i1;
       List $flyTabInfoList = this.getFlyTabInfoList();
       i1 = result * 59;
       if ($flyTabInfoList != null) {
          i = $flyTabInfoList.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFlyTabInfoList(List flyTabInfoList){
       this.flyTabInfoList = flyTabInfoList;
    }
    public void setTabCode(String tabCode){
       this.tabCode = tabCode;
    }
    public void setTabName(String tabName){
       this.tabName = tabName;
    }
    public String toString(){
       return "FlyTabInfoDTO\(tabCode="+this.getTabCode()+", tabName="+this.getTabName()+", flyTabInfoList="+this.getFlyTabInfoList()+"\)";
    }
}
