package com.taikang.fly.check.rest.RoleMenuController;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.dto.RmpResponse;
import java.util.List;
import com.taikang.fly.check.service.RoleMenuService;
import com.taikang.fly.check.dto.system.role.RoleMenuManageDto;

public class RoleMenuController	// class@0002ac from classes.dex
{
    private RoleMenuService roleMenuService;

    public void RoleMenuController(){
       super();
    }
    public RmpResponse getRoleMenuRelactionByUserCode(String roleCode,String moduleCode){
       List menuCodeList = this.roleMenuService.menuIdListByRoleCode(roleCode, moduleCode);
       return RmpResponse.success(menuCodeList);
    }
    public RmpResponse roleMenuManage(RoleMenuManageDto roleMenuMangageDto){
       this.roleMenuService.rolemMenuManage(roleMenuMangageDto);
       return RmpResponse.success();
    }
}
