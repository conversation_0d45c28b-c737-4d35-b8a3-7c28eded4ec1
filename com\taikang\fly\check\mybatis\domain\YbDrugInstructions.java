package com.taikang.fly.check.mybatis.domain.YbDrugInstructions;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;
import java.lang.Class;

public class YbDrugInstructions implements Serializable	// class@000279 from classes.dex
{
    private String addre;
    private String applicable;
    private Date approvalData;
    private String approvalNumber;
    private String character;
    private String classification;
    private String component;
    private String drugAction;
    private String drugInteractions;
    private String englishName;
    private String genericName;
    private String genericName2;
    private String id;
    private String manufacturingEnterprise;
    private String mattersNeeding;
    private String sideEffects;
    private String specifications;
    private String store;
    private String taboo;
    private String termOfValidity;
    private String tradeName;
    private String usageDosage;
    private static final long serialVersionUID = 0x1;

    public void YbDrugInstructions(){
       super();
    }
    public String getAddre(){
       return this.addre;
    }
    public String getApplicable(){
       return this.applicable;
    }
    public Date getApprovalData(){
       return this.approvalData;
    }
    public String getApprovalNumber(){
       return this.approvalNumber;
    }
    public String getCharacter(){
       return this.character;
    }
    public String getClassification(){
       return this.classification;
    }
    public String getComponent(){
       return this.component;
    }
    public String getDrugAction(){
       return this.drugAction;
    }
    public String getDrugInteractions(){
       return this.drugInteractions;
    }
    public String getEnglishName(){
       return this.englishName;
    }
    public String getGenericName(){
       return this.genericName;
    }
    public String getGenericName2(){
       return this.genericName2;
    }
    public String getId(){
       return this.id;
    }
    public String getManufacturingEnterprise(){
       return this.manufacturingEnterprise;
    }
    public String getMattersNeeding(){
       return this.mattersNeeding;
    }
    public String getSideEffects(){
       return this.sideEffects;
    }
    public String getSpecifications(){
       return this.specifications;
    }
    public String getStore(){
       return this.store;
    }
    public String getTaboo(){
       return this.taboo;
    }
    public String getTermOfValidity(){
       return this.termOfValidity;
    }
    public String getTradeName(){
       return this.tradeName;
    }
    public String getUsageDosage(){
       return this.usageDosage;
    }
    public void setAddre(String addre){
       String str = (addre == null)? null: addre.trim();
       this.addre = str;
       return;
    }
    public void setApplicable(String applicable){
       String str = (applicable == null)? null: applicable.trim();
       this.applicable = str;
       return;
    }
    public void setApprovalData(Date approvalData){
       this.approvalData = approvalData;
    }
    public void setApprovalNumber(String approvalNumber){
       String str = (approvalNumber == null)? null: approvalNumber.trim();
       this.approvalNumber = str;
       return;
    }
    public void setCharacter(String character){
       String str = (character == null)? null: character.trim();
       this.character = str;
       return;
    }
    public void setClassification(String classification){
       String str = (classification == null)? null: classification.trim();
       this.classification = str;
       return;
    }
    public void setComponent(String component){
       String str = (component == null)? null: component.trim();
       this.component = str;
       return;
    }
    public void setDrugAction(String drugAction){
       String str = (drugAction == null)? null: drugAction.trim();
       this.drugAction = str;
       return;
    }
    public void setDrugInteractions(String drugInteractions){
       String str = (drugInteractions == null)? null: drugInteractions.trim();
       this.drugInteractions = str;
       return;
    }
    public void setEnglishName(String englishName){
       String str = (englishName == null)? null: englishName.trim();
       this.englishName = str;
       return;
    }
    public void setGenericName(String genericName){
       String str = (genericName == null)? null: genericName.trim();
       this.genericName = str;
       return;
    }
    public void setGenericName2(String genericName2){
       String str = (genericName2 == null)? null: genericName2.trim();
       this.genericName2 = str;
       return;
    }
    public void setId(String id){
       String str = (id == null)? null: id.trim();
       this.id = str;
       return;
    }
    public void setManufacturingEnterprise(String manufacturingEnterprise){
       String str = (manufacturingEnterprise == null)? null: manufacturingEnterprise.trim();
       this.manufacturingEnterprise = str;
       return;
    }
    public void setMattersNeeding(String mattersNeeding){
       String str = (mattersNeeding == null)? null: mattersNeeding.trim();
       this.mattersNeeding = str;
       return;
    }
    public void setSideEffects(String sideEffects){
       String str = (sideEffects == null)? null: sideEffects.trim();
       this.sideEffects = str;
       return;
    }
    public void setSpecifications(String specifications){
       String str = (specifications == null)? null: specifications.trim();
       this.specifications = str;
       return;
    }
    public void setStore(String store){
       String str = (store == null)? null: store.trim();
       this.store = str;
       return;
    }
    public void setTaboo(String taboo){
       String str = (taboo == null)? null: taboo.trim();
       this.taboo = str;
       return;
    }
    public void setTermOfValidity(String termOfValidity){
       String str = (termOfValidity == null)? null: termOfValidity.trim();
       this.termOfValidity = str;
       return;
    }
    public void setTradeName(String tradeName){
       String str = (tradeName == null)? null: tradeName.trim();
       this.tradeName = str;
       return;
    }
    public void setUsageDosage(String usageDosage){
       String str = (usageDosage == null)? null: usageDosage.trim();
       this.usageDosage = str;
       return;
    }
    public String toString(){
       StringBuilder sb = "";
       sb = sb+this.getClass().getSimpleName();
       sb = sb+" [";
       StringBuilder sb1 = sb+"Hash = ";
       sb1 = sb1+this.hashCode();
       sb1 = sb+", id=";
       sb1 = sb1+this.id;
       sb1 = sb+", genericName=";
       sb1 = sb1+this.genericName;
       sb1 = sb+", classification=";
       sb1 = sb1+this.classification;
       sb1 = sb+", tradeName=";
       sb1 = sb1+this.tradeName;
       sb1 = sb+", genericName2=";
       sb1 = sb1+this.genericName2;
       sb1 = sb+", englishName=";
       sb1 = sb1+this.englishName;
       sb1 = sb+", component=";
       sb1 = sb1+this.component;
       sb1 = sb+", character=";
       sb1 = sb1+this.character;
       sb1 = sb+", applicable=";
       sb1 = sb1+this.applicable;
       sb1 = sb+", specifications=";
       sb1 = sb1+this.specifications;
       sb1 = sb+", usageDosage=";
       sb1 = sb1+this.usageDosage;
       sb1 = sb+", sideEffects=";
       sb1 = sb1+this.sideEffects;
       sb1 = sb+", taboo=";
       sb1 = sb1+this.taboo;
       sb1 = sb+", mattersNeeding=";
       sb1 = sb1+this.mattersNeeding;
       sb1 = sb+", drugInteractions=";
       sb1 = sb1+this.drugInteractions;
       sb1 = sb+", drugAction=";
       sb1 = sb1+this.drugAction;
       sb1 = sb+", store=";
       sb1 = sb1+this.store;
       sb1 = sb+", termOfValidity=";
       sb1 = sb1+this.termOfValidity;
       sb1 = sb+", approvalNumber=";
       sb1 = sb1+this.approvalNumber;
       sb1 = sb+", approvalData=";
       sb1 = sb1+this.approvalData;
       sb1 = sb+", manufacturingEnterprise=";
       sb1 = sb1+this.manufacturingEnterprise;
       sb1 = sb+", addre=";
       sb1 = sb1+this.addre;
       sb1 = sb+", serialVersionUID=";
       sb1 = sb1+1;
       sb = sb+"]";
       return sb;
    }
}
