package com.taikang.fly.check.utils.CSVUtils$1;
import java.util.Comparator;
import java.lang.Object;
import com.taikang.fly.check.utils.excel.annotation.ExcelField;
import java.lang.Integer;

final class CSVUtils$1 implements Comparator	// class@00032c from classes.dex
{

    void CSVUtils$1(){
       super();
    }
    public int compare(Object p0,Object p1){
       return this.compare(p0, p1);
    }
    public int compare(Object[] o1,Object[] o2){
       return Integer.valueOf(o1[0].sort()).compareTo(Integer.valueOf(o2[0].sort()));
    }
}
