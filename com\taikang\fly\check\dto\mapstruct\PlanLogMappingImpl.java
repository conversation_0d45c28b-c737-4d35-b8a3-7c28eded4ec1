package com.taikang.fly.check.dto.mapstruct.PlanLogMappingImpl;
import com.taikang.fly.check.dto.mapstruct.PlanLogMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.planLog.AddPlanLogDto;
import com.taikang.fly.check.mybatis.domain.PlanLog;
import java.lang.String;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.util.Date;
import com.taikang.fly.check.utils.SequenceGenerator;
import com.taikang.fly.check.dto.plan.PlanLogRespDto;
import com.taikang.fly.check.dto.plan.PlanLogRespsDto;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class PlanLogMappingImpl implements PlanLogMapping	// class@000172 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void PlanLogMappingImpl(){
       super();
    }
    public PlanLog AddPlanLogTOPlanLog(AddPlanLogDto addPlanLogDto){
       PlanLog planLog;
       if (addPlanLogDto == null) {
          planLog = null;
       }else {
          planLog = new PlanLog();
          planLog.setMissionName(addPlanLogDto.getPlanName());
          planLog.setCreator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          planLog.setModifyTime(new Date());
          planLog.setCreateTime(new Date());
          planLog.setMissionStatus("1");
          planLog.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          planLog.setId(SequenceGenerator.getId());
          planLog.setDownloadStatus("0");
          planLog.setStatus("1");
       }
       return planLog;
    }
    public PlanLogRespsDto PlanLogDtoToDto(PlanLogRespDto planLogRespVo){
       PlanLogRespsDto planLogResps;
       if (planLogRespVo == null) {
          planLogResps = null;
       }else {
          planLogResps = new PlanLogRespsDto();
          planLogResps.setId(planLogRespVo.getId());
          planLogResps.setMissionName(planLogRespVo.getMissionName());
          planLogResps.setCreator(planLogRespVo.getCreator());
          planLogResps.setCreateTime(this.typeConversionMapper.DateTime2String(planLogRespVo.getCreateTime()));
          planLogResps.setModby(planLogRespVo.getModby());
          planLogResps.setModifyTime(this.typeConversionMapper.DateTime2String(planLogRespVo.getModifyTime()));
          planLogResps.setDataStart(this.typeConversionMapper.Date2String(planLogRespVo.getDataStart()));
          planLogResps.setDataEnd(this.typeConversionMapper.Date2String(planLogRespVo.getDataEnd()));
          planLogResps.setRules(planLogRespVo.getRules());
          planLogResps.setMissionStart(this.typeConversionMapper.DateTime2String(planLogRespVo.getMissionStart()));
          planLogResps.setMissionStatus(planLogRespVo.getMissionStatus());
          planLogResps.setMissionEnd(this.typeConversionMapper.DateTime2String(planLogRespVo.getMissionEnd()));
          planLogResps.setStatus(planLogRespVo.getStatus());
          planLogResps.setRedFiled1(planLogRespVo.getRedFiled1());
          planLogResps.setRedFiled2(planLogRespVo.getRedFiled2());
          planLogResps.setRedFiled3(planLogRespVo.getRedFiled3());
          planLogResps.setRedFiled4(planLogRespVo.getRedFiled4());
          planLogResps.setRedFiled5(planLogRespVo.getRedFiled5());
          planLogResps.setDownloadStatus(planLogRespVo.getDownloadStatus());
          planLogResps.setErrorRules(planLogRespVo.getErrorRules());
          planLogResps.setDataSources(planLogRespVo.getDataSources());
          String str = (planLogRespVo.getMissionMessage() != null && planLogRespVo.getMissionMessage().length() > 300)? planLogRespVo.getMissionMessage().substring(0, 300): planLogRespVo.getMissionMessage();
          planLogResps.setMissionMessage(str);
       }
       return planLogResps;
    }
    public List PlanLogDtoToDtos(List planLogList){
       List list;
       if (planLogList == null) {
          list = null;
       }else {
          list = new ArrayList(planLogList.size());
          Iterator iterator = planLogList.iterator();
          while (iterator.hasNext()) {
             list.add(this.PlanLogDtoToDto(iterator.next()));
          }
       }
       return list;
    }
}
