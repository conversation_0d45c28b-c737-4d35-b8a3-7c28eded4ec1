package com.taikang.fly.check.service.MergeService;
import com.baomidou.mybatisplus.extension.service.IService;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.dto.merge.MergeDownloadDTO;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.dto.merge.MergeDTO;
import com.taikang.fly.check.vo.merge.MergeVO;
import java.lang.Integer;
import java.util.List;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.vo.merge.BillDateVO;
import org.springframework.web.multipart.MultipartFile;

public interface abstract MergeService implements IService	// class@0002ee from classes.dex
{

    void delMergeById(String p0);
    CommResponse delTemplate();
    void downloadMerge(MergeDownloadDTO p0,HttpServletResponse p1);
    void downloadTemplate(HttpServletResponse p0);
    MergeVO getMergeTotalPrice(MergeDTO p0);
    NativePage queryMergeByRuleID(Integer p0,Integer p1,String p2,List p3);
    BillDateVO queryMinAndMaxBillDate();
    NativePage queryPage(Integer p0,Integer p1,MergeDTO p2);
    void uploadTemplate(MultipartFile p0);
}
