package com.taikang.fly.check.vo.drg.DrgRuleInfoVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgRuleInfoVo	// class@000376 from classes.dex
{
    private String ruleCode;
    private String ruleName;

    public void DrgRuleInfoVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgRuleInfoVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgRuleInfoVo) {
             b = false;
          }else {
             DrgRuleInfoVo uDrgRuleInfo = o;
             if (!uDrgRuleInfo.canEqual(this)) {
                b = false;
             }else {
                String ruleCode = this.getRuleCode();
                String ruleCode1 = uDrgRuleInfo.getRuleCode();
                if (ruleCode == null) {
                   if (ruleCode1 != null) {
                      b = false;
                   }
                }else if(ruleCode.equals(ruleCode1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = uDrgRuleInfo.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!ruleName.equals(ruleName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getRuleCode(){
       return this.ruleCode;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public int hashCode(){
       String $ruleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleCode = this.getRuleCode()) == null)? i: $ruleCode.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       i1 = result * 59;
       if ($ruleName != null) {
          i = $ruleName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setRuleCode(String ruleCode){
       this.ruleCode = ruleCode;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public String toString(){
       return "DrgRuleInfoVo\(ruleCode="+this.getRuleCode()+", ruleName="+this.getRuleName()+"\)";
    }
}
