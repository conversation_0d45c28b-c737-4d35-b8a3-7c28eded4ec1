package com.taikang.fly.check.dto.TransferTableDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TransferTableDto	// class@0000b5 from classes.dex
{
    private String hospitalName;
    private String tableName;

    public void TransferTableDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TransferTableDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof TransferTableDto) {
             b = false;
          }else {
             TransferTableDto transferTabl = o;
             if (!transferTabl.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = transferTabl.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String hospitalName = this.getHospitalName();
                String hospitalName1 = transferTabl.getHospitalName();
                if (hospitalName == null) {
                   if (hospitalName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!hospitalName.equals(hospitalName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getHospitalName(){
       return this.hospitalName;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       String $hospitalName = this.getHospitalName();
       i1 = result * 59;
       if ($hospitalName != null) {
          i = $hospitalName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setHospitalName(String hospitalName){
       this.hospitalName = hospitalName;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "TransferTableDto\(tableName="+this.getTableName()+", hospitalName="+this.getHospitalName()+"\)";
    }
}
