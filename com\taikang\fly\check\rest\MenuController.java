package com.taikang.fly.check.rest.MenuController;
import java.lang.Object;
import com.taikang.fly.check.dto.RmpResponse;
import java.util.List;
import com.taikang.fly.check.service.MenuService;
import java.lang.String;
import com.taikang.fly.check.dto.menu.MenuAddDto;
import javax.servlet.http.HttpServletRequest;
import com.taikang.fly.check.mybatis.domain.Menu;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import com.taikang.fly.check.dto.menu.MenuEditDto;
import java.lang.Integer;

public class MenuController	// class@00029f from classes.dex
{
    private MenuService menuService;

    public void MenuController(){
       super();
    }
    public RmpResponse getAllMenuTrees(){
       List menuTreeResDtos = this.menuService.menuTreeInitJson();
       return RmpResponse.success(menuTreeResDtos);
    }
    public RmpResponse getAllMenuTreesByModule(String moduleCode){
       List menuTreeResDtos = this.menuService.getAllMenuTreesByModule(moduleCode);
       return RmpResponse.success(menuTreeResDtos);
    }
    public RmpResponse getAllMouduleMenuTrees(){
       List menuTreeResDtos = this.menuService.getAllMouduleMenuTrees();
       return RmpResponse.success(menuTreeResDtos);
    }
    public RmpResponse getUserLeftMenuTrees(String moduleCode){
       List menuTreeResDtos = this.menuService.getUserLeftMenuTrees(moduleCode);
       return RmpResponse.success(menuTreeResDtos);
    }
    public RmpResponse menuAdd(MenuAddDto menuAddDto,HttpServletRequest request){
       this.menuService.addMenu(menuAddDto);
       return RmpResponse.success();
    }
    public RmpResponse menuDel(String menuId){
       if (StringUtils.isBlank(menuId)) {
          throw new BizException(ResponseCodeEnum.PARAMETER_ISNULL_ERROR);
       }
       this.menuService.delMenu(menuId);
       return RmpResponse.success();
    }
    public RmpResponse menuEdit(MenuEditDto menuEditDto,HttpServletRequest request){
       this.menuService.editMenu(menuEditDto);
       return RmpResponse.success();
    }
    public RmpResponse menuIndex(){
       List menuList = this.menuService.getMenuTreeList();
       return RmpResponse.success(menuList);
    }
    public RmpResponse roleMenuListByRoleId(){
       List menuTreeResDtos = this.menuService.roleMenuListByRoleId();
       return RmpResponse.success(menuTreeResDtos);
    }
}
