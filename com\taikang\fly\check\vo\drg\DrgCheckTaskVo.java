package com.taikang.fly.check.vo.drg.DrgCheckTaskVo;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class DrgCheckTaskVo	// class@000361 from classes.dex
{
    private String chkReqCode;
    private String chkSchmNo;
    private List dataImpBchnoList;
    private String fitrDataWay;
    private String fixmedinsCode;
    private String insutypeCode;
    private String mdtrtWayCode;
    private String medTypeCode;
    private String medinsLvCode;
    private String poolareaCode;
    private List ruleInfoList;
    private String runMode;
    private String setlBegndate;
    private String setlEnddate;
    private String updtBegndate;
    private String updtEnddate;

    public void DrgCheckTaskVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgCheckTaskVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgCheckTaskVo){
          b = false;
       }else {
          DrgCheckTaskVo uDrgCheckTas = o;
          if (!uDrgCheckTas.canEqual(this)) {
             b = false;
          }else {
             String chkReqCode = this.getChkReqCode();
             String chkReqCode1 = uDrgCheckTas.getChkReqCode();
             if (chkReqCode == null) {
                if (chkReqCode1 != null) {
                   b = false;
                }
             }else if(chkReqCode.equals(chkReqCode1)){
             }
             String chkSchmNo = this.getChkSchmNo();
             String chkSchmNo1 = uDrgCheckTas.getChkSchmNo();
             if (chkSchmNo == null) {
                if (chkSchmNo1 != null) {
                   b = false;
                }
             }else if(chkSchmNo.equals(chkSchmNo1)){
             }
             String poolareaCode = this.getPoolareaCode();
             String poolareaCode1 = uDrgCheckTas.getPoolareaCode();
             if (poolareaCode == null) {
                if (poolareaCode1 != null) {
                   b = false;
                }
             }else if(poolareaCode.equals(poolareaCode1)){
             }
             String insutypeCode = this.getInsutypeCode();
             String insutypeCode1 = uDrgCheckTas.getInsutypeCode();
             if (insutypeCode == null) {
                if (insutypeCode1 != null) {
                   b = false;
                }
             }else if(insutypeCode.equals(insutypeCode1)){
             }
             String mdtrtWayCode = this.getMdtrtWayCode();
             String mdtrtWayCode1 = uDrgCheckTas.getMdtrtWayCode();
             if (mdtrtWayCode == null) {
                if (mdtrtWayCode1 != null) {
                   b = false;
                }
             }else if(mdtrtWayCode.equals(mdtrtWayCode1)){
             }
             String medTypeCode = this.getMedTypeCode();
             String medTypeCode1 = uDrgCheckTas.getMedTypeCode();
             if (medTypeCode == null) {
                if (medTypeCode1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(medTypeCode.equals(medTypeCode1)){
             }
             String medinsLvCode = this.getMedinsLvCode();
             String medinsLvCode1 = uDrgCheckTas.getMedinsLvCode();
             if (medinsLvCode == null) {
                if (medinsLvCode1 != null) {
                   b = false;
                }
             }else if(medinsLvCode.equals(medinsLvCode1)){
             }
             String fixmedinsCod = this.getFixmedinsCode();
             String fixmedinsCod1 = uDrgCheckTas.getFixmedinsCode();
             if (fixmedinsCod == null) {
                if (fixmedinsCod1 != null) {
                label_00d3 :
                   b = false;
                }
             }else if(fixmedinsCod.equals(fixmedinsCod1)){
             }
             String fitrDataWay = this.getFitrDataWay();
             String fitrDataWay1 = uDrgCheckTas.getFitrDataWay();
             if (fitrDataWay == null) {
                if (fitrDataWay1 != null) {
                   b = false;
                }
             }else if(fitrDataWay.equals(fitrDataWay1)){
             }
             String setlBegndate = this.getSetlBegndate();
             String setlBegndate1 = uDrgCheckTas.getSetlBegndate();
             if (setlBegndate == null) {
                if (setlBegndate1 != null) {
                   b = false;
                }
             }else if(setlBegndate.equals(setlBegndate1)){
             }
             String setlEnddate = this.getSetlEnddate();
             String setlEnddate1 = uDrgCheckTas.getSetlEnddate();
             if (setlEnddate == null) {
                if (setlEnddate1 != null) {
                label_011b :
                   b = false;
                }
             }else if(setlEnddate.equals(setlEnddate1)){
             }
             String updtBegndate = this.getUpdtBegndate();
             String updtBegndate1 = uDrgCheckTas.getUpdtBegndate();
             if (updtBegndate == null) {
                if (updtBegndate1 != null) {
                   b = false;
                }
             }else if(updtBegndate.equals(updtBegndate1)){
             }
             String updtEnddate = this.getUpdtEnddate();
             String updtEnddate1 = uDrgCheckTas.getUpdtEnddate();
             if (updtEnddate == null) {
                if (updtEnddate1 != null) {
                label_014f :
                   b = false;
                }
             }else if(updtEnddate.equals(updtEnddate1)){
             }
             String runMode = this.getRunMode();
             String runMode1 = uDrgCheckTas.getRunMode();
             if (runMode == null) {
                if (runMode1 != null) {
                   b = false;
                }
             }else if(runMode.equals(runMode1)){
             }
             List ruleInfoList = this.getRuleInfoList();
             List ruleInfoList1 = uDrgCheckTas.getRuleInfoList();
             if (ruleInfoList == null) {
                if (ruleInfoList1 != null) {
                label_0181 :
                   b = false;
                }
             }else if(ruleInfoList.equals(ruleInfoList1)){
             }
             List dataImpBchno = this.getDataImpBchnoList();
             List dataImpBchno1 = uDrgCheckTas.getDataImpBchnoList();
             if (dataImpBchno == null) {
                if (dataImpBchno1 != null) {
                label_0199 :
                   b = false;
                }
             }else if(dataImpBchno.equals(dataImpBchno1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getChkReqCode(){
       return this.chkReqCode;
    }
    public String getChkSchmNo(){
       return this.chkSchmNo;
    }
    public List getDataImpBchnoList(){
       return this.dataImpBchnoList;
    }
    public String getFitrDataWay(){
       return this.fitrDataWay;
    }
    public String getFixmedinsCode(){
       return this.fixmedinsCode;
    }
    public String getInsutypeCode(){
       return this.insutypeCode;
    }
    public String getMdtrtWayCode(){
       return this.mdtrtWayCode;
    }
    public String getMedTypeCode(){
       return this.medTypeCode;
    }
    public String getMedinsLvCode(){
       return this.medinsLvCode;
    }
    public String getPoolareaCode(){
       return this.poolareaCode;
    }
    public List getRuleInfoList(){
       return this.ruleInfoList;
    }
    public String getRunMode(){
       return this.runMode;
    }
    public String getSetlBegndate(){
       return this.setlBegndate;
    }
    public String getSetlEnddate(){
       return this.setlEnddate;
    }
    public String getUpdtBegndate(){
       return this.updtBegndate;
    }
    public String getUpdtEnddate(){
       return this.updtEnddate;
    }
    public int hashCode(){
       String $chkReqCode;
       int PRIME = 59;
       int result = 1;
       int i = (($chkReqCode = this.getChkReqCode()) == null)? 43: $chkReqCode.hashCode();
       result = i + 59;
       String $chkSchmNo = this.getChkSchmNo();
       int i1 = result * 59;
       i = ($chkSchmNo == null)? 43: $chkSchmNo.hashCode();
       result = i1 + i;
       String $poolareaCode = this.getPoolareaCode();
       i1 = result * 59;
       i = ($poolareaCode == null)? 43: $poolareaCode.hashCode();
       result = i1 + i;
       String $insutypeCode = this.getInsutypeCode();
       i1 = result * 59;
       i = ($insutypeCode == null)? 43: $insutypeCode.hashCode();
       result = i1 + i;
       String $mdtrtWayCode = this.getMdtrtWayCode();
       i1 = result * 59;
       i = ($mdtrtWayCode == null)? 43: $mdtrtWayCode.hashCode();
       result = i1 + i;
       String medTypeCode = this.getMedTypeCode();
       i1 = result * 59;
       i = (medTypeCode == null)? 43: medTypeCode.hashCode();
       String medinsLvCode = this.getMedinsLvCode();
       i1 = (i1 + i) * 59;
       i = (medinsLvCode == null)? 43: medinsLvCode.hashCode();
       String fixmedinsCod = this.getFixmedinsCode();
       i1 = (i1 + i) * 59;
       i = (fixmedinsCod == null)? 43: fixmedinsCod.hashCode();
       String fitrDataWay = this.getFitrDataWay();
       i1 = (i1 + i) * 59;
       i = (fitrDataWay == null)? 43: fitrDataWay.hashCode();
       String setlBegndate = this.getSetlBegndate();
       i1 = (i1 + i) * 59;
       i = (setlBegndate == null)? 43: setlBegndate.hashCode();
       String setlEnddate = this.getSetlEnddate();
       i1 = (i1 + i) * 59;
       i = (setlEnddate == null)? 43: setlEnddate.hashCode();
       String updtBegndate = this.getUpdtBegndate();
       i1 = (i1 + i) * 59;
       i = (updtBegndate == null)? 43: updtBegndate.hashCode();
       String updtEnddate = this.getUpdtEnddate();
       i1 = (i1 + i) * 59;
       i = (updtEnddate == null)? 43: updtEnddate.hashCode();
       String runMode = this.getRunMode();
       i1 = (i1 + i) * 59;
       i = (runMode == null)? 43: runMode.hashCode();
       List ruleInfoList = this.getRuleInfoList();
       i1 = (i1 + i) * 59;
       i = (ruleInfoList == null)? 43: ruleInfoList.hashCode();
       List dataImpBchno = this.getDataImpBchnoList();
       i1 = (i1 + i) * 59;
       i = (dataImpBchno == null)? 43: dataImpBchno.hashCode();
       return (i1 + i);
    }
    public void setChkReqCode(String chkReqCode){
       this.chkReqCode = chkReqCode;
    }
    public void setChkSchmNo(String chkSchmNo){
       this.chkSchmNo = chkSchmNo;
    }
    public void setDataImpBchnoList(List dataImpBchnoList){
       this.dataImpBchnoList = dataImpBchnoList;
    }
    public void setFitrDataWay(String fitrDataWay){
       this.fitrDataWay = fitrDataWay;
    }
    public void setFixmedinsCode(String fixmedinsCode){
       this.fixmedinsCode = fixmedinsCode;
    }
    public void setInsutypeCode(String insutypeCode){
       this.insutypeCode = insutypeCode;
    }
    public void setMdtrtWayCode(String mdtrtWayCode){
       this.mdtrtWayCode = mdtrtWayCode;
    }
    public void setMedTypeCode(String medTypeCode){
       this.medTypeCode = medTypeCode;
    }
    public void setMedinsLvCode(String medinsLvCode){
       this.medinsLvCode = medinsLvCode;
    }
    public void setPoolareaCode(String poolareaCode){
       this.poolareaCode = poolareaCode;
    }
    public void setRuleInfoList(List ruleInfoList){
       this.ruleInfoList = ruleInfoList;
    }
    public void setRunMode(String runMode){
       this.runMode = runMode;
    }
    public void setSetlBegndate(String setlBegndate){
       this.setlBegndate = setlBegndate;
    }
    public void setSetlEnddate(String setlEnddate){
       this.setlEnddate = setlEnddate;
    }
    public void setUpdtBegndate(String updtBegndate){
       this.updtBegndate = updtBegndate;
    }
    public void setUpdtEnddate(String updtEnddate){
       this.updtEnddate = updtEnddate;
    }
    public String toString(){
       return "DrgCheckTaskVo\(chkReqCode="+this.getChkReqCode()+", chkSchmNo="+this.getChkSchmNo()+", poolareaCode="+this.getPoolareaCode()+", insutypeCode="+this.getInsutypeCode()+", mdtrtWayCode="+this.getMdtrtWayCode()+", medTypeCode="+this.getMedTypeCode()+", medinsLvCode="+this.getMedinsLvCode()+", fixmedinsCode="+this.getFixmedinsCode()+", fitrDataWay="+this.getFitrDataWay()+", setlBegndate="+this.getSetlBegndate()+", setlEnddate="+this.getSetlEnddate()+", updtBegndate="+this.getUpdtBegndate()+", updtEnddate="+this.getUpdtEnddate()+", runMode="+this.getRunMode()+", ruleInfoList="+this.getRuleInfoList()+", dataImpBchnoList="+this.getDataImpBchnoList()+"\)";
    }
}
