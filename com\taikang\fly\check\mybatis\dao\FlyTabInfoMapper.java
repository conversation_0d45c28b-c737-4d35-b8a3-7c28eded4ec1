package com.taikang.fly.check.mybatis.dao.FlyTabInfoMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.FlyTabInfo;
import java.lang.String;

public interface abstract FlyTabInfoMapper implements BaseMapper	// class@0001f8 from classes.dex
{

    List queryFlySoftInfo();
    List queryFlyTabInfo();
    FlyTabInfo queryFlyTabInfoById();
    int updateFlyTabInfoById(String p0);
}
