package com.taikang.fly.check.dto.machineLearning.TableColumnDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TableColumnDto	// class@00013e from classes.dex
{
    private String columnName;
    private String comments;
    private String dataLength;
    private String dataType;

    public void TableColumnDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TableColumnDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof TableColumnDto) {
             b = false;
          }else {
             TableColumnDto tableColumnD = o;
             if (!tableColumnD.canEqual(this)) {
                b = false;
             }else {
                String columnName = this.getColumnName();
                String columnName1 = tableColumnD.getColumnName();
                if (columnName == null) {
                   if (columnName1 != null) {
                      b = false;
                   }
                }else if(columnName.equals(columnName1)){
                }
                String dataType = this.getDataType();
                String dataType1 = tableColumnD.getDataType();
                if (dataType == null) {
                   if (dataType1 != null) {
                      b = false;
                   }
                }else if(dataType.equals(dataType1)){
                }
                String dataLength = this.getDataLength();
                String dataLength1 = tableColumnD.getDataLength();
                if (dataLength == null) {
                   if (dataLength1 != null) {
                      b = false;
                   }
                }else if(dataLength.equals(dataLength1)){
                }
                String comments = this.getComments();
                String comments1 = tableColumnD.getComments();
                if (comments == null) {
                   if (comments1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!comments.equals(comments1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getComments(){
       return this.comments;
    }
    public String getDataLength(){
       return this.dataLength;
    }
    public String getDataType(){
       return this.dataType;
    }
    public int hashCode(){
       String $columnName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($columnName = this.getColumnName()) == null)? i: $columnName.hashCode();
       result = i1 + 59;
       String $dataType = this.getDataType();
       int i2 = result * 59;
       i1 = ($dataType == null)? i: $dataType.hashCode();
       result = i2 + i1;
       String $dataLength = this.getDataLength();
       i2 = result * 59;
       i1 = ($dataLength == null)? i: $dataLength.hashCode();
       result = i2 + i1;
       String $comments = this.getComments();
       i1 = result * 59;
       if ($comments != null) {
          i = $comments.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setComments(String comments){
       this.comments = comments;
    }
    public void setDataLength(String dataLength){
       this.dataLength = dataLength;
    }
    public void setDataType(String dataType){
       this.dataType = dataType;
    }
    public String toString(){
       return "TableColumnDto\(columnName="+this.getColumnName()+", dataType="+this.getDataType()+", dataLength="+this.getDataLength()+", comments="+this.getComments()+"\)";
    }
}
