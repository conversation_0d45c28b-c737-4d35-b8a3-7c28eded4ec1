"""
检查计划业务服务
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from datetime import datetime

from flycheck.models import Plan, PlanRule, FlyRule
from flycheck.schemas.plan import PlanCreate, PlanUpdate


class PlanService:
    """检查计划服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_plans(
        self, 
        skip: int = 0, 
        limit: int = 20,
        plan_name: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[Plan]:
        """获取计划列表"""
        query = self.db.query(Plan).filter(Plan.is_deleted == "0")
        
        # 添加过滤条件
        if plan_name:
            query = query.filter(Plan.plan_name.contains(plan_name))
        
        if status:
            query = query.filter(Plan.status == status)
        
        return query.offset(skip).limit(limit).all()
    
    def get_plan_by_id(self, plan_id: str) -> Optional[Plan]:
        """根据ID获取计划"""
        return (
            self.db.query(Plan)
            .filter(and_(Plan.id == plan_id, Plan.is_deleted == "0"))
            .first()
        )
    
    def create_plan(self, plan_data: PlanCreate) -> Plan:
        """创建计划"""
        # 提取规则ID列表
        rule_ids = plan_data.rule_ids or []
        plan_dict = plan_data.dict(exclude={'rule_ids'})
        
        plan = Plan(**plan_dict)
        plan.status = "draft"  # 默认草稿状态
        
        self.db.add(plan)
        self.db.flush()  # 获取plan.id
        
        # 关联规则
        if rule_ids:
            self._associate_rules(plan.id, rule_ids)
            plan.total_rules = len(rule_ids)
        
        self.db.commit()
        self.db.refresh(plan)
        
        return plan
    
    def update_plan(self, plan_id: str, plan_data: PlanUpdate) -> Optional[Plan]:
        """更新计划"""
        plan = self.get_plan_by_id(plan_id)
        if not plan:
            return None
        
        # 更新字段
        update_data = plan_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(plan, field):
                setattr(plan, field, value)
        
        plan.updated_time = datetime.now()
        
        self.db.commit()
        self.db.refresh(plan)
        
        return plan
    
    def delete_plan(self, plan_id: str) -> bool:
        """删除计划（软删除）"""
        plan = self.get_plan_by_id(plan_id)
        if not plan:
            return False
        
        plan.soft_delete()
        self.db.commit()
        
        return True
    
    def activate_plan(self, plan_id: str) -> bool:
        """激活计划"""
        plan = self.get_plan_by_id(plan_id)
        if not plan:
            return False
        
        plan.status = "active"
        plan.updated_time = datetime.now()
        self.db.commit()
        
        return True
    
    def deactivate_plan(self, plan_id: str) -> bool:
        """停用计划"""
        plan = self.get_plan_by_id(plan_id)
        if not plan:
            return False
        
        plan.status = "inactive"
        plan.updated_time = datetime.now()
        self.db.commit()
        
        return True
    
    def add_rules_to_plan(self, plan_id: str, rule_ids: List[str]) -> bool:
        """向计划添加规则"""
        plan = self.get_plan_by_id(plan_id)
        if not plan:
            return False
        
        self._associate_rules(plan_id, rule_ids)
        
        # 更新规则总数
        total_rules = self.db.query(PlanRule).filter(PlanRule.plan_id == plan_id).count()
        plan.total_rules = total_rules
        
        self.db.commit()
        return True
    
    def remove_rules_from_plan(self, plan_id: str, rule_ids: List[str]) -> bool:
        """从计划中移除规则"""
        plan = self.get_plan_by_id(plan_id)
        if not plan:
            return False
        
        # 删除关联关系
        self.db.query(PlanRule).filter(
            and_(
                PlanRule.plan_id == plan_id,
                PlanRule.rule_id.in_(rule_ids)
            )
        ).delete(synchronize_session=False)
        
        # 更新规则总数
        total_rules = self.db.query(PlanRule).filter(PlanRule.plan_id == plan_id).count()
        plan.total_rules = total_rules
        
        self.db.commit()
        return True
    
    def get_plan_rules(self, plan_id: str) -> List[dict]:
        """获取计划关联的规则"""
        plan_rules = (
            self.db.query(PlanRule, FlyRule)
            .join(FlyRule, PlanRule.rule_id == FlyRule.id)
            .filter(PlanRule.plan_id == plan_id)
            .order_by(PlanRule.execution_order)
            .all()
        )
        
        result = []
        for plan_rule, rule in plan_rules:
            result.append({
                "plan_rule_id": plan_rule.id,
                "rule_id": rule.id,
                "rule_name": rule.rule_name,
                "rule_type": rule.rule_type,
                "execution_order": plan_rule.execution_order,
                "status": plan_rule.status,
                "rule_params": plan_rule.rule_params
            })
        
        return result
    
    def _associate_rules(self, plan_id: str, rule_ids: List[str]):
        """关联规则到计划"""
        for i, rule_id in enumerate(rule_ids):
            # 检查规则是否存在
            rule = self.db.query(FlyRule).filter(FlyRule.id == rule_id).first()
            if not rule:
                continue
            
            # 检查是否已关联
            existing = self.db.query(PlanRule).filter(
                and_(
                    PlanRule.plan_id == plan_id,
                    PlanRule.rule_id == rule_id
                )
            ).first()
            
            if not existing:
                plan_rule = PlanRule(
                    plan_id=plan_id,
                    rule_id=rule_id,
                    execution_order=i + 1,
                    status="pending"
                )
                self.db.add(plan_rule)
