package com.taikang.fly.check.service.YbDrugListService;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.ybdruglist.YbDrugListSearchDto;
import com.taikang.fly.check.comm.NativePage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.dto.DatasourceInfo;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.DrugCatalogueMapper;
import com.taikang.fly.check.dto.mapstruct.DrugCatalogueListMapping;

public class YbDrugListService	// class@00030f from classes.dex
{
    private DrugCatalogueListMapping drugCatalogueListMapping;
    private DrugCatalogueMapper drugCatalogueMapper;

    public void YbDrugListService(){
       super();
    }
    public NativePage queryList(Integer page,Integer size,YbDrugListSearchDto ybDrugListSearchDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       String oraUserName = ThreadLocalContextHolder.getContext().getUserInfo().getDatasourceInfo().getUsername();
       List drugCatalogueList = this.drugCatalogueMapper.queryList(ybDrugListSearchDto, oraUserName);
       List drugCatalogueListRespDtoList = this.drugCatalogueListMapping.entryToDtoList(drugCatalogueList);
       NativePage pageDto = new NativePage(drugCatalogueListRespDtoList, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
}
