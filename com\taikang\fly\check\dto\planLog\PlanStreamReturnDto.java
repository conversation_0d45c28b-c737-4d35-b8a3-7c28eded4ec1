package com.taikang.fly.check.dto.planLog.PlanStreamReturnDto;
import java.lang.Object;
import java.lang.String;
import java.io.ByteArrayOutputStream;
import java.lang.Integer;
import java.util.Map;
import java.lang.StringBuilder;

public class PlanStreamReturnDto	// class@0001a9 from classes.dex
{
    private String exceptionMes;
    private String flag;
    private Map resultMap;
    private String ruleId;
    private String ruleName;
    private ByteArrayOutputStream stream;
    private Integer totalSize;

    public void PlanStreamReturnDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanStreamReturnDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanStreamReturnDto){
          b = false;
       }else {
          PlanStreamReturnDto planStreamRe = o;
          if (!planStreamRe.canEqual(this)) {
             b = false;
          }else {
             String flag = this.getFlag();
             String flag1 = planStreamRe.getFlag();
             if (flag == null) {
                if (flag1 != null) {
                   b = false;
                }
             }else if(flag.equals(flag1)){
             }
             ByteArrayOutputStream stream = this.getStream();
             ByteArrayOutputStream stream1 = planStreamRe.getStream();
             if (stream == null) {
                if (stream1 != null) {
                   b = false;
                }
             }else if(stream.equals(stream1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = planStreamRe.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String exceptionMes = this.getExceptionMes();
             String exceptionMes1 = planStreamRe.getExceptionMes();
             if (exceptionMes == null) {
                if (exceptionMes1 != null) {
                   b = false;
                }
             }else if(exceptionMes.equals(exceptionMes1)){
             }
             Integer totalSize = this.getTotalSize();
             Integer totalSize1 = planStreamRe.getTotalSize();
             if (totalSize == null) {
                if (totalSize1 != null) {
                   b = false;
                }
             }else if(totalSize.equals(totalSize1)){
             }
             Map resultMap = this.getResultMap();
             Map resultMap1 = planStreamRe.getResultMap();
             if (resultMap == null) {
                if (resultMap1 != null) {
                   b = false;
                }
             }else if(resultMap.equals(resultMap1)){
             }
             String ruleId = this.getRuleId();
             String ruleId1 = planStreamRe.getRuleId();
             if (ruleId == null) {
                if (ruleId1 != null) {
                   b = false;
                }
             }else if(ruleId.equals(ruleId1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getExceptionMes(){
       return this.exceptionMes;
    }
    public String getFlag(){
       return this.flag;
    }
    public Map getResultMap(){
       return this.resultMap;
    }
    public String getRuleId(){
       return this.ruleId;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public ByteArrayOutputStream getStream(){
       return this.stream;
    }
    public Integer getTotalSize(){
       return this.totalSize;
    }
    public int hashCode(){
       String $flag;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($flag = this.getFlag()) == null)? i: $flag.hashCode();
       result = i1 + 59;
       ByteArrayOutputStream $stream = this.getStream();
       int i2 = result * 59;
       i1 = ($stream == null)? i: $stream.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $exceptionMes = this.getExceptionMes();
       i2 = result * 59;
       i1 = ($exceptionMes == null)? i: $exceptionMes.hashCode();
       result = i2 + i1;
       Integer $totalSize = this.getTotalSize();
       i2 = result * 59;
       i1 = ($totalSize == null)? i: $totalSize.hashCode();
       result = i2 + i1;
       Map resultMap = this.getResultMap();
       i2 = result * 59;
       i1 = (resultMap == null)? i: resultMap.hashCode();
       String ruleId = this.getRuleId();
       i1 = (i2 + i1) * 59;
       if (ruleId != null) {
          i = ruleId.hashCode();
       }
       return (i1 + i);
    }
    public void setExceptionMes(String exceptionMes){
       this.exceptionMes = exceptionMes;
    }
    public void setFlag(String flag){
       this.flag = flag;
    }
    public void setResultMap(Map resultMap){
       this.resultMap = resultMap;
    }
    public void setRuleId(String ruleId){
       this.ruleId = ruleId;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setStream(ByteArrayOutputStream stream){
       this.stream = stream;
    }
    public void setTotalSize(Integer totalSize){
       this.totalSize = totalSize;
    }
    public String toString(){
       return "PlanStreamReturnDto\(flag="+this.getFlag()+", stream="+this.getStream()+", ruleName="+this.getRuleName()+", exceptionMes="+this.getExceptionMes()+", totalSize="+this.getTotalSize()+", resultMap="+this.getResultMap()+", ruleId="+this.getRuleId()+"\)";
    }
}
