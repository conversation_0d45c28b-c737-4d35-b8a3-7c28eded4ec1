"""
系统管理相关模型
"""

from sqlalchemy import Column, String, Text, JSON

from .base import BaseModel


class User(BaseModel):
    """用户"""
    
    __tablename__ = "sys_user"
    
    username = Column(String(50), nullable=False, unique=True, comment="用户名")
    password = Column(String(255), nullable=False, comment="密码")
    real_name = Column(String(50), comment="真实姓名")
    email = Column(String(100), comment="邮箱")
    phone = Column(String(20), comment="电话")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-启用 0-禁用")
    
    # 扩展信息
    profile = Column(JSON, comment="用户配置")


class Role(BaseModel):
    """角色"""
    
    __tablename__ = "sys_role"
    
    role_name = Column(String(50), nullable=False, comment="角色名称")
    role_code = Column(String(50), nullable=False, unique=True, comment="角色编码")
    description = Column(Text, comment="角色描述")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-启用 0-禁用")


class Menu(BaseModel):
    """菜单"""
    
    __tablename__ = "sys_menu"
    
    menu_name = Column(String(50), nullable=False, comment="菜单名称")
    menu_code = Column(String(50), comment="菜单编码")
    parent_id = Column(String, comment="父菜单ID")
    menu_type = Column(String(10), comment="菜单类型 1-目录 2-菜单 3-按钮")
    path = Column(String(200), comment="路由路径")
    component = Column(String(200), comment="组件路径")
    icon = Column(String(50), comment="图标")
    sort_order = Column(String(10), comment="排序")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-启用 0-禁用")


class SystemConfig(BaseModel):
    """系统配置"""
    
    __tablename__ = "sys_config"
    
    config_key = Column(String(100), nullable=False, unique=True, comment="配置键")
    config_value = Column(Text, comment="配置值")
    config_type = Column(String(20), comment="配置类型")
    description = Column(Text, comment="配置描述")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-启用 0-禁用")
