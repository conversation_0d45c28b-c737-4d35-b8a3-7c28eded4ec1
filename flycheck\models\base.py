"""
基础模型类
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, DateTime, func
from sqlalchemy.dialects.postgresql import UUID
import uuid

from flycheck.database import Base


class BaseModel(Base):
    """基础模型类"""
    
    __abstract__ = True
    
    id = Column(
        String, 
        primary_key=True, 
        default=lambda: str(uuid.uuid4()),
        comment="主键ID"
    )
    
    created_time = Column(
        DateTime,
        default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_time = Column(
        DateTime,
        default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    created_by = Column(
        String(50),
        nullable=True,
        comment="创建人"
    )
    
    updated_by = Column(
        String(50),
        nullable=True,
        comment="更新人"
    )
    
    is_deleted = Column(
        String(1),
        default="0",
        nullable=False,
        comment="是否删除 0-否 1-是"
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: dict) -> None:
        """从字典更新属性"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def soft_delete(self) -> None:
        """软删除"""
        self.is_deleted = "1"
        self.updated_time = datetime.now()
    
    def restore(self) -> None:
        """恢复删除"""
        self.is_deleted = "0"
        self.updated_time = datetime.now()
