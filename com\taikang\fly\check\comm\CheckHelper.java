package com.taikang.fly.check.comm.CheckHelper;
import java.lang.annotation.Annotation;
import java.lang.String;

public interface abstract CheckHelper implements Annotation	// class@00006f from classes.dex
{

    String dictValues();
    String emptyCode();
    boolean isDate();
    boolean isLength();
    boolean isNumber();
    int length();
    String lengthCode();
    String lengthRule();
    String name();
    boolean notEmpty();
    String numberCode();
}
