package com.taikang.fly.check.dto.clickhouseFlyRule.OraTableInfoDto;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class OraTableInfoDto	// class@0000d1 from classes.dex
{
    private List column;
    private String tableName;

    public void OraTableInfoDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof OraTableInfoDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof OraTableInfoDto) {
             b = false;
          }else {
             OraTableInfoDto oraTableInfo = o;
             if (!oraTableInfo.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = oraTableInfo.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                List column = this.getColumn();
                List column1 = oraTableInfo.getColumn();
                if (column == null) {
                   if (column1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!column.equals(column1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getColumn(){
       return this.column;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       List $column = this.getColumn();
       i1 = result * 59;
       if ($column != null) {
          i = $column.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumn(List column){
       this.column = column;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "OraTableInfoDto\(tableName="+this.getTableName()+", column="+this.getColumn()+"\)";
    }
}
