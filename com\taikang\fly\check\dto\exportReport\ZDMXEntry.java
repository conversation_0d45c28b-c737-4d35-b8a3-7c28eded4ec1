package com.taikang.fly.check.dto.exportReport.ZDMXEntry;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ZDMXEntry	// class@0000f3 from classes.dex
{
    private String MZMX;
    private String MZZD;
    private String ZYMX;
    private String ZYZD;
    private String avgLenOfStay;
    private String avgPriceOfStay;
    private String countMAvgDay;
    private String mzYlSum;
    private String mzhz;
    private String mzjbtcSum;
    private String mzjz;
    private String priceMAvgSecond;
    private String zyYlSum;
    private String zyhz;
    private String zyjbtcSum;
    private String zyjz;

    public void ZDMXEntry(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ZDMXEntry;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ZDMXEntry){
          b = false;
       }else {
          ZDMXEntry zDMXEntry = o;
          if (!zDMXEntry.canEqual(this)) {
             b = false;
          }else {
             String zYZD = this.getZYZD();
             String zYZD1 = zDMXEntry.getZYZD();
             if (zYZD == null) {
                if (zYZD1 != null) {
                   b = false;
                }
             }else if(zYZD.equals(zYZD1)){
             }
             String zYMX = this.getZYMX();
             String zYMX1 = zDMXEntry.getZYMX();
             if (zYMX == null) {
                if (zYMX1 != null) {
                   b = false;
                }
             }else if(zYMX.equals(zYMX1)){
             }
             String mZZD = this.getMZZD();
             String mZZD1 = zDMXEntry.getMZZD();
             if (mZZD == null) {
                if (mZZD1 != null) {
                   b = false;
                }
             }else if(mZZD.equals(mZZD1)){
             }
             String mZMX = this.getMZMX();
             String mZMX1 = zDMXEntry.getMZMX();
             if (mZMX == null) {
                if (mZMX1 != null) {
                   b = false;
                }
             }else if(mZMX.equals(mZMX1)){
             }
             String mzYlSum = this.getMzYlSum();
             String mzYlSum1 = zDMXEntry.getMzYlSum();
             if (mzYlSum == null) {
                if (mzYlSum1 != null) {
                   b = false;
                }
             }else if(mzYlSum.equals(mzYlSum1)){
             }
             String mzjbtcSum = this.getMzjbtcSum();
             String mzjbtcSum1 = zDMXEntry.getMzjbtcSum();
             if (mzjbtcSum == null) {
                if (mzjbtcSum1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(mzjbtcSum.equals(mzjbtcSum1)){
             }
             String zyYlSum = this.getZyYlSum();
             String zyYlSum1 = zDMXEntry.getZyYlSum();
             if (zyYlSum == null) {
                if (zyYlSum1 != null) {
                   b = false;
                }
             }else if(zyYlSum.equals(zyYlSum1)){
             }
             String zyjbtcSum = this.getZyjbtcSum();
             String zyjbtcSum1 = zDMXEntry.getZyjbtcSum();
             if (zyjbtcSum == null) {
                if (zyjbtcSum1 != null) {
                label_00d3 :
                   b = false;
                }
             }else if(zyjbtcSum.equals(zyjbtcSum1)){
             }
             String mzhz = this.getMzhz();
             String mzhz1 = zDMXEntry.getMzhz();
             if (mzhz == null) {
                if (mzhz1 != null) {
                   b = false;
                }
             }else if(mzhz.equals(mzhz1)){
             }
             String mzjz = this.getMzjz();
             String mzjz1 = zDMXEntry.getMzjz();
             if (mzjz == null) {
                if (mzjz1 != null) {
                label_0105 :
                   b = false;
                }
             }else if(mzjz.equals(mzjz1)){
             }
             String zyhz = this.getZyhz();
             String zyhz1 = zDMXEntry.getZyhz();
             if (zyhz == null) {
                if (zyhz1 != null) {
                   b = false;
                }
             }else if(zyhz.equals(zyhz1)){
             }
             String zyjz = this.getZyjz();
             String zyjz1 = zDMXEntry.getZyjz();
             if (zyjz == null) {
                if (zyjz1 != null) {
                label_0137 :
                   b = false;
                }
             }else if(zyjz.equals(zyjz1)){
             }
             String countMAvgDay = this.getCountMAvgDay();
             String countMAvgDay1 = zDMXEntry.getCountMAvgDay();
             if (countMAvgDay == null) {
                if (countMAvgDay1 != null) {
                   b = false;
                }
             }else if(countMAvgDay.equals(countMAvgDay1)){
             }
             String priceMAvgSec = this.getPriceMAvgSecond();
             String priceMAvgSec1 = zDMXEntry.getPriceMAvgSecond();
             if (priceMAvgSec == null) {
                if (priceMAvgSec1 != null) {
                label_0169 :
                   b = false;
                }
             }else if(priceMAvgSec.equals(priceMAvgSec1)){
             }
             String avgLenOfStay = this.getAvgLenOfStay();
             String avgLenOfStay1 = zDMXEntry.getAvgLenOfStay();
             if (avgLenOfStay == null) {
                if (avgLenOfStay1 != null) {
                   b = false;
                }
             }else if(avgLenOfStay.equals(avgLenOfStay1)){
             }
             String avgPriceOfSt = this.getAvgPriceOfStay();
             String avgPriceOfSt1 = zDMXEntry.getAvgPriceOfStay();
             if (avgPriceOfSt == null) {
                if (avgPriceOfSt1 != null) {
                   b = false;
                }
             }else if(avgPriceOfSt.equals(avgPriceOfSt1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAvgLenOfStay(){
       return this.avgLenOfStay;
    }
    public String getAvgPriceOfStay(){
       return this.avgPriceOfStay;
    }
    public String getCountMAvgDay(){
       return this.countMAvgDay;
    }
    public String getMZMX(){
       return this.MZMX;
    }
    public String getMZZD(){
       return this.MZZD;
    }
    public String getMzYlSum(){
       return this.mzYlSum;
    }
    public String getMzhz(){
       return this.mzhz;
    }
    public String getMzjbtcSum(){
       return this.mzjbtcSum;
    }
    public String getMzjz(){
       return this.mzjz;
    }
    public String getPriceMAvgSecond(){
       return this.priceMAvgSecond;
    }
    public String getZYMX(){
       return this.ZYMX;
    }
    public String getZYZD(){
       return this.ZYZD;
    }
    public String getZyYlSum(){
       return this.zyYlSum;
    }
    public String getZyhz(){
       return this.zyhz;
    }
    public String getZyjbtcSum(){
       return this.zyjbtcSum;
    }
    public String getZyjz(){
       return this.zyjz;
    }
    public int hashCode(){
       String $ZYZD;
       int PRIME = 59;
       int result = 1;
       int i = (($ZYZD = this.getZYZD()) == null)? 43: $ZYZD.hashCode();
       result = i + 59;
       String $ZYMX = this.getZYMX();
       int i1 = result * 59;
       i = ($ZYMX == null)? 43: $ZYMX.hashCode();
       result = i1 + i;
       String $MZZD = this.getMZZD();
       i1 = result * 59;
       i = ($MZZD == null)? 43: $MZZD.hashCode();
       result = i1 + i;
       String $MZMX = this.getMZMX();
       i1 = result * 59;
       i = ($MZMX == null)? 43: $MZMX.hashCode();
       result = i1 + i;
       String $mzYlSum = this.getMzYlSum();
       i1 = result * 59;
       i = ($mzYlSum == null)? 43: $mzYlSum.hashCode();
       result = i1 + i;
       String mzjbtcSum = this.getMzjbtcSum();
       i1 = result * 59;
       i = (mzjbtcSum == null)? 43: mzjbtcSum.hashCode();
       String zyYlSum = this.getZyYlSum();
       i1 = (i1 + i) * 59;
       i = (zyYlSum == null)? 43: zyYlSum.hashCode();
       String zyjbtcSum = this.getZyjbtcSum();
       i1 = (i1 + i) * 59;
       i = (zyjbtcSum == null)? 43: zyjbtcSum.hashCode();
       String mzhz = this.getMzhz();
       i1 = (i1 + i) * 59;
       i = (mzhz == null)? 43: mzhz.hashCode();
       String mzjz = this.getMzjz();
       i1 = (i1 + i) * 59;
       i = (mzjz == null)? 43: mzjz.hashCode();
       String zyhz = this.getZyhz();
       i1 = (i1 + i) * 59;
       i = (zyhz == null)? 43: zyhz.hashCode();
       String zyjz = this.getZyjz();
       i1 = (i1 + i) * 59;
       i = (zyjz == null)? 43: zyjz.hashCode();
       String countMAvgDay = this.getCountMAvgDay();
       i1 = (i1 + i) * 59;
       i = (countMAvgDay == null)? 43: countMAvgDay.hashCode();
       String priceMAvgSec = this.getPriceMAvgSecond();
       i1 = (i1 + i) * 59;
       i = (priceMAvgSec == null)? 43: priceMAvgSec.hashCode();
       String avgLenOfStay = this.getAvgLenOfStay();
       i1 = (i1 + i) * 59;
       i = (avgLenOfStay == null)? 43: avgLenOfStay.hashCode();
       String avgPriceOfSt = this.getAvgPriceOfStay();
       i1 = (i1 + i) * 59;
       i = (avgPriceOfSt == null)? 43: avgPriceOfSt.hashCode();
       return (i1 + i);
    }
    public void setAvgLenOfStay(String avgLenOfStay){
       this.avgLenOfStay = avgLenOfStay;
    }
    public void setAvgPriceOfStay(String avgPriceOfStay){
       this.avgPriceOfStay = avgPriceOfStay;
    }
    public void setCountMAvgDay(String countMAvgDay){
       this.countMAvgDay = countMAvgDay;
    }
    public void setMZMX(String MZMX){
       this.MZMX = MZMX;
    }
    public void setMZZD(String MZZD){
       this.MZZD = MZZD;
    }
    public void setMzYlSum(String mzYlSum){
       this.mzYlSum = mzYlSum;
    }
    public void setMzhz(String mzhz){
       this.mzhz = mzhz;
    }
    public void setMzjbtcSum(String mzjbtcSum){
       this.mzjbtcSum = mzjbtcSum;
    }
    public void setMzjz(String mzjz){
       this.mzjz = mzjz;
    }
    public void setPriceMAvgSecond(String priceMAvgSecond){
       this.priceMAvgSecond = priceMAvgSecond;
    }
    public void setZYMX(String ZYMX){
       this.ZYMX = ZYMX;
    }
    public void setZYZD(String ZYZD){
       this.ZYZD = ZYZD;
    }
    public void setZyYlSum(String zyYlSum){
       this.zyYlSum = zyYlSum;
    }
    public void setZyhz(String zyhz){
       this.zyhz = zyhz;
    }
    public void setZyjbtcSum(String zyjbtcSum){
       this.zyjbtcSum = zyjbtcSum;
    }
    public void setZyjz(String zyjz){
       this.zyjz = zyjz;
    }
    public String toString(){
       return "ZDMXEntry\(ZYZD="+this.getZYZD()+", ZYMX="+this.getZYMX()+", MZZD="+this.getMZZD()+", MZMX="+this.getMZMX()+", mzYlSum="+this.getMzYlSum()+", mzjbtcSum="+this.getMzjbtcSum()+", zyYlSum="+this.getZyYlSum()+", zyjbtcSum="+this.getZyjbtcSum()+", mzhz="+this.getMzhz()+", mzjz="+this.getMzjz()+", zyhz="+this.getZyhz()+", zyjz="+this.getZyjz()+", countMAvgDay="+this.getCountMAvgDay()+", priceMAvgSecond="+this.getPriceMAvgSecond()+", avgLenOfStay="+this.getAvgLenOfStay()+", avgPriceOfStay="+this.getAvgPriceOfStay()+"\)";
    }
}
