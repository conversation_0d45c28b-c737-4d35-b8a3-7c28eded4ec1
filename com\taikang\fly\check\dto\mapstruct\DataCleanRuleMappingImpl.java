package com.taikang.fly.check.dto.mapstruct.DataCleanRuleMappingImpl;
import com.taikang.fly.check.dto.mapstruct.DataCleanRuleMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleAddDto;
import com.taikang.fly.check.mybatis.domain.DataCleanRule;
import java.lang.String;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.time.LocalDateTime;
import com.taikang.fly.check.utils.SequenceGenerator;
import com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleEditDto;

public class DataCleanRuleMappingImpl implements DataCleanRuleMapping	// class@000147 from classes.dex
{

    public void DataCleanRuleMappingImpl(){
       super();
    }
    public DataCleanRule addDto2DataCleanRule(DataCleanRuleAddDto addDto){
       DataCleanRule uDataCleanRu;
       if (addDto == null) {
          uDataCleanRu = null;
       }else {
          uDataCleanRu = new DataCleanRule();
          uDataCleanRu.setTableName(addDto.getTableName());
          uDataCleanRu.setFieldName(addDto.getFieldName());
          uDataCleanRu.setCleanRule(addDto.getCleanRule());
          uDataCleanRu.setExplain(addDto.getExplain());
          uDataCleanRu.setCreator(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          uDataCleanRu.setModifyTime(LocalDateTime.now());
          uDataCleanRu.setCreatedTime(LocalDateTime.now());
          uDataCleanRu.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          uDataCleanRu.setId(SequenceGenerator.getId());
          uDataCleanRu.setStatus("00");
       }
       return uDataCleanRu;
    }
    public DataCleanRule editDto2DataCleanRule(DataCleanRuleEditDto editDto){
       DataCleanRule uDataCleanRu;
       if (editDto == null) {
          uDataCleanRu = null;
       }else {
          uDataCleanRu = new DataCleanRule();
          uDataCleanRu.setId(editDto.getId());
          uDataCleanRu.setTableName(editDto.getTableName());
          uDataCleanRu.setFieldName(editDto.getFieldName());
          uDataCleanRu.setCleanRule(editDto.getCleanRule());
          uDataCleanRu.setExplain(editDto.getExplain());
          uDataCleanRu.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          uDataCleanRu.setModifyTime(LocalDateTime.now());
       }
       return uDataCleanRu;
    }
}
