package com.taikang.fly.check.mybatis.domain.MergeField;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class MergeField	// class@000251 from classes.dex
{
    private String database;
    private String field;
    private Integer tag;

    public void MergeField(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MergeField;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MergeField) {
             b = false;
          }else {
             MergeField mergeField = o;
             if (!mergeField.canEqual(this)) {
                b = false;
             }else {
                String database = this.getDatabase();
                String database1 = mergeField.getDatabase();
                if (database == null) {
                   if (database1 != null) {
                      b = false;
                   }
                }else if(database.equals(database1)){
                }
                String field = this.getField();
                String field1 = mergeField.getField();
                if (field == null) {
                   if (field1 != null) {
                      b = false;
                   }
                }else if(field.equals(field1)){
                }
                Integer tag = this.getTag();
                Integer tag1 = mergeField.getTag();
                if (tag == null) {
                   if (tag1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!tag.equals(tag1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDatabase(){
       return this.database;
    }
    public String getField(){
       return this.field;
    }
    public Integer getTag(){
       return this.tag;
    }
    public int hashCode(){
       String $database;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($database = this.getDatabase()) == null)? i: $database.hashCode();
       result = i1 + 59;
       String $field = this.getField();
       int i2 = result * 59;
       i1 = ($field == null)? i: $field.hashCode();
       result = i2 + i1;
       Integer $tag = this.getTag();
       i1 = result * 59;
       if ($tag != null) {
          i = $tag.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDatabase(String database){
       this.database = database;
    }
    public void setField(String field){
       this.field = field;
    }
    public void setTag(Integer tag){
       this.tag = tag;
    }
    public String toString(){
       return "MergeField\(database="+this.getDatabase()+", field="+this.getField()+", tag="+this.getTag()+"\)";
    }
}
