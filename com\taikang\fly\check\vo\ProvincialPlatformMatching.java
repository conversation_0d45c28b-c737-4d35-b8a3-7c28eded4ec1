package com.taikang.fly.check.vo.ProvincialPlatformMatching;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ProvincialPlatformMatching implements Serializable	// class@00035b from classes.dex
{
    private String createSql;
    private String id;
    private String newCreateSql;
    private String tableName;
    private String type;

    public void ProvincialPlatformMatching(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ProvincialPlatformMatching;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ProvincialPlatformMatching) {
             b = false;
          }else {
             ProvincialPlatformMatching provincialPl = o;
             if (!provincialPl.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = provincialPl.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String tableName = this.getTableName();
                String tableName1 = provincialPl.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String createSql = this.getCreateSql();
                String createSql1 = provincialPl.getCreateSql();
                if (createSql == null) {
                   if (createSql1 != null) {
                      b = false;
                   }
                }else if(createSql.equals(createSql1)){
                }
                String newCreateSql = this.getNewCreateSql();
                String newCreateSql1 = provincialPl.getNewCreateSql();
                if (newCreateSql == null) {
                   if (newCreateSql1 != null) {
                      b = false;
                   }
                }else if(newCreateSql.equals(newCreateSql1)){
                }
                String type = this.getType();
                String type1 = provincialPl.getType();
                if (type == null) {
                   if (type1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!type.equals(type1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCreateSql(){
       return this.createSql;
    }
    public String getId(){
       return this.id;
    }
    public String getNewCreateSql(){
       return this.newCreateSql;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getType(){
       return this.type;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tableName = this.getTableName();
       int i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $createSql = this.getCreateSql();
       i2 = result * 59;
       i1 = ($createSql == null)? i: $createSql.hashCode();
       result = i2 + i1;
       String $newCreateSql = this.getNewCreateSql();
       i2 = result * 59;
       i1 = ($newCreateSql == null)? i: $newCreateSql.hashCode();
       result = i2 + i1;
       String $type = this.getType();
       i1 = result * 59;
       if ($type != null) {
          i = $type.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCreateSql(String createSql){
       this.createSql = createSql;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewCreateSql(String newCreateSql){
       this.newCreateSql = newCreateSql;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public void setType(String type){
       this.type = type;
    }
    public String toString(){
       return "ProvincialPlatformMatching\(id="+this.getId()+", tableName="+this.getTableName()+", createSql="+this.getCreateSql()+", newCreateSql="+this.getNewCreateSql()+", type="+this.getType()+"\)";
    }
}
