package com.taikang.fly.check.dto.ybDrugInstructions.YbDrugInstructionsRespDto;
import java.io.Serializable;
import java.lang.Long;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class YbDrugInstructionsRespDto implements Serializable	// class@0001d0 from classes.dex
{
    private String addre;
    private String applicable;
    private Date approvalData;
    private String approvalNumber;
    private String character;
    private String classification;
    private String component;
    private String drugAction;
    private String drugInteractions;
    private String englishName;
    private String genericName;
    private String genericName2;
    private String id;
    private String manufacturingEnterprise;
    private String mattersNeeding;
    private String sideEffects;
    private String specifications;
    private String store;
    private String taboo;
    private String termOfValidity;
    private String tradeName;
    private String usageDosage;
    private static final Long serialVersionUID;

    static {
       YbDrugInstructionsRespDto.serialVersionUID = Long.valueOf(1);
    }
    public void YbDrugInstructionsRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbDrugInstructionsRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof YbDrugInstructionsRespDto){
          b = false;
       }else {
          YbDrugInstructionsRespDto ybDrugInstru = o;
          if (!ybDrugInstru.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = ybDrugInstru.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String genericName = this.getGenericName();
             String genericName1 = ybDrugInstru.getGenericName();
             if (genericName == null) {
                if (genericName1 != null) {
                   b = false;
                }
             }else if(genericName.equals(genericName1)){
             }
             String classificati = this.getClassification();
             String classificati1 = ybDrugInstru.getClassification();
             if (classificati == null) {
                if (classificati1 != null) {
                   b = false;
                }
             }else if(classificati.equals(classificati1)){
             }
             String tradeName = this.getTradeName();
             String tradeName1 = ybDrugInstru.getTradeName();
             if (tradeName == null) {
                if (tradeName1 != null) {
                   b = false;
                }
             }else if(tradeName.equals(tradeName1)){
             }
             String genericName2 = this.getGenericName2();
             String genericName21 = ybDrugInstru.getGenericName2();
             if (genericName2 == null) {
                if (genericName21 != null) {
                   b = false;
                }
             }else if(genericName2.equals(genericName21)){
             }
             String englishName = this.getEnglishName();
             String englishName1 = ybDrugInstru.getEnglishName();
             if (englishName == null) {
                if (englishName1 != null) {
                   b = false;
                }
             }else if(englishName.equals(englishName1)){
             }
             String component = this.getComponent();
             String component1 = ybDrugInstru.getComponent();
             if (component == null) {
                if (component1 != null) {
                label_00bd :
                   b = false;
                }
             }else if(component.equals(component1)){
             }
             String character = this.getCharacter();
             String character1 = ybDrugInstru.getCharacter();
             if (character == null) {
                if (character1 != null) {
                   b = false;
                }
             }else if(character.equals(character1)){
             }
             String applicable = this.getApplicable();
             String applicable1 = ybDrugInstru.getApplicable();
             if (applicable == null) {
                if (applicable1 != null) {
                label_00ed :
                   b = false;
                }
             }else if(applicable.equals(applicable1)){
             }
             String specificatio = this.getSpecifications();
             String specificatio1 = ybDrugInstru.getSpecifications();
             if (specificatio == null) {
                if (specificatio1 != null) {
                   b = false;
                }
             }else if(specificatio.equals(specificatio1)){
             }
             String usageDosage = this.getUsageDosage();
             String usageDosage1 = ybDrugInstru.getUsageDosage();
             if (usageDosage == null) {
                if (usageDosage1 != null) {
                label_011f :
                   b = false;
                }
             }else if(usageDosage.equals(usageDosage1)){
             }
             String sideEffects = this.getSideEffects();
             String sideEffects1 = ybDrugInstru.getSideEffects();
             if (sideEffects == null) {
                if (sideEffects1 != null) {
                   b = false;
                }
             }else if(sideEffects.equals(sideEffects1)){
             }
             String taboo = this.getTaboo();
             String taboo1 = ybDrugInstru.getTaboo();
             if (taboo == null) {
                if (taboo1 != null) {
                   b = false;
                }
             }else if(taboo.equals(taboo1)){
             }
             String mattersNeedi = this.getMattersNeeding();
             String mattersNeedi1 = ybDrugInstru.getMattersNeeding();
             if (mattersNeedi == null) {
                if (mattersNeedi1 != null) {
                label_016d :
                   b = false;
                }
             }else if(mattersNeedi.equals(mattersNeedi1)){
             }
             String drugInteract = this.getDrugInteractions();
             String drugInteract1 = ybDrugInstru.getDrugInteractions();
             if (drugInteract == null) {
                if (drugInteract1 != null) {
                   b = false;
                }
             }else if(drugInteract.equals(drugInteract1)){
             }
             String drugAction = this.getDrugAction();
             String drugAction1 = ybDrugInstru.getDrugAction();
             if (drugAction == null) {
                if (drugAction1 != null) {
                label_019f :
                   b = false;
                }
             }else if(drugAction.equals(drugAction1)){
             }
             String store = this.getStore();
             String store1 = ybDrugInstru.getStore();
             if (store == null) {
                if (store1 != null) {
                   b = false;
                }
             }else if(store.equals(store1)){
             }
             String termOfValidi = this.getTermOfValidity();
             String termOfValidi1 = ybDrugInstru.getTermOfValidity();
             if (termOfValidi == null) {
                if (termOfValidi1 != null) {
                label_01d1 :
                   b = false;
                }
             }else if(termOfValidi.equals(termOfValidi1)){
             }
             String approvalNumb = this.getApprovalNumber();
             String approvalNumb1 = ybDrugInstru.getApprovalNumber();
             if (approvalNumb == null) {
                if (approvalNumb1 != null) {
                   b = false;
                }
             }else if(approvalNumb.equals(approvalNumb1)){
             }
             Date approvalData = this.getApprovalData();
             Date approvalData1 = ybDrugInstru.getApprovalData();
             if (approvalData == null) {
                if (approvalData1 != null) {
                label_0203 :
                   b = false;
                }
             }else if(approvalData.equals(approvalData1)){
             }
             String manufacturin = this.getManufacturingEnterprise();
             String manufacturin1 = ybDrugInstru.getManufacturingEnterprise();
             if (manufacturin == null) {
                if (manufacturin1 != null) {
                   b = false;
                }
             }else if(manufacturin.equals(manufacturin1)){
             }
             String addre = this.getAddre();
             String addre1 = ybDrugInstru.getAddre();
             if (addre == null) {
                if (addre1 != null) {
                label_0235 :
                   b = false;
                }
             }else if(addre.equals(addre1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAddre(){
       return this.addre;
    }
    public String getApplicable(){
       return this.applicable;
    }
    public Date getApprovalData(){
       return this.approvalData;
    }
    public String getApprovalNumber(){
       return this.approvalNumber;
    }
    public String getCharacter(){
       return this.character;
    }
    public String getClassification(){
       return this.classification;
    }
    public String getComponent(){
       return this.component;
    }
    public String getDrugAction(){
       return this.drugAction;
    }
    public String getDrugInteractions(){
       return this.drugInteractions;
    }
    public String getEnglishName(){
       return this.englishName;
    }
    public String getGenericName(){
       return this.genericName;
    }
    public String getGenericName2(){
       return this.genericName2;
    }
    public String getId(){
       return this.id;
    }
    public String getManufacturingEnterprise(){
       return this.manufacturingEnterprise;
    }
    public String getMattersNeeding(){
       return this.mattersNeeding;
    }
    public String getSideEffects(){
       return this.sideEffects;
    }
    public String getSpecifications(){
       return this.specifications;
    }
    public String getStore(){
       return this.store;
    }
    public String getTaboo(){
       return this.taboo;
    }
    public String getTermOfValidity(){
       return this.termOfValidity;
    }
    public String getTradeName(){
       return this.tradeName;
    }
    public String getUsageDosage(){
       return this.usageDosage;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $genericName = this.getGenericName();
       int i1 = result * 59;
       i = ($genericName == null)? 43: $genericName.hashCode();
       result = i1 + i;
       String $classification = this.getClassification();
       i1 = result * 59;
       i = ($classification == null)? 43: $classification.hashCode();
       result = i1 + i;
       String $tradeName = this.getTradeName();
       i1 = result * 59;
       i = ($tradeName == null)? 43: $tradeName.hashCode();
       result = i1 + i;
       String $genericName2 = this.getGenericName2();
       i1 = result * 59;
       i = ($genericName2 == null)? 43: $genericName2.hashCode();
       result = i1 + i;
       String englishName = this.getEnglishName();
       i1 = result * 59;
       i = (englishName == null)? 43: englishName.hashCode();
       String component = this.getComponent();
       i1 = (i1 + i) * 59;
       i = (component == null)? 43: component.hashCode();
       String character = this.getCharacter();
       i1 = (i1 + i) * 59;
       i = (character == null)? 43: character.hashCode();
       String applicable = this.getApplicable();
       i1 = (i1 + i) * 59;
       i = (applicable == null)? 43: applicable.hashCode();
       String specificatio = this.getSpecifications();
       i1 = (i1 + i) * 59;
       i = (specificatio == null)? 43: specificatio.hashCode();
       String usageDosage = this.getUsageDosage();
       i1 = (i1 + i) * 59;
       i = (usageDosage == null)? 43: usageDosage.hashCode();
       String sideEffects = this.getSideEffects();
       i1 = (i1 + i) * 59;
       i = (sideEffects == null)? 43: sideEffects.hashCode();
       String taboo = this.getTaboo();
       i1 = (i1 + i) * 59;
       i = (taboo == null)? 43: taboo.hashCode();
       String mattersNeedi = this.getMattersNeeding();
       i1 = (i1 + i) * 59;
       i = (mattersNeedi == null)? 43: mattersNeedi.hashCode();
       String drugInteract = this.getDrugInteractions();
       i1 = (i1 + i) * 59;
       i = (drugInteract == null)? 43: drugInteract.hashCode();
       String drugAction = this.getDrugAction();
       i1 = (i1 + i) * 59;
       i = (drugAction == null)? 43: drugAction.hashCode();
       String store = this.getStore();
       i1 = (i1 + i) * 59;
       i = (store == null)? 43: store.hashCode();
       String termOfValidi = this.getTermOfValidity();
       i1 = (i1 + i) * 59;
       i = (termOfValidi == null)? 43: termOfValidi.hashCode();
       String approvalNumb = this.getApprovalNumber();
       i1 = (i1 + i) * 59;
       i = (approvalNumb == null)? 43: approvalNumb.hashCode();
       Date approvalData = this.getApprovalData();
       i1 = (i1 + i) * 59;
       i = (approvalData == null)? 43: approvalData.hashCode();
       String manufacturin = this.getManufacturingEnterprise();
       i1 = (i1 + i) * 59;
       i = (manufacturin == null)? 43: manufacturin.hashCode();
       String addre = this.getAddre();
       i1 = (i1 + i) * 59;
       i = (addre == null)? 43: addre.hashCode();
       return (i1 + i);
    }
    public void setAddre(String addre){
       this.addre = addre;
    }
    public void setApplicable(String applicable){
       this.applicable = applicable;
    }
    public void setApprovalData(Date approvalData){
       this.approvalData = approvalData;
    }
    public void setApprovalNumber(String approvalNumber){
       this.approvalNumber = approvalNumber;
    }
    public void setCharacter(String character){
       this.character = character;
    }
    public void setClassification(String classification){
       this.classification = classification;
    }
    public void setComponent(String component){
       this.component = component;
    }
    public void setDrugAction(String drugAction){
       this.drugAction = drugAction;
    }
    public void setDrugInteractions(String drugInteractions){
       this.drugInteractions = drugInteractions;
    }
    public void setEnglishName(String englishName){
       this.englishName = englishName;
    }
    public void setGenericName(String genericName){
       this.genericName = genericName;
    }
    public void setGenericName2(String genericName2){
       this.genericName2 = genericName2;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setManufacturingEnterprise(String manufacturingEnterprise){
       this.manufacturingEnterprise = manufacturingEnterprise;
    }
    public void setMattersNeeding(String mattersNeeding){
       this.mattersNeeding = mattersNeeding;
    }
    public void setSideEffects(String sideEffects){
       this.sideEffects = sideEffects;
    }
    public void setSpecifications(String specifications){
       this.specifications = specifications;
    }
    public void setStore(String store){
       this.store = store;
    }
    public void setTaboo(String taboo){
       this.taboo = taboo;
    }
    public void setTermOfValidity(String termOfValidity){
       this.termOfValidity = termOfValidity;
    }
    public void setTradeName(String tradeName){
       this.tradeName = tradeName;
    }
    public void setUsageDosage(String usageDosage){
       this.usageDosage = usageDosage;
    }
    public String toString(){
       return "YbDrugInstructionsRespDto\(id="+this.getId()+", genericName="+this.getGenericName()+", classification="+this.getClassification()+", tradeName="+this.getTradeName()+", genericName2="+this.getGenericName2()+", englishName="+this.getEnglishName()+", component="+this.getComponent()+", character="+this.getCharacter()+", applicable="+this.getApplicable()+", specifications="+this.getSpecifications()+", usageDosage="+this.getUsageDosage()+", sideEffects="+this.getSideEffects()+", taboo="+this.getTaboo()+", mattersNeeding="+this.getMattersNeeding()+", drugInteractions="+this.getDrugInteractions()+", drugAction="+this.getDrugAction()+", store="+this.getStore()+", termOfValidity="+this.getTermOfValidity()+", approvalNumber="+this.getApprovalNumber()+", approvalData="+this.getApprovalData()+", manufacturingEnterprise="+this.getManufacturingEnterprise()+", addre="+this.getAddre()+"\)";
    }
}
