package com.taikang.fly.check.mybatis.domain.DictColumn;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DictColumn	// class@00023b from classes.dex
{
    private String dictCode;
    private String dictName;
    private String dictTypeCode;

    public void DictColumn(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictColumn;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DictColumn) {
             b = false;
          }else {
             DictColumn uDictColumn = o;
             if (!uDictColumn.canEqual(this)) {
                b = false;
             }else {
                String dictTypeCode = this.getDictTypeCode();
                String dictTypeCode1 = uDictColumn.getDictTypeCode();
                if (dictTypeCode == null) {
                   if (dictTypeCode1 != null) {
                      b = false;
                   }
                }else if(dictTypeCode.equals(dictTypeCode1)){
                }
                String dictCode = this.getDictCode();
                String dictCode1 = uDictColumn.getDictCode();
                if (dictCode == null) {
                   if (dictCode1 != null) {
                      b = false;
                   }
                }else if(dictCode.equals(dictCode1)){
                }
                String dictName = this.getDictName();
                String dictName1 = uDictColumn.getDictName();
                if (dictName == null) {
                   if (dictName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!dictName.equals(dictName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDictCode(){
       return this.dictCode;
    }
    public String getDictName(){
       return this.dictName;
    }
    public String getDictTypeCode(){
       return this.dictTypeCode;
    }
    public int hashCode(){
       String $dictTypeCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($dictTypeCode = this.getDictTypeCode()) == null)? i: $dictTypeCode.hashCode();
       result = i1 + 59;
       String $dictCode = this.getDictCode();
       int i2 = result * 59;
       i1 = ($dictCode == null)? i: $dictCode.hashCode();
       result = i2 + i1;
       String $dictName = this.getDictName();
       i1 = result * 59;
       if ($dictName != null) {
          i = $dictName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDictCode(String dictCode){
       this.dictCode = dictCode;
    }
    public void setDictName(String dictName){
       this.dictName = dictName;
    }
    public void setDictTypeCode(String dictTypeCode){
       this.dictTypeCode = dictTypeCode;
    }
    public String toString(){
       return "DictColumn\(dictTypeCode="+this.getDictTypeCode()+", dictCode="+this.getDictCode()+", dictName="+this.getDictName()+"\)";
    }
}
