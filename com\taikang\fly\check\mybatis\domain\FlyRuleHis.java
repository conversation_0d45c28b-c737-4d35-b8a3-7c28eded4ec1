package com.taikang.fly.check.mybatis.domain.FlyRuleHis;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class FlyRuleHis implements Serializable	// class@000244 from classes.dex
{
    private Date createdTime;
    private String id;
    private String newSqlName;
    private Date operateTime;
    private String operator;
    private String policyBasis;
    private String ps;
    private String region;
    private String removed;
    private String resultFlag;
    private String ruleName;
    private String ruleType;
    private String sqlName;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleHis(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleHis;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleHis){
          b = false;
       }else {
          FlyRuleHis uFlyRuleHis = o;
          if (!uFlyRuleHis.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uFlyRuleHis.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleHis.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String operator = this.getOperator();
             String operator1 = uFlyRuleHis.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleHis.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uFlyRuleHis.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String resultFlag = this.getResultFlag();
             String resultFlag1 = uFlyRuleHis.getResultFlag();
             if (resultFlag == null) {
                if (resultFlag1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(resultFlag.equals(resultFlag1)){
             }
             String removed = this.getRemoved();
             String removed1 = uFlyRuleHis.getRemoved();
             if (removed == null) {
                if (removed1 != null) {
                   b = false;
                }
             }else if(removed.equals(removed1)){
             }
             Date operateTime = this.getOperateTime();
             Date operateTime1 = uFlyRuleHis.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                label_00d3 :
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             Date createdTime = this.getCreatedTime();
             Date createdTime1 = uFlyRuleHis.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String ps = this.getPs();
             String ps1 = uFlyRuleHis.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_0103 :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uFlyRuleHis.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = uFlyRuleHis.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                label_0133 :
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = uFlyRuleHis.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreatedTime(){
       return this.createdTime;
    }
    public String getId(){
       return this.id;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public Date getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getResultFlag(){
       return this.resultFlag;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $operator = this.getOperator();
       i1 = result * 59;
       i = ($operator == null)? 43: $operator.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $ruleType = this.getRuleType();
       i1 = result * 59;
       i = ($ruleType == null)? 43: $ruleType.hashCode();
       result = i1 + i;
       String resultFlag = this.getResultFlag();
       i1 = result * 59;
       i = (resultFlag == null)? 43: resultFlag.hashCode();
       String removed = this.getRemoved();
       i1 = (i1 + i) * 59;
       i = (removed == null)? 43: removed.hashCode();
       Date operateTime = this.getOperateTime();
       i1 = (i1 + i) * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       Date createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String sqlName = this.getSqlName();
       i1 = (i1 + i) * 59;
       i = (sqlName == null)? 43: sqlName.hashCode();
       String newSqlName = this.getNewSqlName();
       i1 = (i1 + i) * 59;
       i = (newSqlName == null)? 43: newSqlName.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(Date createdTime){
       this.createdTime = createdTime;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setOperateTime(Date operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRemoved(String removed){
       this.removed = removed;
    }
    public void setResultFlag(String resultFlag){
       this.resultFlag = resultFlag;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public String toString(){
       return "FlyRuleHis\(id="+this.getId()+", ruleName="+this.getRuleName()+", operator="+this.getOperator()+", region="+this.getRegion()+", ruleType="+this.getRuleType()+", resultFlag="+this.getResultFlag()+", removed="+this.getRemoved()+", operateTime="+this.getOperateTime()+", createdTime="+this.getCreatedTime()+", ps="+this.getPs()+", sqlName="+this.getSqlName()+", newSqlName="+this.getNewSqlName()+", policyBasis="+this.getPolicyBasis()+"\)";
    }
}
