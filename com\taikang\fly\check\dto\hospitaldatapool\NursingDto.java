package com.taikang.fly.check.dto.hospitaldatapool.NursingDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class NursingDto implements Serializable	// class@000130 from classes.dex
{
    private String dischargeDepartment;
    private String nursing;
    private String patientName;
    private String totalDays;
    private String year;

    public void NursingDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof NursingDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof NursingDto) {
             b = false;
          }else {
             NursingDto nursingDto = o;
             if (!nursingDto.canEqual(this)) {
                b = false;
             }else {
                String year = this.getYear();
                String year1 = nursingDto.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String patientName = this.getPatientName();
                String patientName1 = nursingDto.getPatientName();
                if (patientName == null) {
                   if (patientName1 != null) {
                      b = false;
                   }
                }else if(patientName.equals(patientName1)){
                }
                String dischargeDep = this.getDischargeDepartment();
                String dischargeDep1 = nursingDto.getDischargeDepartment();
                if (dischargeDep == null) {
                   if (dischargeDep1 != null) {
                      b = false;
                   }
                }else if(dischargeDep.equals(dischargeDep1)){
                }
                String totalDays = this.getTotalDays();
                String totalDays1 = nursingDto.getTotalDays();
                if (totalDays == null) {
                   if (totalDays1 != null) {
                      b = false;
                   }
                }else if(totalDays.equals(totalDays1)){
                }
                String nursing = this.getNursing();
                String nursing1 = nursingDto.getNursing();
                if (nursing == null) {
                   if (nursing1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!nursing.equals(nursing1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDischargeDepartment(){
       return this.dischargeDepartment;
    }
    public String getNursing(){
       return this.nursing;
    }
    public String getPatientName(){
       return this.patientName;
    }
    public String getTotalDays(){
       return this.totalDays;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $year;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($year = this.getYear()) == null)? i: $year.hashCode();
       result = i1 + 59;
       String $patientName = this.getPatientName();
       int i2 = result * 59;
       i1 = ($patientName == null)? i: $patientName.hashCode();
       result = i2 + i1;
       String $dischargeDepartment = this.getDischargeDepartment();
       i2 = result * 59;
       i1 = ($dischargeDepartment == null)? i: $dischargeDepartment.hashCode();
       result = i2 + i1;
       String $totalDays = this.getTotalDays();
       i2 = result * 59;
       i1 = ($totalDays == null)? i: $totalDays.hashCode();
       result = i2 + i1;
       String $nursing = this.getNursing();
       i1 = result * 59;
       if ($nursing != null) {
          i = $nursing.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDischargeDepartment(String dischargeDepartment){
       this.dischargeDepartment = dischargeDepartment;
    }
    public void setNursing(String nursing){
       this.nursing = nursing;
    }
    public void setPatientName(String patientName){
       this.patientName = patientName;
    }
    public void setTotalDays(String totalDays){
       this.totalDays = totalDays;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "NursingDto\(year="+this.getYear()+", patientName="+this.getPatientName()+", dischargeDepartment="+this.getDischargeDepartment()+", totalDays="+this.getTotalDays()+", nursing="+this.getNursing()+"\)";
    }
}
