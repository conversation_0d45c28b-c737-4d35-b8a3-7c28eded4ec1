package com.taikang.fly.check.mybatis.domain.ThreeOrder;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ThreeOrder	// class@000270 from classes.dex
{
    private String HospitalProjectCode;
    private String HospitalProjectName;
    private String chargingItemLevel;
    private String costType;
    private String dosageForm;
    private String drugDosageUnit;
    private String hospitalProjectC;
    private String hospitalProjectN;
    private String typesOfCharges;

    public void ThreeOrder(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ThreeOrder;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ThreeOrder){
          b = false;
       }else {
          ThreeOrder threeOrder = o;
          if (!threeOrder.canEqual(this)) {
             b = false;
          }else {
             String hospitalProj = this.getHospitalProjectName();
             String hospitalProj1 = threeOrder.getHospitalProjectName();
             if (hospitalProj == null) {
                if (hospitalProj1 != null) {
                   b = false;
                }
             }else if(hospitalProj.equals(hospitalProj1)){
             }
             String hospitalProj2 = this.getHospitalProjectCode();
             String hospitalProj3 = threeOrder.getHospitalProjectCode();
             if (hospitalProj2 == null) {
                if (hospitalProj3 != null) {
                   b = false;
                }
             }else if(hospitalProj2.equals(hospitalProj3)){
             }
             String typesOfCharg = this.getTypesOfCharges();
             String typesOfCharg1 = threeOrder.getTypesOfCharges();
             if (typesOfCharg == null) {
                if (typesOfCharg1 != null) {
                   b = false;
                }
             }else if(typesOfCharg.equals(typesOfCharg1)){
             }
             String chargingItem = this.getChargingItemLevel();
             String chargingItem1 = threeOrder.getChargingItemLevel();
             if (chargingItem == null) {
                if (chargingItem1 != null) {
                   b = false;
                }
             }else if(chargingItem.equals(chargingItem1)){
             }
             String hospitalProj4 = this.getHospitalProjectC();
             String hospitalProj5 = threeOrder.getHospitalProjectC();
             if (hospitalProj4 == null) {
                if (hospitalProj5 != null) {
                   b = false;
                }
             }else if(hospitalProj4.equals(hospitalProj5)){
             }
             String hospitalProj6 = this.getHospitalProjectN();
             String hospitalProj7 = threeOrder.getHospitalProjectN();
             if (hospitalProj6 == null) {
                if (hospitalProj7 != null) {
                label_009c :
                   b = false;
                }
             }else if(hospitalProj6.equals(hospitalProj7)){
             }
             String costType = this.getCostType();
             String costType1 = threeOrder.getCostType();
             if (costType == null) {
                if (costType1 != null) {
                   b = false;
                }
             }else if(costType.equals(costType1)){
             }
             String dosageForm = this.getDosageForm();
             String dosageForm1 = threeOrder.getDosageForm();
             if (dosageForm == null) {
                if (dosageForm1 != null) {
                label_00ca :
                   b = false;
                }
             }else if(dosageForm.equals(dosageForm1)){
             }
             String drugDosageUn = this.getDrugDosageUnit();
             String drugDosageUn1 = threeOrder.getDrugDosageUnit();
             if (drugDosageUn == null) {
                if (drugDosageUn1 != null) {
                   b = false;
                }
             }else if(drugDosageUn.equals(drugDosageUn1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getChargingItemLevel(){
       return this.chargingItemLevel;
    }
    public String getCostType(){
       return this.costType;
    }
    public String getDosageForm(){
       return this.dosageForm;
    }
    public String getDrugDosageUnit(){
       return this.drugDosageUnit;
    }
    public String getHospitalProjectC(){
       return this.hospitalProjectC;
    }
    public String getHospitalProjectCode(){
       return this.HospitalProjectCode;
    }
    public String getHospitalProjectN(){
       return this.hospitalProjectN;
    }
    public String getHospitalProjectName(){
       return this.HospitalProjectName;
    }
    public String getTypesOfCharges(){
       return this.typesOfCharges;
    }
    public int hashCode(){
       String $HospitalProjectName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($HospitalProjectName = this.getHospitalProjectName()) == null)? i: $HospitalProjectName.hashCode();
       result = i1 + 59;
       String $HospitalProjectCode = this.getHospitalProjectCode();
       int i2 = result * 59;
       i1 = ($HospitalProjectCode == null)? i: $HospitalProjectCode.hashCode();
       result = i2 + i1;
       String $typesOfCharges = this.getTypesOfCharges();
       i2 = result * 59;
       i1 = ($typesOfCharges == null)? i: $typesOfCharges.hashCode();
       result = i2 + i1;
       String $chargingItemLevel = this.getChargingItemLevel();
       i2 = result * 59;
       i1 = ($chargingItemLevel == null)? i: $chargingItemLevel.hashCode();
       result = i2 + i1;
       String $hospitalProjectC = this.getHospitalProjectC();
       i2 = result * 59;
       i1 = ($hospitalProjectC == null)? i: $hospitalProjectC.hashCode();
       result = i2 + i1;
       String hospitalProj = this.getHospitalProjectN();
       i2 = result * 59;
       i1 = (hospitalProj == null)? i: hospitalProj.hashCode();
       String costType = this.getCostType();
       i2 = (i2 + i1) * 59;
       i1 = (costType == null)? i: costType.hashCode();
       String dosageForm = this.getDosageForm();
       i2 = (i2 + i1) * 59;
       i1 = (dosageForm == null)? i: dosageForm.hashCode();
       String drugDosageUn = this.getDrugDosageUnit();
       i1 = (i2 + i1) * 59;
       if (drugDosageUn != null) {
          i = drugDosageUn.hashCode();
       }
       return (i1 + i);
    }
    public void setChargingItemLevel(String chargingItemLevel){
       this.chargingItemLevel = chargingItemLevel;
    }
    public void setCostType(String costType){
       this.costType = costType;
    }
    public void setDosageForm(String dosageForm){
       this.dosageForm = dosageForm;
    }
    public void setDrugDosageUnit(String drugDosageUnit){
       this.drugDosageUnit = drugDosageUnit;
    }
    public void setHospitalProjectC(String hospitalProjectC){
       this.hospitalProjectC = hospitalProjectC;
    }
    public void setHospitalProjectCode(String HospitalProjectCode){
       this.HospitalProjectCode = HospitalProjectCode;
    }
    public void setHospitalProjectN(String hospitalProjectN){
       this.hospitalProjectN = hospitalProjectN;
    }
    public void setHospitalProjectName(String HospitalProjectName){
       this.HospitalProjectName = HospitalProjectName;
    }
    public void setTypesOfCharges(String typesOfCharges){
       this.typesOfCharges = typesOfCharges;
    }
    public String toString(){
       return "ThreeOrder\(HospitalProjectName="+this.getHospitalProjectName()+", HospitalProjectCode="+this.getHospitalProjectCode()+", typesOfCharges="+this.getTypesOfCharges()+", chargingItemLevel="+this.getChargingItemLevel()+", hospitalProjectC="+this.getHospitalProjectC()+", hospitalProjectN="+this.getHospitalProjectN()+", costType="+this.getCostType()+", dosageForm="+this.getDosageForm()+", drugDosageUnit="+this.getDrugDosageUnit()+"\)";
    }
}
