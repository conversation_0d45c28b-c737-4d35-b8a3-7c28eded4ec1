package com.taikang.fly.check.dto.mapstruct.FlyRuleTemplateMapping;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleTemplate;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateRespDto;
import java.util.List;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateEditDto;

public interface abstract FlyRuleTemplateMapping	// class@00015a from classes.dex
{

    FlyRuleTemplate addDtoToDomain(FlyRuleTemplateAddDto p0);
    FlyRuleTemplateRespDto domainToInfoDto(FlyRuleTemplate p0);
    FlyRuleTemplateAddDto domainToaddDto(FlyRuleTemplate p0);
    FlyRuleTemplate dtoToDomain(FlyRuleTemplateRespDto p0);
    List dtoToDomains(List p0);
    FlyRuleTemplate editDtoToDomain(FlyRuleTemplateEditDto p0,FlyRuleTemplate p1);
    List entityToDtos(List p0);
}
