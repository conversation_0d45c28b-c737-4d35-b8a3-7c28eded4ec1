package com.taikang.fly.check.mybatis.dao.UserLoginLogMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.UserLoginLog;

public interface abstract UserLoginLogMapper implements BaseMapper	// class@000224 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    int insertSelective(UserLoginLog p0);
    UserLoginLog selectByPrimaryKey(String p0);
    int updateByPrimaryKey(UserLoginLog p0);
    int updateByPrimaryKeySelective(UserLoginLog p0);
}
