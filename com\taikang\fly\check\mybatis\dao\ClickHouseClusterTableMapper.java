package com.taikang.fly.check.mybatis.dao.ClickHouseClusterTableMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.ClickHouseClusterTable;

public interface abstract ClickHouseClusterTableMapper implements BaseMapper	// class@0001d7 from classes.dex
{

    void deleteData(String p0,List p1);
    void insertClickHouseClusterTable(ClickHouseClusterTable p0);
    ClickHouseClusterTable queryClickHouseClusterTableField(String p0);
    List queryClickHouseClusterTableList(String p0);
    List queryClickHouseClusterTableNameList(String p0);
}
