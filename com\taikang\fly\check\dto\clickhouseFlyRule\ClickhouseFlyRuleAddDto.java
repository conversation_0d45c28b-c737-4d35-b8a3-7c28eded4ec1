package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ClickhouseFlyRuleAddDto implements Serializable	// class@0000be from classes.dex
{
    private String diagnosisType;
    private String ps;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private String ruleType;
    private String sqlName;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseFlyRuleAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseFlyRuleAddDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseFlyRuleAddDto){
          b = false;
       }else {
          ClickhouseFlyRuleAddDto uClickhouseF = o;
          if (!uClickhouseF.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uClickhouseF.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uClickhouseF.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uClickhouseF.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ps = this.getPs();
             String ps1 = uClickhouseF.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uClickhouseF.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uClickhouseF.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uClickhouseF.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                label_00b7 :
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uClickhouseF.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uClickhouseF.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                label_00e3 :
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $sqlName = this.getSqlName();
       int i2 = result * 59;
       i1 = ($sqlName == null)? i: $sqlName.hashCode();
       result = i2 + i1;
       String $ruleType = this.getRuleType();
       i2 = result * 59;
       i1 = ($ruleType == null)? i: $ruleType.hashCode();
       result = i2 + i1;
       String $ps = this.getPs();
       i2 = result * 59;
       i1 = ($ps == null)? i: $ps.hashCode();
       result = i2 + i1;
       String $ruleLevel = this.getRuleLevel();
       i2 = result * 59;
       i1 = ($ruleLevel == null)? i: $ruleLevel.hashCode();
       result = i2 + i1;
       String ruleCategory = this.getRuleCategory1();
       i2 = result * 59;
       i1 = (ruleCategory == null)? i: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i2 = (i2 + i1) * 59;
       i1 = (ruleCategory1 == null)? i: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i2 = (i2 + i1) * 59;
       i1 = (diagnosisTyp == null)? i: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i2 + i1) * 59;
       if (ruleDescribe != null) {
          i = ruleDescribe.hashCode();
       }
       return (i1 + i);
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public String toString(){
       return "ClickhouseFlyRuleAddDto\(ruleName="+this.getRuleName()+", sqlName="+this.getSqlName()+", ruleType="+this.getRuleType()+", ps="+this.getPs()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+"\)";
    }
}
