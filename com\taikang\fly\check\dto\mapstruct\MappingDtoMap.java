package com.taikang.fly.check.dto.mapstruct.MappingDtoMap;
import java.lang.Object;
import java.util.Map;
import java.lang.String;
import java.util.Date;
import com.taikang.fly.check.utils.DateUtils;

public class MappingDtoMap	// class@000163 from classes.dex
{

    public void MappingDtoMap(){
       super();
    }
    public String create_time(Map in){
       return DateUtils.format(in.get("create_time"), "yyyy-MM-dd HH:mm:ss");
    }
    public String creator(Map in){
       return in.get("creator");
    }
    public String role_code(Map in){
       return in.get("role_code");
    }
    public String role_id(Map in){
       return in.get("id");
    }
    public String role_name(Map in){
       return in.get("name");
    }
}
