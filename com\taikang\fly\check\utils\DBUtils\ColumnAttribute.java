package com.taikang.fly.check.utils.DBUtils.ColumnAttribute;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class ColumnAttribute implements Serializable	// class@000331 from classes.dex
{
    private String columnComment;
    private String columnName;
    private String columnType;
    private Integer excelNum;
    private String isNullable;
    private static final long serialVersionUID = 0xcf1f28defc0b8f6c;

    public void ColumnAttribute(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ColumnAttribute;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ColumnAttribute) {
             b = false;
          }else {
             ColumnAttribute uColumnAttri = o;
             if (!uColumnAttri.canEqual(this)) {
                b = false;
             }else {
                String columnName = this.getColumnName();
                String columnName1 = uColumnAttri.getColumnName();
                if (columnName == null) {
                   if (columnName1 != null) {
                      b = false;
                   }
                }else if(columnName.equals(columnName1)){
                }
                String columnType = this.getColumnType();
                String columnType1 = uColumnAttri.getColumnType();
                if (columnType == null) {
                   if (columnType1 != null) {
                      b = false;
                   }
                }else if(columnType.equals(columnType1)){
                }
                String isNullable = this.getIsNullable();
                String isNullable1 = uColumnAttri.getIsNullable();
                if (isNullable == null) {
                   if (isNullable1 != null) {
                      b = false;
                   }
                }else if(isNullable.equals(isNullable1)){
                }
                String columnCommen = this.getColumnComment();
                String columnCommen1 = uColumnAttri.getColumnComment();
                if (columnCommen == null) {
                   if (columnCommen1 != null) {
                      b = false;
                   }
                }else if(columnCommen.equals(columnCommen1)){
                }
                Integer excelNum = this.getExcelNum();
                Integer excelNum1 = uColumnAttri.getExcelNum();
                if (excelNum == null) {
                   if (excelNum1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!excelNum.equals(excelNum1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getColumnComment(){
       return this.columnComment;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getColumnType(){
       return this.columnType;
    }
    public Integer getExcelNum(){
       return this.excelNum;
    }
    public String getIsNullable(){
       return this.isNullable;
    }
    public int hashCode(){
       String $columnName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($columnName = this.getColumnName()) == null)? i: $columnName.hashCode();
       result = i1 + 59;
       String $columnType = this.getColumnType();
       int i2 = result * 59;
       i1 = ($columnType == null)? i: $columnType.hashCode();
       result = i2 + i1;
       String $isNullable = this.getIsNullable();
       i2 = result * 59;
       i1 = ($isNullable == null)? i: $isNullable.hashCode();
       result = i2 + i1;
       String $columnComment = this.getColumnComment();
       i2 = result * 59;
       i1 = ($columnComment == null)? i: $columnComment.hashCode();
       result = i2 + i1;
       Integer $excelNum = this.getExcelNum();
       i1 = result * 59;
       if ($excelNum != null) {
          i = $excelNum.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumnComment(String columnComment){
       this.columnComment = columnComment;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setColumnType(String columnType){
       this.columnType = columnType;
    }
    public void setExcelNum(Integer excelNum){
       this.excelNum = excelNum;
    }
    public void setIsNullable(String isNullable){
       this.isNullable = isNullable;
    }
    public String toString(){
       return "ColumnAttribute\(columnName="+this.getColumnName()+", columnType="+this.getColumnType()+", isNullable="+this.getIsNullable()+", columnComment="+this.getColumnComment()+", excelNum="+this.getExcelNum()+"\)";
    }
}
