package com.taikang.fly.check.mybatis.domain.ClickhouseFlyRuleMulti;
import java.io.Serializable;
import java.lang.Object;
import java.util.Date;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.Class;

public class ClickhouseFlyRuleMulti implements Serializable	// class@000234 from classes.dex
{
    private Date createdTime;
    private String creater;
    private String dataSources;
    private String diagnosisType;
    private Date executionDate;
    private String id;
    private String newSqlName;
    private Date operateTime;
    private String operator;
    private String ps;
    private String redField1;
    private String redField2;
    private String redField3;
    private String region;
    private String removed;
    private String resultFlag;
    private String resultsEnforcement;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleLogic;
    private String ruleName;
    private String ruleParameter;
    private String ruleType;
    private String sourceOfRule;
    private String sqlName;
    private String state;
    private String submitState;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseFlyRuleMulti(){
       super();
    }
    public Date getCreatedTime(){
       return this.createdTime;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDataSources(){
       return this.dataSources;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public Date getExecutionDate(){
       return this.executionDate;
    }
    public String getId(){
       return this.id;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public Date getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRedField1(){
       return this.redField1;
    }
    public String getRedField2(){
       return this.redField2;
    }
    public String getRedField3(){
       return this.redField3;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getResultFlag(){
       return this.resultFlag;
    }
    public String getResultsEnforcement(){
       return this.resultsEnforcement;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleLogic(){
       return this.ruleLogic;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleParameter(){
       return this.ruleParameter;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSourceOfRule(){
       return this.sourceOfRule;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public String getState(){
       return this.state;
    }
    public String getSubmitState(){
       return this.submitState;
    }
    public void setCreatedTime(Date createdTime){
       this.createdTime = createdTime;
    }
    public void setCreater(String creater){
       String str = (creater == null)? null: creater.trim();
       this.creater = str;
       return;
    }
    public void setDataSources(String dataSources){
       String str = (dataSources == null)? null: dataSources.trim();
       this.dataSources = str;
       return;
    }
    public void setDiagnosisType(String diagnosisType){
       String str = (diagnosisType == null)? null: diagnosisType.trim();
       this.diagnosisType = str;
       return;
    }
    public void setExecutionDate(Date executionDate){
       this.executionDate = executionDate;
    }
    public void setId(String id){
       String str = (id == null)? null: id.trim();
       this.id = str;
       return;
    }
    public void setNewSqlName(String newSqlName){
       String str = (newSqlName == null)? null: newSqlName.trim();
       this.newSqlName = str;
       return;
    }
    public void setOperateTime(Date operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       String str = (operator == null)? null: operator.trim();
       this.operator = str;
       return;
    }
    public void setPs(String ps){
       String str = (ps == null)? null: ps.trim();
       this.ps = str;
       return;
    }
    public void setRedField1(String redField1){
       String str = (redField1 == null)? null: redField1.trim();
       this.redField1 = str;
       return;
    }
    public void setRedField2(String redField2){
       String str = (redField2 == null)? null: redField2.trim();
       this.redField2 = str;
       return;
    }
    public void setRedField3(String redField3){
       String str = (redField3 == null)? null: redField3.trim();
       this.redField3 = str;
       return;
    }
    public void setRegion(String region){
       String str = (region == null)? null: region.trim();
       this.region = str;
       return;
    }
    public void setRemoved(String removed){
       String str = (removed == null)? null: removed.trim();
       this.removed = str;
       return;
    }
    public void setResultFlag(String resultFlag){
       String str = (resultFlag == null)? null: resultFlag.trim();
       this.resultFlag = str;
       return;
    }
    public void setResultsEnforcement(String resultsEnforcement){
       String str = (resultsEnforcement == null)? null: resultsEnforcement.trim();
       this.resultsEnforcement = str;
       return;
    }
    public void setRuleCategory1(String ruleCategory1){
       String str = (ruleCategory1 == null)? null: ruleCategory1.trim();
       this.ruleCategory1 = str;
       return;
    }
    public void setRuleCategory2(String ruleCategory2){
       String str = (ruleCategory2 == null)? null: ruleCategory2.trim();
       this.ruleCategory2 = str;
       return;
    }
    public void setRuleDescribe(String ruleDescribe){
       String str = (ruleDescribe == null)? null: ruleDescribe.trim();
       this.ruleDescribe = str;
       return;
    }
    public void setRuleLevel(String ruleLevel){
       String str = (ruleLevel == null)? null: ruleLevel.trim();
       this.ruleLevel = str;
       return;
    }
    public void setRuleLogic(String ruleLogic){
       String str = (ruleLogic == null)? null: ruleLogic.trim();
       this.ruleLogic = str;
       return;
    }
    public void setRuleName(String ruleName){
       String str = (ruleName == null)? null: ruleName.trim();
       this.ruleName = str;
       return;
    }
    public void setRuleParameter(String ruleParameter){
       String str = (ruleParameter == null)? null: ruleParameter.trim();
       this.ruleParameter = str;
       return;
    }
    public void setRuleType(String ruleType){
       String str = (ruleType == null)? null: ruleType.trim();
       this.ruleType = str;
       return;
    }
    public void setSourceOfRule(String sourceOfRule){
       String str = (sourceOfRule == null)? null: sourceOfRule.trim();
       this.sourceOfRule = str;
       return;
    }
    public void setSqlName(String sqlName){
       String str = (sqlName == null)? null: sqlName.trim();
       this.sqlName = str;
       return;
    }
    public void setState(String state){
       String str = (state == null)? null: state.trim();
       this.state = str;
       return;
    }
    public void setSubmitState(String submitState){
       String str = (submitState == null)? null: submitState.trim();
       this.submitState = str;
       return;
    }
    public String toString(){
       StringBuilder sb = "";
       sb = sb+this.getClass().getSimpleName();
       sb = sb+" [";
       StringBuilder sb1 = sb+"Hash = ";
       sb1 = sb1+this.hashCode();
       sb1 = sb+", id=";
       sb1 = sb1+this.id;
       sb1 = sb+", ruleName=";
       sb1 = sb1+this.ruleName;
       sb1 = sb+", operator=";
       sb1 = sb1+this.operator;
       sb1 = sb+", region=";
       sb1 = sb1+this.region;
       sb1 = sb+", ruleType=";
       sb1 = sb1+this.ruleType;
       sb1 = sb+", resultFlag=";
       sb1 = sb1+this.resultFlag;
       sb1 = sb+", removed=";
       sb1 = sb1+this.removed;
       sb1 = sb+", operateTime=";
       sb1 = sb1+this.operateTime;
       sb1 = sb+", createdTime=";
       sb1 = sb1+this.createdTime;
       sb1 = sb+", ps=";
       sb1 = sb1+this.ps;
       sb1 = sb+", state=";
       sb1 = sb1+this.state;
       sb1 = sb+", creater=";
       sb1 = sb1+this.creater;
       sb1 = sb+", resultsEnforcement=";
       sb1 = sb1+this.resultsEnforcement;
       sb1 = sb+", executionDate=";
       sb1 = sb1+this.executionDate;
       sb1 = sb+", dataSources=";
       sb1 = sb1+this.dataSources;
       sb1 = sb+", submitState=";
       sb1 = sb1+this.submitState;
       sb1 = sb+", ruleLevel=";
       sb1 = sb1+this.ruleLevel;
       sb1 = sb+", ruleCategory1=";
       sb1 = sb1+this.ruleCategory1;
       sb1 = sb+", ruleCategory2=";
       sb1 = sb1+this.ruleCategory2;
       sb1 = sb+", diagnosisType=";
       sb1 = sb1+this.diagnosisType;
       sb1 = sb+", ruleDescribe=";
       sb1 = sb1+this.ruleDescribe;
       sb1 = sb+", sourceOfRule=";
       sb1 = sb1+this.sourceOfRule;
       sb1 = sb+", ruleLogic=";
       sb1 = sb1+this.ruleLogic;
       sb1 = sb+", ruleParameter=";
       sb1 = sb1+this.ruleParameter;
       sb1 = sb+", redField1=";
       sb1 = sb1+this.redField1;
       sb1 = sb+", redField2=";
       sb1 = sb1+this.redField2;
       sb1 = sb+", redField3=";
       sb1 = sb1+this.redField3;
       sb1 = sb+", sqlName=";
       sb1 = sb1+this.sqlName;
       sb1 = sb+", newSqlName=";
       sb1 = sb1+this.newSqlName;
       sb1 = sb+", serialVersionUID=";
       sb1 = sb1+1;
       sb = sb+"]";
       return sb;
    }
}
