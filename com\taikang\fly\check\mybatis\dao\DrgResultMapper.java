package com.taikang.fly.check.mybatis.dao.DrgResultMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;

public interface abstract DrgResultMapper implements BaseMapper	// class@0001e9 from classes.dex
{

    List concatRuleNameList(String p0,String p1);
    List getDischargeDepartment();
    List getDrgCodes();
    List getQueryConditionRuleName();
    List selectDiag(String p0);
    List selectOprn(String p0);
}
