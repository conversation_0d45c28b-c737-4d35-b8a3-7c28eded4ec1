package com.taikang.fly.check.dto.UserRoleDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserRoleDto implements Serializable	// class@0000b7 from classes.dex
{
    private String createTime;
    private String creator;
    private String id;
    private String modby;
    private String modifyTime;
    private String roleCode;
    private String signature;
    private String userCode;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a7;

    public void UserRoleDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserRoleDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof UserRoleDto){
          b = false;
       }else {
          UserRoleDto userRoleDto = o;
          if (!userRoleDto.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = userRoleDto.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String userCode = this.getUserCode();
             String userCode1 = userRoleDto.getUserCode();
             if (userCode == null) {
                if (userCode1 != null) {
                   b = false;
                }
             }else if(userCode.equals(userCode1)){
             }
             String roleCode = this.getRoleCode();
             String roleCode1 = userRoleDto.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             String creator = this.getCreator();
             String creator1 = userRoleDto.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = userRoleDto.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = userRoleDto.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_009d :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = userRoleDto.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = userRoleDto.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00c9 :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $userCode = this.getUserCode();
       int i2 = result * 59;
       i1 = ($userCode == null)? i: $userCode.hashCode();
       result = i2 + i1;
       String $roleCode = this.getRoleCode();
       i2 = result * 59;
       i1 = ($roleCode == null)? i: $roleCode.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i1 = (i2 + i1) * 59;
       if (signature != null) {
          i = signature.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserRoleDto\(id="+this.getId()+", userCode="+this.getUserCode()+", roleCode="+this.getRoleCode()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+"\)";
    }
}
