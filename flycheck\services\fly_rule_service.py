"""
飞行检查规则业务服务
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from flycheck.models import FlyRule, FlyRuleHistory
from flycheck.schemas.fly_rule import FlyRuleCreate, FlyRuleUpdate
import json
from datetime import datetime


class FlyRuleService:
    """飞行检查规则服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_rules(
        self, 
        skip: int = 0, 
        limit: int = 20,
        rule_name: Optional[str] = None,
        rule_category1: Optional[str] = None,
        state: Optional[str] = None
    ) -> List[FlyRule]:
        """获取规则列表"""
        query = self.db.query(FlyRule).filter(FlyRule.is_deleted == "0")
        
        # 添加过滤条件
        if rule_name:
            query = query.filter(FlyRule.rule_name.contains(rule_name))
        
        if rule_category1:
            query = query.filter(FlyRule.rule_category1 == rule_category1)
        
        if state:
            query = query.filter(FlyRule.state == state)
        
        return query.offset(skip).limit(limit).all()
    
    def get_rule_by_id(self, rule_id: str) -> Optional[FlyRule]:
        """根据ID获取规则"""
        return (
            self.db.query(FlyRule)
            .filter(and_(FlyRule.id == rule_id, FlyRule.is_deleted == "0"))
            .first()
        )
    
    def create_rule(self, rule_data: FlyRuleCreate) -> FlyRule:
        """创建规则"""
        rule = FlyRule(**rule_data.dict())
        rule.state = "1"  # 默认启用
        rule.submit_state = "draft"  # 默认草稿状态
        
        self.db.add(rule)
        self.db.commit()
        self.db.refresh(rule)
        
        # 创建历史记录
        self._create_history(rule, "create", "创建规则")
        
        return rule
    
    def update_rule(self, rule_id: str, rule_data: FlyRuleUpdate) -> Optional[FlyRule]:
        """更新规则"""
        rule = self.get_rule_by_id(rule_id)
        if not rule:
            return None
        
        # 保存更新前的快照
        old_snapshot = self._create_snapshot(rule)
        
        # 更新字段
        update_data = rule_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(rule, field):
                setattr(rule, field, value)
        
        rule.updated_time = datetime.now()
        
        self.db.commit()
        self.db.refresh(rule)
        
        # 创建历史记录
        self._create_history(rule, "update", "更新规则", old_snapshot)
        
        return rule
    
    def delete_rule(self, rule_id: str) -> bool:
        """删除规则（软删除）"""
        rule = self.get_rule_by_id(rule_id)
        if not rule:
            return False
        
        rule.soft_delete()
        self.db.commit()
        
        # 创建历史记录
        self._create_history(rule, "delete", "删除规则")
        
        return True
    
    def enable_rule(self, rule_id: str) -> bool:
        """启用规则"""
        rule = self.get_rule_by_id(rule_id)
        if not rule:
            return False
        
        rule.state = "1"
        rule.updated_time = datetime.now()
        self.db.commit()
        
        # 创建历史记录
        self._create_history(rule, "enable", "启用规则")
        
        return True
    
    def disable_rule(self, rule_id: str) -> bool:
        """禁用规则"""
        rule = self.get_rule_by_id(rule_id)
        if not rule:
            return False
        
        rule.state = "0"
        rule.updated_time = datetime.now()
        self.db.commit()
        
        # 创建历史记录
        self._create_history(rule, "disable", "禁用规则")
        
        return True
    
    def get_rule_categories(self) -> List[str]:
        """获取规则分类列表"""
        categories = (
            self.db.query(FlyRule.rule_category1)
            .filter(and_(FlyRule.is_deleted == "0", FlyRule.rule_category1.isnot(None)))
            .distinct()
            .all()
        )
        return [cat[0] for cat in categories if cat[0]]
    
    def search_rules(self, keyword: str) -> List[FlyRule]:
        """搜索规则"""
        return (
            self.db.query(FlyRule)
            .filter(
                and_(
                    FlyRule.is_deleted == "0",
                    or_(
                        FlyRule.rule_name.contains(keyword),
                        FlyRule.rule_describe.contains(keyword),
                        FlyRule.policy_basis.contains(keyword)
                    )
                )
            )
            .all()
        )
    
    def _create_snapshot(self, rule: FlyRule) -> dict:
        """创建规则快照"""
        return {
            "rule_name": rule.rule_name,
            "rule_describe": rule.rule_describe,
            "rule_logic": rule.rule_logic,
            "rule_sql": rule.rule_sql,
            "rule_category1": rule.rule_category1,
            "rule_category2": rule.rule_category2,
            "rule_type": rule.rule_type,
            "state": rule.state,
            "policy_basis": rule.policy_basis,
            "problem_description": rule.problem_description,
        }
    
    def _create_history(
        self, 
        rule: FlyRule, 
        change_type: str, 
        change_reason: str,
        old_snapshot: dict = None
    ):
        """创建历史记录"""
        try:
            history = FlyRuleHistory(
                rule_id=rule.id,
                version=f"v{datetime.now().strftime('%Y%m%d%H%M%S')}",
                change_type=change_type,
                change_reason=change_reason,
                rule_snapshot=json.dumps(old_snapshot) if old_snapshot else None
            )
            
            self.db.add(history)
            self.db.commit()
            
        except Exception as e:
            # 历史记录失败不应该影响主要操作
            print(f"创建历史记录失败: {e}")
            self.db.rollback()
