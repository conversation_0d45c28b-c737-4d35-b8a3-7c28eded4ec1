package com.taikang.fly.check.utils.excel.annotation.ExcelField;
import java.lang.annotation.Annotation;
import java.lang.String;
import java.lang.Class;

public interface abstract ExcelField implements Annotation	// class@000356 from classes.dex
{

    int align();
    String dictType();
    Class fieldType();
    int[] groups();
    int sort();
    String title();
    int type();
    String value();
}
