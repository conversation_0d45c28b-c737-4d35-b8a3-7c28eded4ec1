package com.taikang.fly.check.dto.mapstruct.UserModuleMapping;
import com.taikang.fly.check.mybatis.domain.Module;
import com.taikang.fly.check.dto.module.ModuleIndexResDto;
import java.util.List;
import com.taikang.fly.check.dto.usermodule.UserModuleRespAddDto;
import com.taikang.fly.check.mybatis.domain.UserModule;
import com.taikang.fly.check.dto.usermodule.UserModuleRespEditDto;

public interface abstract UserModuleMapping	// class@00017f from classes.dex
{

    ModuleIndexResDto moduleToModuleIndexResDto(Module p0);
    List moduleToModuleIndexResDto(List p0);
    UserModule userModuleAddDtoToUserModule(UserModuleRespAddDto p0);
    UserModule userModuleEditDtoToUserModule(UserModuleRespEditDto p0);
}
