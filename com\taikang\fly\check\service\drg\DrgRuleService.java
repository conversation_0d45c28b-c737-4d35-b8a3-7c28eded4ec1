package com.taikang.fly.check.service.drg.DrgRuleService;
import com.taikang.fly.check.vo.drg.DrgCheckTaskDateVo;
import com.taikang.fly.check.mybatis.domain.drg.DrgCheckTasks;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.dto.drg.DrgResultQueryDTO;
import java.lang.Integer;
import com.taikang.fly.check.vo.drg.DrgQueryFeeVarianceOfDepartmentVo;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.vo.drg.DrgSerachVo;
import com.taikang.fly.check.dto.drg.DrgProfitAndLossDownloadDto;
import java.util.List;
import com.taikang.fly.check.vo.drg.DrgCheckAuditTaskResultVo;
import com.taikang.fly.check.dto.drg.DrgGroupOrDepartmentInfoDto;
import com.taikang.fly.check.dto.drg.DepartmentAndDrgGroupNamesDto;
import com.taikang.fly.check.vo.drg.DrgFeeOfDepartmentVo;
import com.taikang.fly.check.dto.drg.DrgProfitAndLossDto;

public interface abstract DrgRuleService	// class@000313 from classes.dex
{

    DrgCheckTasks checkAuditTasks(DrgCheckTaskDateVo p0);
    boolean delDrgResult();
    void downInspectionResults(HttpServletResponse p0,DrgResultQueryDTO p1);
    NativePage drgFeeVarianceOfDepartment(Integer p0,Integer p1,DrgQueryFeeVarianceOfDepartmentVo p2);
    void drgRegroup();
    NativePage drgRegroupCompare(Integer p0,Integer p1);
    void export(DrgSerachVo p0,HttpServletResponse p1);
    void exportDrgDateOfDepartment(HttpServletResponse p0);
    void exportDrgDateOfDepartmentNum(HttpServletResponse p0);
    void exportDrgDateOfDepartmentRW(HttpServletResponse p0);
    void exportDrgFeeVarianceOfDepartment(HttpServletResponse p0,DrgQueryFeeVarianceOfDepartmentVo p1);
    void exportProfitAndLoss(DrgProfitAndLossDownloadDto p0,HttpServletResponse p1);
    List getAllDepartmentNames();
    List getAllDrgGroupsNames();
    NativePage getCheckAuditTaskResults(Integer p0,Integer p1,DrgCheckAuditTaskResultVo p2);
    NativePage getCheckAuditTasks(Integer p0,Integer p1);
    NativePage getDRGProportion(Integer p0,Integer p1);
    NativePage getDepartmentProfitOrLossByDrgGroup(Integer p0,Integer p1,DrgGroupOrDepartmentInfoDto p2);
    List getDischargeDepartment();
    List getDrgCodes();
    NativePage getDrgDateOfDepartment(Integer p0,Integer p1);
    NativePage getDrgDateOfDepartmentNum(Integer p0,Integer p1);
    NativePage getDrgDateOfDepartmentRW(Integer p0,Integer p1);
    NativePage getDrgDepartmentsProfitAndLoss(Integer p0,Integer p1,DepartmentAndDrgGroupNamesDto p2);
    NativePage getDrgFeeOfDepartmentDetail(Integer p0,Integer p1,DrgFeeOfDepartmentVo p2);
    NativePage getDrgGroupProfitOrLossByDepartment(Integer p0,Integer p1,DrgGroupOrDepartmentInfoDto p2);
    NativePage getDrgGroupsList(Integer p0,Integer p1);
    NativePage getDrgGroupsProfitAndLoss(Integer p0,Integer p1,DepartmentAndDrgGroupNamesDto p2);
    List getDrgYear();
    void getGroupsData();
    NativePage getLossDepartmentOrDrg(Integer p0,Integer p1,DrgProfitAndLossDto p2);
    NativePage getProfitDepartmentOrDrg(Integer p0,Integer p1,DrgProfitAndLossDto p2);
    List getQueryConditionRuleName();
    List getRuleListResult();
    List getYear();
    void groupParam();
    NativePage queryList(Integer p0,Integer p1,DrgResultQueryDTO p2);
}
