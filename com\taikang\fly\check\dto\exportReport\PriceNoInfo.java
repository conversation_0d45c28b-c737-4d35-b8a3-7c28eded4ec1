package com.taikang.fly.check.dto.exportReport.PriceNoInfo;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Double;
import java.lang.StringBuilder;

public class PriceNoInfo implements Serializable	// class@0000f2 from classes.dex
{
    private String hospitalProjectName;
    private double price;
    private String specs;
    private String type;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void PriceNoInfo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PriceNoInfo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof PriceNoInfo) {
             b = false;
          }else {
             PriceNoInfo priceNoInfo = o;
             if (!priceNoInfo.canEqual(this)) {
                b = false;
             }else {
                String type = this.getType();
                String type1 = priceNoInfo.getType();
                if (type == null) {
                   if (type1 != null) {
                      b = false;
                   }
                }else if(type.equals(type1)){
                }
                String specs = this.getSpecs();
                String specs1 = priceNoInfo.getSpecs();
                if (specs == null) {
                   if (specs1 != null) {
                      b = false;
                   }
                }else if(specs.equals(specs1)){
                }
                String hospitalProj = this.getHospitalProjectName();
                String hospitalProj1 = priceNoInfo.getHospitalProjectName();
                if (hospitalProj == null) {
                   if (hospitalProj1 != null) {
                      b = false;
                   }
                }else if(hospitalProj.equals(hospitalProj1)){
                }
                if (Double.compare(this.getPrice(), priceNoInfo.getPrice())) {
                   b = false;
                }
             }
          }
       }
       return b;
    }
    public String getHospitalProjectName(){
       return this.hospitalProjectName;
    }
    public double getPrice(){
       return this.price;
    }
    public String getSpecs(){
       return this.specs;
    }
    public String getType(){
       return this.type;
    }
    public int hashCode(){
       String $type;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($type = this.getType()) == null)? i: $type.hashCode();
       result = i1 + 59;
       String $specs = this.getSpecs();
       int i2 = result * 59;
       i1 = ($specs == null)? i: $specs.hashCode();
       result = i2 + i1;
       String $hospitalProjectName = this.getHospitalProjectName();
       i1 = result * 59;
       if ($hospitalProjectName != null) {
          i = $hospitalProjectName.hashCode();
       }
       result = i1 + i;
       long l = Double.doubleToLongBits(this.getPrice());
       return ((result * 59) + (int)((l >> 32) ^ l));
    }
    public void setHospitalProjectName(String hospitalProjectName){
       this.hospitalProjectName = hospitalProjectName;
    }
    public void setPrice(double price){
       this.price = price;
    }
    public void setSpecs(String specs){
       this.specs = specs;
    }
    public void setType(String type){
       this.type = type;
    }
    public String toString(){
       return "PriceNoInfo\(type="+this.getType()+", specs="+this.getSpecs()+", hospitalProjectName="+this.getHospitalProjectName()+", price="+this.getPrice()+"\)";
    }
}
