package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseMultiFlyRuleTemplateRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDate;
import java.lang.StringBuilder;

public class ClickhouseMultiFlyRuleTemplateRespDto implements Serializable	// class@0000cb from classes.dex
{
    private LocalDate createdTime;
    private String creater;
    private String diagnosisType;
    private String id;
    private LocalDate operateTime;
    private String operator;
    private String parameter;
    private String ps;
    private String removed;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private String sqlExample;
    private String sqlTemplate;
    private String templateName;
    private String templateType;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseMultiFlyRuleTemplateRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseMultiFlyRuleTemplateRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseMultiFlyRuleTemplateRespDto){
          b = false;
       }else {
          ClickhouseMultiFlyRuleTemplateRespDto uClickhouseM = o;
          if (!uClickhouseM.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uClickhouseM.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uClickhouseM.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uClickhouseM.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uClickhouseM.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uClickhouseM.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uClickhouseM.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ps = this.getPs();
             String ps1 = uClickhouseM.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String sqlTemplate = this.getSqlTemplate();
             String sqlTemplate1 = uClickhouseM.getSqlTemplate();
             if (sqlTemplate == null) {
                if (sqlTemplate1 != null) {
                label_00d5 :
                   b = false;
                }
             }else if(sqlTemplate.equals(sqlTemplate1)){
             }
             String sqlExample = this.getSqlExample();
             String sqlExample1 = uClickhouseM.getSqlExample();
             if (sqlExample == null) {
                if (sqlExample1 != null) {
                   b = false;
                }
             }else if(sqlExample.equals(sqlExample1)){
             }
             String parameter = this.getParameter();
             String parameter1 = uClickhouseM.getParameter();
             if (parameter == null) {
                if (parameter1 != null) {
                label_0109 :
                   b = false;
                }
             }else if(parameter.equals(parameter1)){
             }
             String creater = this.getCreater();
             String creater1 = uClickhouseM.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String operator = this.getOperator();
             String operator1 = uClickhouseM.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                label_0139 :
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             LocalDate operateTime = this.getOperateTime();
             LocalDate operateTime1 = uClickhouseM.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             LocalDate createdTime = this.getCreatedTime();
             LocalDate createdTime1 = uClickhouseM.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String id = this.getId();
             String id1 = uClickhouseM.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String removed = this.getRemoved();
             String removed1 = uClickhouseM.getRemoved();
             if (removed == null) {
                if (removed1 != null) {
                label_0199 :
                   b = false;
                }
             }else if(removed.equals(removed1)){
             }
             String templateType = this.getTemplateType();
             String templateType1 = uClickhouseM.getTemplateType();
             if (templateType == null) {
                if (templateType1 != null) {
                   b = false;
                }
             }else if(templateType.equals(templateType1)){
             }
             String templateName = this.getTemplateName();
             String templateName1 = uClickhouseM.getTemplateName();
             if (templateName == null) {
                if (templateName1 != null) {
                label_01cb :
                   b = false;
                }
             }else if(templateName.equals(templateName1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDate getCreatedTime(){
       return this.createdTime;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public LocalDate getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getParameter(){
       return this.parameter;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getSqlExample(){
       return this.sqlExample;
    }
    public String getSqlTemplate(){
       return this.sqlTemplate;
    }
    public String getTemplateName(){
       return this.templateName;
    }
    public String getTemplateType(){
       return this.templateType;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $ruleLevel = this.getRuleLevel();
       int i1 = result * 59;
       i = ($ruleLevel == null)? 43: $ruleLevel.hashCode();
       result = i1 + i;
       String $ruleCategory1 = this.getRuleCategory1();
       i1 = result * 59;
       i = ($ruleCategory1 == null)? 43: $ruleCategory1.hashCode();
       result = i1 + i;
       String $ruleCategory2 = this.getRuleCategory2();
       i1 = result * 59;
       i = ($ruleCategory2 == null)? 43: $ruleCategory2.hashCode();
       result = i1 + i;
       String $diagnosisType = this.getDiagnosisType();
       i1 = result * 59;
       i = ($diagnosisType == null)? 43: $diagnosisType.hashCode();
       result = i1 + i;
       String ruleDescribe = this.getRuleDescribe();
       i1 = result * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String sqlTemplate = this.getSqlTemplate();
       i1 = (i1 + i) * 59;
       i = (sqlTemplate == null)? 43: sqlTemplate.hashCode();
       String sqlExample = this.getSqlExample();
       i1 = (i1 + i) * 59;
       i = (sqlExample == null)? 43: sqlExample.hashCode();
       String parameter = this.getParameter();
       i1 = (i1 + i) * 59;
       i = (parameter == null)? 43: parameter.hashCode();
       String creater = this.getCreater();
       i1 = (i1 + i) * 59;
       i = (creater == null)? 43: creater.hashCode();
       String operator = this.getOperator();
       i1 = (i1 + i) * 59;
       i = (operator == null)? 43: operator.hashCode();
       LocalDate operateTime = this.getOperateTime();
       i1 = (i1 + i) * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       LocalDate createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String id = this.getId();
       i1 = (i1 + i) * 59;
       i = (id == null)? 43: id.hashCode();
       String removed = this.getRemoved();
       i1 = (i1 + i) * 59;
       i = (removed == null)? 43: removed.hashCode();
       String templateType = this.getTemplateType();
       i1 = (i1 + i) * 59;
       i = (templateType == null)? 43: templateType.hashCode();
       String templateName = this.getTemplateName();
       i1 = (i1 + i) * 59;
       i = (templateName == null)? 43: templateName.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(LocalDate createdTime){
       this.createdTime = createdTime;
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setOperateTime(LocalDate operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setParameter(String parameter){
       this.parameter = parameter;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRemoved(String removed){
       this.removed = removed;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setSqlExample(String sqlExample){
       this.sqlExample = sqlExample;
    }
    public void setSqlTemplate(String sqlTemplate){
       this.sqlTemplate = sqlTemplate;
    }
    public void setTemplateName(String templateName){
       this.templateName = templateName;
    }
    public void setTemplateType(String templateType){
       this.templateType = templateType;
    }
    public String toString(){
       return "ClickhouseMultiFlyRuleTemplateRespDto\(ruleName="+this.getRuleName()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ps="+this.getPs()+", sqlTemplate="+this.getSqlTemplate()+", sqlExample="+this.getSqlExample()+", parameter="+this.getParameter()+", creater="+this.getCreater()+", operator="+this.getOperator()+", operateTime="+this.getOperateTime()+", createdTime="+this.getCreatedTime()+", id="+this.getId()+", removed="+this.getRemoved()+", templateType="+this.getTemplateType()+", templateName="+this.getTemplateName()+"\)";
    }
}
