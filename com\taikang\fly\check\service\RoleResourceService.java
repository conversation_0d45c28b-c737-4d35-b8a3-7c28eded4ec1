package com.taikang.fly.check.service.RoleResourceService;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taikang.fly.check.mybatis.dao.RoleResourceMapper;
import java.util.List;
import com.taikang.fly.check.dto.system.role.RoleResourceManageDto;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import com.taikang.fly.check.mybatis.domain.RoleResource;
import java.util.Date;
import com.taikang.fly.check.utils.SequenceGenerator;

public class RoleResourceService	// class@0002fb from classes.dex
{
    private RoleResourceMapper roleResourceMapper;

    public void RoleResourceService(){
       super();
    }
    private Integer deleteByRoleCode(String roleCode){
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("role_code", roleCode);
       return Integer.valueOf(this.roleResourceMapper.delete(queryWrapper));
    }
    public List getRoleResourceList(String roleCode){
       return this.roleResourceMapper.getResourceIdsByRoleCode(roleCode);
    }
    public void roleResourceManage(RoleResourceManageDto roleResourceManageDto){
       this.deleteByRoleCode(roleResourceManageDto.getRoleCode());
       String resources = (roleResourceManageDto.getResourceIds() == null)? "": roleResourceManageDto.getResourceIds();
       if (!StringUtils.isBlank(resources)) {
          String[] stringArray = resources.split(",");
          RoleResource roleResource = new RoleResource();
          roleResource.setCreateTime(new Date());
          roleResource.setCreator("sysadmin");
          roleResource.setRoleCode(roleResourceManageDto.getRoleCode());
          roleResource.setModby("sysadmin");
          roleResource.setModifyTime(new Date());
          roleResource.setSignature("1");
          int len = stringArray.length;
          for (int i = 0; i < len; i = i + 1) {
             roleResource.setId(SequenceGenerator.getId());
             roleResource.setResourceCode(stringArray[i]);
             this.roleResourceMapper.insert(roleResource);
          }
       }
       return;
    }
}
