package com.taikang.fly.check.dto.csventry.FieldTypeDict;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;
import java.lang.StringBuilder;

public class FieldTypeDict	// class@0000da from classes.dex
{
    private String fieldName;
    private Integer fieldNum;

    public void FieldTypeDict(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FieldTypeDict;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FieldTypeDict) {
             b = false;
          }else {
             FieldTypeDict uFieldTypeDi = o;
             if (!uFieldTypeDi.canEqual(this)) {
                b = false;
             }else {
                Integer fieldNum = this.getFieldNum();
                Integer fieldNum1 = uFieldTypeDi.getFieldNum();
                if (fieldNum == null) {
                   if (fieldNum1 != null) {
                      b = false;
                   }
                }else if(fieldNum.equals(fieldNum1)){
                }
                String fieldName = this.getFieldName();
                String fieldName1 = uFieldTypeDi.getFieldName();
                if (fieldName == null) {
                   if (fieldName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!fieldName.equals(fieldName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFieldName(){
       return this.fieldName;
    }
    public Integer getFieldNum(){
       return this.fieldNum;
    }
    public int hashCode(){
       Integer $fieldNum;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($fieldNum = this.getFieldNum()) == null)? i: $fieldNum.hashCode();
       result = i1 + 59;
       String $fieldName = this.getFieldName();
       i1 = result * 59;
       if ($fieldName != null) {
          i = $fieldName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFieldName(String fieldName){
       this.fieldName = fieldName;
    }
    public void setFieldNum(Integer fieldNum){
       this.fieldNum = fieldNum;
    }
    public String toString(){
       return "FieldTypeDict\(fieldNum="+this.getFieldNum()+", fieldName="+this.getFieldName()+"\)";
    }
}
