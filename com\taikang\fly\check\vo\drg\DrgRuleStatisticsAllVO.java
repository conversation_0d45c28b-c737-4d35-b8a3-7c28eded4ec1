package com.taikang.fly.check.vo.drg.DrgRuleStatisticsAllVO;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class DrgRuleStatisticsAllVO	// class@000377 from classes.dex
{
    private String drgRuleCount;
    private List drgRuleStatisticsVOList;
    private String setlIdAllCount;

    public void DrgRuleStatisticsAllVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgRuleStatisticsAllVO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgRuleStatisticsAllVO) {
             b = false;
          }else {
             DrgRuleStatisticsAllVO uDrgRuleStat = o;
             if (!uDrgRuleStat.canEqual(this)) {
                b = false;
             }else {
                String drgRuleCount = this.getDrgRuleCount();
                String drgRuleCount1 = uDrgRuleStat.getDrgRuleCount();
                if (drgRuleCount == null) {
                   if (drgRuleCount1 != null) {
                      b = false;
                   }
                }else if(drgRuleCount.equals(drgRuleCount1)){
                }
                String setlIdAllCou = this.getSetlIdAllCount();
                String setlIdAllCou1 = uDrgRuleStat.getSetlIdAllCount();
                if (setlIdAllCou == null) {
                   if (setlIdAllCou1 != null) {
                      b = false;
                   }
                }else if(setlIdAllCou.equals(setlIdAllCou1)){
                }
                List drgRuleStati = this.getDrgRuleStatisticsVOList();
                List drgRuleStati1 = uDrgRuleStat.getDrgRuleStatisticsVOList();
                if (drgRuleStati == null) {
                   if (drgRuleStati1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!drgRuleStati.equals(drgRuleStati1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrgRuleCount(){
       return this.drgRuleCount;
    }
    public List getDrgRuleStatisticsVOList(){
       return this.drgRuleStatisticsVOList;
    }
    public String getSetlIdAllCount(){
       return this.setlIdAllCount;
    }
    public int hashCode(){
       String $drgRuleCount;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgRuleCount = this.getDrgRuleCount()) == null)? i: $drgRuleCount.hashCode();
       result = i1 + 59;
       String $setlIdAllCount = this.getSetlIdAllCount();
       int i2 = result * 59;
       i1 = ($setlIdAllCount == null)? i: $setlIdAllCount.hashCode();
       result = i2 + i1;
       List $drgRuleStatisticsVOList = this.getDrgRuleStatisticsVOList();
       i1 = result * 59;
       if ($drgRuleStatisticsVOList != null) {
          i = $drgRuleStatisticsVOList.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrgRuleCount(String drgRuleCount){
       this.drgRuleCount = drgRuleCount;
    }
    public void setDrgRuleStatisticsVOList(List drgRuleStatisticsVOList){
       this.drgRuleStatisticsVOList = drgRuleStatisticsVOList;
    }
    public void setSetlIdAllCount(String setlIdAllCount){
       this.setlIdAllCount = setlIdAllCount;
    }
    public String toString(){
       return "DrgRuleStatisticsAllVO\(drgRuleCount="+this.getDrgRuleCount()+", setlIdAllCount="+this.getSetlIdAllCount()+", drgRuleStatisticsVOList="+this.getDrgRuleStatisticsVOList()+"\)";
    }
}
