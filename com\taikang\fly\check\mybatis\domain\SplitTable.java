package com.taikang.fly.check.mybatis.domain.SplitTable;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class SplitTable implements Serializable	// class@00026b from classes.dex
{
    private String area;
    private LocalDateTime createDate;
    private String creator;
    private String dataSource;
    private Integer delectStatus;
    private String hospitalId;
    private String hospitalName;
    private String id;
    private String spare1;
    private String spare2;
    private String spare3;
    private String spare4;
    private String spare5;
    private String splitInfo;
    private Integer splitStatus;
    private LocalDateTime updateDate;
    private String updateUser;
    private static final long serialVersionUID = 0x1;

    public void SplitTable(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof SplitTable;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof SplitTable){
          b = false;
       }else {
          SplitTable splitTable = o;
          if (!splitTable.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = splitTable.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String hospitalId = this.getHospitalId();
             String hospitalId1 = splitTable.getHospitalId();
             if (hospitalId == null) {
                if (hospitalId1 != null) {
                   b = false;
                }
             }else if(hospitalId.equals(hospitalId1)){
             }
             String dataSource = this.getDataSource();
             String dataSource1 = splitTable.getDataSource();
             if (dataSource == null) {
                if (dataSource1 != null) {
                   b = false;
                }
             }else if(dataSource.equals(dataSource1)){
             }
             String hospitalName = this.getHospitalName();
             String hospitalName1 = splitTable.getHospitalName();
             if (hospitalName == null) {
                if (hospitalName1 != null) {
                   b = false;
                }
             }else if(hospitalName.equals(hospitalName1)){
             }
             Integer splitStatus = this.getSplitStatus();
             Integer splitStatus1 = splitTable.getSplitStatus();
             if (splitStatus == null) {
                if (splitStatus1 != null) {
                   b = false;
                }
             }else if(splitStatus.equals(splitStatus1)){
             }
             String splitInfo = this.getSplitInfo();
             String splitInfo1 = splitTable.getSplitInfo();
             if (splitInfo == null) {
                if (splitInfo1 != null) {
                label_00a5 :
                   b = false;
                }
             }else if(splitInfo.equals(splitInfo1)){
             }
             String area = this.getArea();
             String area1 = splitTable.getArea();
             if (area == null) {
                if (area1 != null) {
                   b = false;
                }
             }else if(area.equals(area1)){
             }
             Integer delectStatus = this.getDelectStatus();
             Integer delectStatus1 = splitTable.getDelectStatus();
             if (delectStatus == null) {
                if (delectStatus1 != null) {
                label_00d7 :
                   b = false;
                }
             }else if(delectStatus.equals(delectStatus1)){
             }
             String creator = this.getCreator();
             String creator1 = splitTable.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createDate = this.getCreateDate();
             LocalDateTime createDate1 = splitTable.getCreateDate();
             if (createDate == null) {
                if (createDate1 != null) {
                   b = false;
                }
             }else if(createDate.equals(createDate1)){
             }
             String updateUser = this.getUpdateUser();
             String updateUser1 = splitTable.getUpdateUser();
             if (updateUser == null) {
                if (updateUser1 != null) {
                   b = false;
                }
             }else if(updateUser.equals(updateUser1)){
             }
             LocalDateTime updateDate = this.getUpdateDate();
             LocalDateTime updateDate1 = splitTable.getUpdateDate();
             if (updateDate == null) {
                if (updateDate1 != null) {
                   b = false;
                }
             }else if(updateDate.equals(updateDate1)){
             }
             String spare1 = this.getSpare1();
             String spare11 = splitTable.getSpare1();
             if (spare1 == null) {
                if (spare11 != null) {
                label_0153 :
                   b = false;
                }
             }else if(spare1.equals(spare11)){
             }
             String spare2 = this.getSpare2();
             String spare21 = splitTable.getSpare2();
             if (spare2 == null) {
                if (spare21 != null) {
                   b = false;
                }
             }else if(spare2.equals(spare21)){
             }
             String spare3 = this.getSpare3();
             String spare31 = splitTable.getSpare3();
             if (spare3 == null) {
                if (spare31 != null) {
                label_0183 :
                   b = false;
                }
             }else if(spare3.equals(spare31)){
             }
             String spare4 = this.getSpare4();
             String spare41 = splitTable.getSpare4();
             if (spare4 == null) {
                if (spare41 != null) {
                   b = false;
                }
             }else if(spare4.equals(spare41)){
             }
             String spare5 = this.getSpare5();
             String spare51 = splitTable.getSpare5();
             if (spare5 == null) {
                if (spare51 != null) {
                label_01b3 :
                   b = false;
                }
             }else if(spare5.equals(spare51)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getArea(){
       return this.area;
    }
    public LocalDateTime getCreateDate(){
       return this.createDate;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDataSource(){
       return this.dataSource;
    }
    public Integer getDelectStatus(){
       return this.delectStatus;
    }
    public String getHospitalId(){
       return this.hospitalId;
    }
    public String getHospitalName(){
       return this.hospitalName;
    }
    public String getId(){
       return this.id;
    }
    public String getSpare1(){
       return this.spare1;
    }
    public String getSpare2(){
       return this.spare2;
    }
    public String getSpare3(){
       return this.spare3;
    }
    public String getSpare4(){
       return this.spare4;
    }
    public String getSpare5(){
       return this.spare5;
    }
    public String getSplitInfo(){
       return this.splitInfo;
    }
    public Integer getSplitStatus(){
       return this.splitStatus;
    }
    public LocalDateTime getUpdateDate(){
       return this.updateDate;
    }
    public String getUpdateUser(){
       return this.updateUser;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $hospitalId = this.getHospitalId();
       int i1 = result * 59;
       i = ($hospitalId == null)? 43: $hospitalId.hashCode();
       result = i1 + i;
       String $dataSource = this.getDataSource();
       i1 = result * 59;
       i = ($dataSource == null)? 43: $dataSource.hashCode();
       result = i1 + i;
       String $hospitalName = this.getHospitalName();
       i1 = result * 59;
       i = ($hospitalName == null)? 43: $hospitalName.hashCode();
       result = i1 + i;
       Integer $splitStatus = this.getSplitStatus();
       i1 = result * 59;
       i = ($splitStatus == null)? 43: $splitStatus.hashCode();
       result = i1 + i;
       String splitInfo = this.getSplitInfo();
       i1 = result * 59;
       i = (splitInfo == null)? 43: splitInfo.hashCode();
       String area = this.getArea();
       i1 = (i1 + i) * 59;
       i = (area == null)? 43: area.hashCode();
       Integer delectStatus = this.getDelectStatus();
       i1 = (i1 + i) * 59;
       i = (delectStatus == null)? 43: delectStatus.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       LocalDateTime createDate = this.getCreateDate();
       i1 = (i1 + i) * 59;
       i = (createDate == null)? 43: createDate.hashCode();
       String updateUser = this.getUpdateUser();
       i1 = (i1 + i) * 59;
       i = (updateUser == null)? 43: updateUser.hashCode();
       LocalDateTime updateDate = this.getUpdateDate();
       i1 = (i1 + i) * 59;
       i = (updateDate == null)? 43: updateDate.hashCode();
       String spare1 = this.getSpare1();
       i1 = (i1 + i) * 59;
       i = (spare1 == null)? 43: spare1.hashCode();
       String spare2 = this.getSpare2();
       i1 = (i1 + i) * 59;
       i = (spare2 == null)? 43: spare2.hashCode();
       String spare3 = this.getSpare3();
       i1 = (i1 + i) * 59;
       i = (spare3 == null)? 43: spare3.hashCode();
       String spare4 = this.getSpare4();
       i1 = (i1 + i) * 59;
       i = (spare4 == null)? 43: spare4.hashCode();
       String spare5 = this.getSpare5();
       i1 = (i1 + i) * 59;
       i = (spare5 == null)? 43: spare5.hashCode();
       return (i1 + i);
    }
    public SplitTable setArea(String area){
       this.area = area;
       return this;
    }
    public SplitTable setCreateDate(LocalDateTime createDate){
       this.createDate = createDate;
       return this;
    }
    public SplitTable setCreator(String creator){
       this.creator = creator;
       return this;
    }
    public SplitTable setDataSource(String dataSource){
       this.dataSource = dataSource;
       return this;
    }
    public SplitTable setDelectStatus(Integer delectStatus){
       this.delectStatus = delectStatus;
       return this;
    }
    public SplitTable setHospitalId(String hospitalId){
       this.hospitalId = hospitalId;
       return this;
    }
    public SplitTable setHospitalName(String hospitalName){
       this.hospitalName = hospitalName;
       return this;
    }
    public SplitTable setId(String id){
       this.id = id;
       return this;
    }
    public SplitTable setSpare1(String spare1){
       this.spare1 = spare1;
       return this;
    }
    public SplitTable setSpare2(String spare2){
       this.spare2 = spare2;
       return this;
    }
    public SplitTable setSpare3(String spare3){
       this.spare3 = spare3;
       return this;
    }
    public SplitTable setSpare4(String spare4){
       this.spare4 = spare4;
       return this;
    }
    public SplitTable setSpare5(String spare5){
       this.spare5 = spare5;
       return this;
    }
    public SplitTable setSplitInfo(String splitInfo){
       this.splitInfo = splitInfo;
       return this;
    }
    public SplitTable setSplitStatus(Integer splitStatus){
       this.splitStatus = splitStatus;
       return this;
    }
    public SplitTable setUpdateDate(LocalDateTime updateDate){
       this.updateDate = updateDate;
       return this;
    }
    public SplitTable setUpdateUser(String updateUser){
       this.updateUser = updateUser;
       return this;
    }
    public String toString(){
       return "SplitTable\(id="+this.getId()+", hospitalId="+this.getHospitalId()+", dataSource="+this.getDataSource()+", hospitalName="+this.getHospitalName()+", splitStatus="+this.getSplitStatus()+", splitInfo="+this.getSplitInfo()+", area="+this.getArea()+", delectStatus="+this.getDelectStatus()+", creator="+this.getCreator()+", createDate="+this.getCreateDate()+", updateUser="+this.getUpdateUser()+", updateDate="+this.getUpdateDate()+", spare1="+this.getSpare1()+", spare2="+this.getSpare2()+", spare3="+this.getSpare3()+", spare4="+this.getSpare4()+", spare5="+this.getSpare5()+"\)";
    }
}
