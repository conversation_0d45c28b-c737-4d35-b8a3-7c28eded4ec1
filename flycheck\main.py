"""
FastAPI应用主入口
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from loguru import logger
import os

from flycheck.config import get_settings
from flycheck.database import init_db
from flycheck.api import api_router

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("FlyCheck Python 启动中...")
    
    # 初始化数据库
    init_db()
    
    # 创建必要的目录
    os.makedirs("./uploads", exist_ok=True)
    os.makedirs("./results", exist_ok=True)
    os.makedirs("./logs", exist_ok=True)
    
    logger.info("FlyCheck Python 启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("FlyCheck Python 关闭中...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="医保飞行检查系统 - Python版本",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
if os.path.exists("./static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 注册API路由
app.include_router(api_router, prefix="/api/v1")


@app.get("/", response_class=HTMLResponse)
async def root():
    """首页"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>FlyCheck Python</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { text-align: center; margin-bottom: 40px; }
            .links { text-align: center; }
            .links a { margin: 0 20px; text-decoration: none; color: #007bff; }
            .links a:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚁 FlyCheck Python</h1>
            <p>医保飞行检查系统 - Python版本</p>
        </div>
        <div class="links">
            <a href="/docs" target="_blank">📚 API文档 (Swagger)</a>
            <a href="/redoc" target="_blank">📖 API文档 (ReDoc)</a>
        </div>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "timestamp": "2024-01-01T00:00:00Z"
    }


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = logger.info(f"请求开始: {request.method} {request.url}")
    
    response = await call_next(request)
    
    logger.info(
        f"请求完成: {request.method} {request.url} - "
        f"状态码: {response.status_code}"
    )
    
    return response


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "flycheck.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
