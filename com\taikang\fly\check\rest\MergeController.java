package com.taikang.fly.check.rest.MergeController;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.service.MergeService;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.dto.merge.MergeDownloadDTO;
import com.taikang.fly.check.dto.merge.MergeDTO;
import com.taikang.fly.check.vo.merge.MergeVO;
import java.util.List;
import com.taikang.fly.check.service.MergeFieldService;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.Integer;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.vo.merge.BillDateVO;
import org.springframework.web.multipart.MultipartFile;

public class MergeController	// class@0002a0 from classes.dex
{
    MergeFieldService mergeFieldService;
    MergeService mergeService;

    public void MergeController(){
       super();
    }
    public CommResponse delMergeById(String ruleIds){
       this.mergeService.delMergeById(ruleIds);
       return CommResponse.success();
    }
    public CommResponse delTemplate(){
       return this.mergeService.delTemplate();
    }
    public void downloadTemplate(HttpServletResponse response){
       this.mergeService.downloadTemplate(response);
    }
    public void executeAll(MergeDownloadDTO mergeDownloadDTO,HttpServletResponse response){
       this.mergeService.downloadMerge(mergeDownloadDTO, response);
    }
    public CommResponse getMergeTotalPrice(MergeDTO mergeDTO){
       return CommResponse.success(this.mergeService.getMergeTotalPrice(mergeDTO));
    }
    public CommResponse mergeField(){
       return CommResponse.success(this.mergeFieldService.mergeField());
    }
    public CommResponse mergeFieldInsert(List list){
       CommResponse uCommRespons = (this.mergeFieldService.mergeFieldInsert(list))? CommResponse.success(): CommResponse.error(ResponseCodeEnum.SAVE_ERROR);
       return uCommRespons;
    }
    public CommResponse queryMergeByRuleID(Integer pageNum,Integer pageSize,String ruleId,List fieldList){
       return CommResponse.success(this.mergeService.queryMergeByRuleID(pageNum, pageSize, ruleId, fieldList));
    }
    public CommResponse queryMinAndMaxBillDate(){
       return CommResponse.success(this.mergeService.queryMinAndMaxBillDate());
    }
    public CommResponse queryPage(Integer pageNum,Integer pageSize,MergeDTO mergeDTO){
       return CommResponse.success(this.mergeService.queryPage(pageNum, pageSize, mergeDTO));
    }
    public CommResponse uploadTemplate(MultipartFile file){
       this.mergeService.uploadTemplate(file);
       return CommResponse.success();
    }
}
