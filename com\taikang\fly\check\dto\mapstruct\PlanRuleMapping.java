package com.taikang.fly.check.dto.mapstruct.PlanRuleMapping;
import java.util.List;
import com.taikang.fly.check.dto.planRule.PlanRuleAddDto;
import com.taikang.fly.check.mybatis.domain.PlanRule;
import com.taikang.fly.check.dto.plan.PlanRuleRespDto;

public interface abstract PlanRuleMapping	// class@000175 from classes.dex
{

    List DtosToEntry(List p0);
    PlanRule dtoToEntry(PlanRuleAddDto p0);
    PlanRuleRespDto entryToDto(PlanRule p0);
    List entryToDtos(List p0);
}
