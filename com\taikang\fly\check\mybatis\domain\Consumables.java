package com.taikang.fly.check.mybatis.domain.Consumables;
import java.lang.Object;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.StringBuilder;

public class Consumables	// class@000237 from classes.dex
{
    private Date endTime;
    private BigDecimal hospitalSelfPay;
    private BigDecimal maxPrice;
    private BigDecimal oneMaxPrice;
    private String outProInfo;
    private BigDecimal outpatientSelfPay;
    private String payer;
    private String paymentCategory;
    private String proCode;
    private String proConnotation;
    private String proName;
    private String remark;
    private Date startTime;
    private BigDecimal threeMaxPrice;
    private BigDecimal twoMaxPrice;
    private BigDecimal workInjurySelfPay;
    private BigDecimal yourSelfPay;

    public void Consumables(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof Consumables;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof Consumables){
          b = false;
       }else {
          Consumables uConsumables = o;
          if (!uConsumables.canEqual(this)) {
             b = false;
          }else {
             String proCode = this.getProCode();
             String proCode1 = uConsumables.getProCode();
             if (proCode == null) {
                if (proCode1 != null) {
                   b = false;
                }
             }else if(proCode.equals(proCode1)){
             }
             String proName = this.getProName();
             String proName1 = uConsumables.getProName();
             if (proName == null) {
                if (proName1 != null) {
                   b = false;
                }
             }else if(proName.equals(proName1)){
             }
             String proConnotati = this.getProConnotation();
             String proConnotati1 = uConsumables.getProConnotation();
             if (proConnotati == null) {
                if (proConnotati1 != null) {
                   b = false;
                }
             }else if(proConnotati.equals(proConnotati1)){
             }
             String outProInfo = this.getOutProInfo();
             String outProInfo1 = uConsumables.getOutProInfo();
             if (outProInfo == null) {
                if (outProInfo1 != null) {
                   b = false;
                }
             }else if(outProInfo.equals(outProInfo1)){
             }
             String paymentCateg = this.getPaymentCategory();
             String paymentCateg1 = uConsumables.getPaymentCategory();
             if (paymentCateg == null) {
                if (paymentCateg1 != null) {
                   b = false;
                }
             }else if(paymentCateg.equals(paymentCateg1)){
             }
             String payer = this.getPayer();
             String payer1 = uConsumables.getPayer();
             if (payer == null) {
                if (payer1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(payer.equals(payer1)){
             }
             String remark = this.getRemark();
             String remark1 = uConsumables.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             BigDecimal maxPrice = this.getMaxPrice();
             BigDecimal maxPrice1 = uConsumables.getMaxPrice();
             if (maxPrice == null) {
                if (maxPrice1 != null) {
                   b = false;
                }
             }else if(maxPrice.equals(maxPrice1)){
             }
             BigDecimal threeMaxPric = this.getThreeMaxPrice();
             BigDecimal threeMaxPric1 = uConsumables.getThreeMaxPrice();
             if (threeMaxPric == null) {
                if (threeMaxPric1 != null) {
                label_00eb :
                   b = false;
                }
             }else if(threeMaxPric.equals(threeMaxPric1)){
             }
             BigDecimal twoMaxPrice = this.getTwoMaxPrice();
             BigDecimal twoMaxPrice1 = uConsumables.getTwoMaxPrice();
             if (twoMaxPrice == null) {
                if (twoMaxPrice1 != null) {
                   b = false;
                }
             }else if(twoMaxPrice.equals(twoMaxPrice1)){
             }
             BigDecimal oneMaxPrice = this.getOneMaxPrice();
             BigDecimal oneMaxPrice1 = uConsumables.getOneMaxPrice();
             if (oneMaxPrice == null) {
                if (oneMaxPrice1 != null) {
                   b = false;
                }
             }else if(oneMaxPrice.equals(oneMaxPrice1)){
             }
             BigDecimal outpatientSe = this.getOutpatientSelfPay();
             BigDecimal outpatientSe1 = uConsumables.getOutpatientSelfPay();
             if (outpatientSe == null) {
                if (outpatientSe1 != null) {
                   b = false;
                }
             }else if(outpatientSe.equals(outpatientSe1)){
             }
             BigDecimal hospitalSelf = this.getHospitalSelfPay();
             BigDecimal hospitalSelf1 = uConsumables.getHospitalSelfPay();
             if (hospitalSelf == null) {
                if (hospitalSelf1 != null) {
                   b = false;
                }
             }else if(hospitalSelf.equals(hospitalSelf1)){
             }
             BigDecimal workInjurySe = this.getWorkInjurySelfPay();
             BigDecimal workInjurySe1 = uConsumables.getWorkInjurySelfPay();
             if (workInjurySe == null) {
                if (workInjurySe1 != null) {
                   b = false;
                }
             }else if(workInjurySe.equals(workInjurySe1)){
             }
             BigDecimal yourSelfPay = this.getYourSelfPay();
             BigDecimal yourSelfPay1 = uConsumables.getYourSelfPay();
             if (yourSelfPay == null) {
                if (yourSelfPay1 != null) {
                   b = false;
                }
             }else if(yourSelfPay.equals(yourSelfPay1)){
             }
             Date startTime = this.getStartTime();
             Date startTime1 = uConsumables.getStartTime();
             if (startTime == null) {
                if (startTime1 != null) {
                label_019b :
                   b = false;
                }
             }else if(startTime.equals(startTime1)){
             }
             Date endTime = this.getEndTime();
             Date endTime1 = uConsumables.getEndTime();
             if (endTime == null) {
                if (endTime1 != null) {
                label_01b3 :
                   b = false;
                }
             }else if(endTime.equals(endTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getEndTime(){
       return this.endTime;
    }
    public BigDecimal getHospitalSelfPay(){
       return this.hospitalSelfPay;
    }
    public BigDecimal getMaxPrice(){
       return this.maxPrice;
    }
    public BigDecimal getOneMaxPrice(){
       return this.oneMaxPrice;
    }
    public String getOutProInfo(){
       return this.outProInfo;
    }
    public BigDecimal getOutpatientSelfPay(){
       return this.outpatientSelfPay;
    }
    public String getPayer(){
       return this.payer;
    }
    public String getPaymentCategory(){
       return this.paymentCategory;
    }
    public String getProCode(){
       return this.proCode;
    }
    public String getProConnotation(){
       return this.proConnotation;
    }
    public String getProName(){
       return this.proName;
    }
    public String getRemark(){
       return this.remark;
    }
    public Date getStartTime(){
       return this.startTime;
    }
    public BigDecimal getThreeMaxPrice(){
       return this.threeMaxPrice;
    }
    public BigDecimal getTwoMaxPrice(){
       return this.twoMaxPrice;
    }
    public BigDecimal getWorkInjurySelfPay(){
       return this.workInjurySelfPay;
    }
    public BigDecimal getYourSelfPay(){
       return this.yourSelfPay;
    }
    public int hashCode(){
       String $proCode;
       int PRIME = 59;
       int result = 1;
       int i = (($proCode = this.getProCode()) == null)? 43: $proCode.hashCode();
       result = i + 59;
       String $proName = this.getProName();
       int i1 = result * 59;
       i = ($proName == null)? 43: $proName.hashCode();
       result = i1 + i;
       String $proConnotation = this.getProConnotation();
       i1 = result * 59;
       i = ($proConnotation == null)? 43: $proConnotation.hashCode();
       result = i1 + i;
       String $outProInfo = this.getOutProInfo();
       i1 = result * 59;
       i = ($outProInfo == null)? 43: $outProInfo.hashCode();
       result = i1 + i;
       String $paymentCategory = this.getPaymentCategory();
       i1 = result * 59;
       i = ($paymentCategory == null)? 43: $paymentCategory.hashCode();
       result = i1 + i;
       String payer = this.getPayer();
       i1 = result * 59;
       i = (payer == null)? 43: payer.hashCode();
       String remark = this.getRemark();
       i1 = (i1 + i) * 59;
       i = (remark == null)? 43: remark.hashCode();
       BigDecimal maxPrice = this.getMaxPrice();
       i1 = (i1 + i) * 59;
       i = (maxPrice == null)? 43: maxPrice.hashCode();
       BigDecimal threeMaxPric = this.getThreeMaxPrice();
       i1 = (i1 + i) * 59;
       i = (threeMaxPric == null)? 43: threeMaxPric.hashCode();
       BigDecimal twoMaxPrice = this.getTwoMaxPrice();
       i1 = (i1 + i) * 59;
       i = (twoMaxPrice == null)? 43: twoMaxPrice.hashCode();
       BigDecimal oneMaxPrice = this.getOneMaxPrice();
       i1 = (i1 + i) * 59;
       i = (oneMaxPrice == null)? 43: oneMaxPrice.hashCode();
       BigDecimal outpatientSe = this.getOutpatientSelfPay();
       i1 = (i1 + i) * 59;
       i = (outpatientSe == null)? 43: outpatientSe.hashCode();
       BigDecimal hospitalSelf = this.getHospitalSelfPay();
       i1 = (i1 + i) * 59;
       i = (hospitalSelf == null)? 43: hospitalSelf.hashCode();
       BigDecimal workInjurySe = this.getWorkInjurySelfPay();
       i1 = (i1 + i) * 59;
       i = (workInjurySe == null)? 43: workInjurySe.hashCode();
       BigDecimal yourSelfPay = this.getYourSelfPay();
       i1 = (i1 + i) * 59;
       i = (yourSelfPay == null)? 43: yourSelfPay.hashCode();
       Date startTime = this.getStartTime();
       i1 = (i1 + i) * 59;
       i = (startTime == null)? 43: startTime.hashCode();
       Date endTime = this.getEndTime();
       i1 = (i1 + i) * 59;
       i = (endTime == null)? 43: endTime.hashCode();
       return (i1 + i);
    }
    public void setEndTime(Date endTime){
       this.endTime = endTime;
    }
    public void setHospitalSelfPay(BigDecimal hospitalSelfPay){
       this.hospitalSelfPay = hospitalSelfPay;
    }
    public void setMaxPrice(BigDecimal maxPrice){
       this.maxPrice = maxPrice;
    }
    public void setOneMaxPrice(BigDecimal oneMaxPrice){
       this.oneMaxPrice = oneMaxPrice;
    }
    public void setOutProInfo(String outProInfo){
       this.outProInfo = outProInfo;
    }
    public void setOutpatientSelfPay(BigDecimal outpatientSelfPay){
       this.outpatientSelfPay = outpatientSelfPay;
    }
    public void setPayer(String payer){
       this.payer = payer;
    }
    public void setPaymentCategory(String paymentCategory){
       this.paymentCategory = paymentCategory;
    }
    public void setProCode(String proCode){
       this.proCode = proCode;
    }
    public void setProConnotation(String proConnotation){
       this.proConnotation = proConnotation;
    }
    public void setProName(String proName){
       this.proName = proName;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public void setStartTime(Date startTime){
       this.startTime = startTime;
    }
    public void setThreeMaxPrice(BigDecimal threeMaxPrice){
       this.threeMaxPrice = threeMaxPrice;
    }
    public void setTwoMaxPrice(BigDecimal twoMaxPrice){
       this.twoMaxPrice = twoMaxPrice;
    }
    public void setWorkInjurySelfPay(BigDecimal workInjurySelfPay){
       this.workInjurySelfPay = workInjurySelfPay;
    }
    public void setYourSelfPay(BigDecimal yourSelfPay){
       this.yourSelfPay = yourSelfPay;
    }
    public String toString(){
       return "Consumables\(proCode="+this.getProCode()+", proName="+this.getProName()+", proConnotation="+this.getProConnotation()+", outProInfo="+this.getOutProInfo()+", paymentCategory="+this.getPaymentCategory()+", payer="+this.getPayer()+", remark="+this.getRemark()+", maxPrice="+this.getMaxPrice()+", threeMaxPrice="+this.getThreeMaxPrice()+", twoMaxPrice="+this.getTwoMaxPrice()+", oneMaxPrice="+this.getOneMaxPrice()+", outpatientSelfPay="+this.getOutpatientSelfPay()+", hospitalSelfPay="+this.getHospitalSelfPay()+", workInjurySelfPay="+this.getWorkInjurySelfPay()+", yourSelfPay="+this.getYourSelfPay()+", startTime="+this.getStartTime()+", endTime="+this.getEndTime()+"\)";
    }
}
