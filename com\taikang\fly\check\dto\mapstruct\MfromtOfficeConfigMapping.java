package com.taikang.fly.check.dto.mapstruct.MfromtOfficeConfigMapping;
import com.taikang.fly.check.mybatis.domain.MfromtOfficeConfig;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigRespDto;
import java.util.List;

public interface abstract MfromtOfficeConfigMapping	// class@000169 from classes.dex
{

    ColConfigRespDto toColConfigRespDto(MfromtOfficeConfig p0);
    List toColConfigRespDtoList(List p0);
}
