package com.taikang.fly.check.dto.plan.PlanRuleRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PlanRuleRespDto implements Serializable	// class@0001a3 from classes.dex
{
    private String createTime;
    private String id;
    private String medicalCategory;
    private String planId;
    private String ps;
    private String ruleId;
    private String ruleIntension;
    private String ruleName;
    private String ruleType;
    private String sourceOfRule;
    private String status;
    private static final long serialVersionUID = 0x1;

    public void PlanRuleRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanRuleRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanRuleRespDto){
          b = false;
       }else {
          PlanRuleRespDto planRuleResp = o;
          if (!planRuleResp.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = planRuleResp.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleId = this.getRuleId();
             String ruleId1 = planRuleResp.getRuleId();
             if (ruleId == null) {
                if (ruleId1 != null) {
                   b = false;
                }
             }else if(ruleId.equals(ruleId1)){
             }
             String planId = this.getPlanId();
             String planId1 = planRuleResp.getPlanId();
             if (planId == null) {
                if (planId1 != null) {
                   b = false;
                }
             }else if(planId.equals(planId1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = planRuleResp.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String sourceOfRule = this.getSourceOfRule();
             String sourceOfRule1 = planRuleResp.getSourceOfRule();
             if (sourceOfRule == null) {
                if (sourceOfRule1 != null) {
                   b = false;
                }
             }else if(sourceOfRule.equals(sourceOfRule1)){
             }
             String ruleIntensio = this.getRuleIntension();
             String ruleIntensio1 = planRuleResp.getRuleIntension();
             if (ruleIntensio == null) {
                if (ruleIntensio1 != null) {
                label_00a1 :
                   b = false;
                }
             }else if(ruleIntensio.equals(ruleIntensio1)){
             }
             String ps = this.getPs();
             String ps1 = planRuleResp.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = planRuleResp.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                label_00d1 :
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String medicalCateg = this.getMedicalCategory();
             String medicalCateg1 = planRuleResp.getMedicalCategory();
             if (medicalCateg == null) {
                if (medicalCateg1 != null) {
                   b = false;
                }
             }else if(medicalCateg.equals(medicalCateg1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = planRuleResp.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String status = this.getStatus();
             String status1 = planRuleResp.getStatus();
             if (status == null) {
                if (status1 != null) {
                label_0117 :
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getId(){
       return this.id;
    }
    public String getMedicalCategory(){
       return this.medicalCategory;
    }
    public String getPlanId(){
       return this.planId;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleId(){
       return this.ruleId;
    }
    public String getRuleIntension(){
       return this.ruleIntension;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSourceOfRule(){
       return this.sourceOfRule;
    }
    public String getStatus(){
       return this.status;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleId = this.getRuleId();
       int i1 = result * 59;
       i = ($ruleId == null)? 43: $ruleId.hashCode();
       result = i1 + i;
       String $planId = this.getPlanId();
       i1 = result * 59;
       i = ($planId == null)? 43: $planId.hashCode();
       result = i1 + i;
       String $ruleName = this.getRuleName();
       i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $sourceOfRule = this.getSourceOfRule();
       i1 = result * 59;
       i = ($sourceOfRule == null)? 43: $sourceOfRule.hashCode();
       result = i1 + i;
       String ruleIntensio = this.getRuleIntension();
       i1 = result * 59;
       i = (ruleIntensio == null)? 43: ruleIntensio.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String ruleType = this.getRuleType();
       i1 = (i1 + i) * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String medicalCateg = this.getMedicalCategory();
       i1 = (i1 + i) * 59;
       i = (medicalCateg == null)? 43: medicalCateg.hashCode();
       String createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String status = this.getStatus();
       i1 = (i1 + i) * 59;
       i = (status == null)? 43: status.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setMedicalCategory(String medicalCategory){
       this.medicalCategory = medicalCategory;
    }
    public void setPlanId(String planId){
       this.planId = planId;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleId(String ruleId){
       this.ruleId = ruleId;
    }
    public void setRuleIntension(String ruleIntension){
       this.ruleIntension = ruleIntension;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSourceOfRule(String sourceOfRule){
       this.sourceOfRule = sourceOfRule;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public String toString(){
       return "PlanRuleRespDto\(id="+this.getId()+", ruleId="+this.getRuleId()+", planId="+this.getPlanId()+", ruleName="+this.getRuleName()+", sourceOfRule="+this.getSourceOfRule()+", ruleIntension="+this.getRuleIntension()+", ps="+this.getPs()+", ruleType="+this.getRuleType()+", medicalCategory="+this.getMedicalCategory()+", createTime="+this.getCreateTime()+", status="+this.getStatus()+"\)";
    }
}
