package com.taikang.fly.check.mybatis.dao.YbConsumablesListMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.YbConsumablesList;
import java.lang.Object;
import java.util.List;

public interface abstract YbConsumablesListMapper implements BaseMapper	// class@000229 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    int insert(YbConsumablesList p0);
    default int insert(Object p0){
       return this.insert(p0);
    }
    List selectAll();
    YbConsumablesList selectByPrimaryKey(String p0);
    int updateByPrimaryKey(YbConsumablesList p0);
}
