package com.taikang.fly.check.utils.LoginInfoUtil;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.dto.DatasourceInfo;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;

public class LoginInfoUtil	// class@000342 from classes.dex
{

    public void LoginInfoUtil(){
       super();
    }
    public static String getHospName(){
       return LoginInfoUtil.getUserInfo().getDatasourceInfo().getHospName();
    }
    public static String getHospNameThread(){
       return LoginInfoUtil.getUserInfoThread().getDatasourceInfo().getHospName();
    }
    public static UserDto getUserInfo(){
       return ThreadLocalContextHolder.getContext().getUserInfo();
    }
    public static UserDto getUserInfoThread(){
       return ThreadLocalContextHolder.getContextThread().getUserInfo();
    }
}
