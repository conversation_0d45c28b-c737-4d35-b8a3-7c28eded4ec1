<template>
  <div class="test-page">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">前后端集成测试</h1>
        <p class="page-description">测试前端与后端API的连接</p>
      </div>

      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>API连接测试</span>
            <el-button type="primary" @click="testConnection" :loading="loading">
              测试连接
            </el-button>
          </div>
        </template>

        <div class="test-results">
          <el-alert
            v-if="connectionStatus"
            :title="connectionStatus.title"
            :type="connectionStatus.type"
            :description="connectionStatus.description"
            show-icon
            :closable="false"
          />

          <div v-if="apiData.length > 0" class="api-data">
            <h3>获取到的规则数据：</h3>
            <el-table :data="apiData" stripe style="width: 100%">
              <el-table-column prop="rule_name" label="规则名称" width="200" />
              <el-table-column prop="rule_describe" label="规则描述" />
              <el-table-column prop="rule_type" label="规则类型" width="100" />
              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <el-card class="test-card">
        <template #header>
          <span>系统信息</span>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="前端地址">
            {{ frontendUrl }}
          </el-descriptions-item>
          <el-descriptions-item label="后端地址">
            {{ backendUrl }}
          </el-descriptions-item>
          <el-descriptions-item label="当前时间">
            {{ currentTime }}
          </el-descriptions-item>
          <el-descriptions-item label="浏览器">
            {{ userAgent }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getRules } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const apiData = ref([])
const connectionStatus = ref(null)

const frontendUrl = window.location.origin
const backendUrl = 'http://localhost:8000'
const currentTime = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'))
const userAgent = navigator.userAgent

// 方法
const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const testConnection = async () => {
  loading.value = true
  connectionStatus.value = null
  apiData.value = []

  try {
    // 测试获取规则数据
    const response = await getRules()
    
    if (response && Array.isArray(response)) {
      connectionStatus.value = {
        title: '连接成功！',
        type: 'success',
        description: `成功获取到 ${response.length} 条规则数据`
      }
      apiData.value = response.slice(0, 5) // 只显示前5条数据
      ElMessage.success('前后端连接测试成功')
    } else {
      throw new Error('返回数据格式不正确')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    connectionStatus.value = {
      title: '连接失败！',
      type: 'error',
      description: `错误信息: ${error.message || '未知错误'}`
    }
    ElMessage.error('前后端连接测试失败')
  } finally {
    loading.value = false
  }
}

// 更新当前时间
const updateTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载
onMounted(() => {
  // 自动测试连接
  testConnection()
  
  // 每秒更新时间
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.test-page {
  height: 100%;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-results {
  margin-top: 20px;
}

.api-data {
  margin-top: 20px;
}

.api-data h3 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
