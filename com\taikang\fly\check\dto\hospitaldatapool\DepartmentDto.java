package com.taikang.fly.check.dto.hospitaldatapool.DepartmentDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DepartmentDto implements Serializable	// class@00012d from classes.dex
{
    private String name;
    private String value;
    private String year;
    private static final long serialVersionUID = 0x1;

    public void DepartmentDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DepartmentDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DepartmentDto) {
             b = false;
          }else {
             DepartmentDto uDepartmentD = o;
             if (!uDepartmentD.canEqual(this)) {
                b = false;
             }else {
                String year = this.getYear();
                String year1 = uDepartmentD.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String name = this.getName();
                String name1 = uDepartmentD.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String value = this.getValue();
                String value1 = uDepartmentD.getValue();
                if (value == null) {
                   if (value1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!value.equals(value1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getName(){
       return this.name;
    }
    public String getValue(){
       return this.value;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $year;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($year = this.getYear()) == null)? i: $year.hashCode();
       result = i1 + 59;
       String $name = this.getName();
       int i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       String $value = this.getValue();
       i1 = result * 59;
       if ($value != null) {
          i = $value.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setValue(String value){
       this.value = value;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "DepartmentDto\(year="+this.getYear()+", name="+this.getName()+", value="+this.getValue()+"\)";
    }
}
