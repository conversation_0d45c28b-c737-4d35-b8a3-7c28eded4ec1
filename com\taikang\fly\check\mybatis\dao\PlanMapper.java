package com.taikang.fly.check.mybatis.dao.PlanMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.Plan;
import java.lang.Object;

public interface abstract PlanMapper implements BaseMapper	// class@000216 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    List findPlanList(JSONObject p0);
    int insert(Plan p0);
    default int insert(Object p0){
       return this.insert(p0);
    }
    List selectAll();
    Plan selectByIdAndStatus(String p0);
    Plan selectByPrimaryKey(String p0);
    int updateByPrimaryKey(Plan p0);
}
