package com.taikang.fly.check.vo.drg.DrgGroupsProfitAndLossVo;
import java.lang.Object;
import java.lang.String;
import java.math.BigDecimal;
import java.lang.StringBuilder;

public class DrgGroupsProfitAndLossVo	// class@00036c from classes.dex
{
    private String drgCode;
    private String drgName;
    private BigDecimal totalProfitAndLoss;

    public void DrgGroupsProfitAndLossVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgGroupsProfitAndLossVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgGroupsProfitAndLossVo) {
             b = false;
          }else {
             DrgGroupsProfitAndLossVo uDrgGroupsPr = o;
             if (!uDrgGroupsPr.canEqual(this)) {
                b = false;
             }else {
                String drgCode = this.getDrgCode();
                String drgCode1 = uDrgGroupsPr.getDrgCode();
                if (drgCode == null) {
                   if (drgCode1 != null) {
                      b = false;
                   }
                }else if(drgCode.equals(drgCode1)){
                }
                String drgName = this.getDrgName();
                String drgName1 = uDrgGroupsPr.getDrgName();
                if (drgName == null) {
                   if (drgName1 != null) {
                      b = false;
                   }
                }else if(drgName.equals(drgName1)){
                }
                BigDecimal totalProfitA = this.getTotalProfitAndLoss();
                BigDecimal totalProfitA1 = uDrgGroupsPr.getTotalProfitAndLoss();
                if (totalProfitA == null) {
                   if (totalProfitA1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!totalProfitA.equals(totalProfitA1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getDrgName(){
       return this.drgName;
    }
    public BigDecimal getTotalProfitAndLoss(){
       return this.totalProfitAndLoss;
    }
    public int hashCode(){
       String $drgCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgCode = this.getDrgCode()) == null)? i: $drgCode.hashCode();
       result = i1 + 59;
       String $drgName = this.getDrgName();
       int i2 = result * 59;
       i1 = ($drgName == null)? i: $drgName.hashCode();
       result = i2 + i1;
       BigDecimal $totalProfitAndLoss = this.getTotalProfitAndLoss();
       i1 = result * 59;
       if ($totalProfitAndLoss != null) {
          i = $totalProfitAndLoss.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setDrgName(String drgName){
       this.drgName = drgName;
    }
    public void setTotalProfitAndLoss(BigDecimal totalProfitAndLoss){
       this.totalProfitAndLoss = totalProfitAndLoss;
    }
    public String toString(){
       return "DrgGroupsProfitAndLossVo\(drgCode="+this.getDrgCode()+", drgName="+this.getDrgName()+", totalProfitAndLoss="+this.getTotalProfitAndLoss()+"\)";
    }
}
