package com.taikang.fly.check.dto.ErrorDataExportExcrlDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ErrorDataExportExcrlDto implements Serializable	// class@00009d from classes.dex
{
    private String details;
    private String field;
    private String line;
    private static final long serialVersionUID = 0x1;

    public void ErrorDataExportExcrlDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ErrorDataExportExcrlDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ErrorDataExportExcrlDto) {
             b = false;
          }else {
             ErrorDataExportExcrlDto uErrorDataEx = o;
             if (!uErrorDataEx.canEqual(this)) {
                b = false;
             }else {
                String line = this.getLine();
                String line1 = uErrorDataEx.getLine();
                if (line == null) {
                   if (line1 != null) {
                      b = false;
                   }
                }else if(line.equals(line1)){
                }
                String field = this.getField();
                String field1 = uErrorDataEx.getField();
                if (field == null) {
                   if (field1 != null) {
                      b = false;
                   }
                }else if(field.equals(field1)){
                }
                String details = this.getDetails();
                String details1 = uErrorDataEx.getDetails();
                if (details == null) {
                   if (details1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!details.equals(details1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDetails(){
       return this.details;
    }
    public String getField(){
       return this.field;
    }
    public String getLine(){
       return this.line;
    }
    public int hashCode(){
       String $line;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($line = this.getLine()) == null)? i: $line.hashCode();
       result = i1 + 59;
       String $field = this.getField();
       int i2 = result * 59;
       i1 = ($field == null)? i: $field.hashCode();
       result = i2 + i1;
       String $details = this.getDetails();
       i1 = result * 59;
       if ($details != null) {
          i = $details.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDetails(String details){
       this.details = details;
    }
    public void setField(String field){
       this.field = field;
    }
    public void setLine(String line){
       this.line = line;
    }
    public String toString(){
       return "ErrorDataExportExcrlDto\(line="+this.getLine()+", field="+this.getField()+", details="+this.getDetails()+"\)";
    }
}
