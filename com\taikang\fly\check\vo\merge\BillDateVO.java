package com.taikang.fly.check.vo.merge.BillDateVO;
import java.lang.Object;
import java.time.LocalDateTime;
import java.lang.String;
import java.lang.StringBuilder;

public class BillDateVO	// class@00037e from classes.dex
{
    private LocalDateTime endTime;
    private LocalDateTime startTime;

    public void BillDateVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof BillDateVO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof BillDateVO) {
             b = false;
          }else {
             BillDateVO uBillDateVO = o;
             if (!uBillDateVO.canEqual(this)) {
                b = false;
             }else {
                LocalDateTime startTime = this.getStartTime();
                LocalDateTime startTime1 = uBillDateVO.getStartTime();
                if (startTime == null) {
                   if (startTime1 != null) {
                      b = false;
                   }
                }else if(startTime.equals(startTime1)){
                }
                LocalDateTime endTime = this.getEndTime();
                LocalDateTime endTime1 = uBillDateVO.getEndTime();
                if (endTime == null) {
                   if (endTime1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!endTime.equals(endTime1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public LocalDateTime getEndTime(){
       return this.endTime;
    }
    public LocalDateTime getStartTime(){
       return this.startTime;
    }
    public int hashCode(){
       LocalDateTime $startTime;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($startTime = this.getStartTime()) == null)? i: $startTime.hashCode();
       result = i1 + 59;
       LocalDateTime $endTime = this.getEndTime();
       i1 = result * 59;
       if ($endTime != null) {
          i = $endTime.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setEndTime(LocalDateTime endTime){
       this.endTime = endTime;
    }
    public void setStartTime(LocalDateTime startTime){
       this.startTime = startTime;
    }
    public String toString(){
       return "BillDateVO\(startTime="+this.getStartTime()+", endTime="+this.getEndTime()+"\)";
    }
}
