package org.springframework.boot.loader.jar.CentralDirectoryParser;
import java.lang.Object;
import java.util.ArrayList;
import org.springframework.boot.loader.jar.CentralDirectoryEndRecord;
import org.springframework.boot.loader.data.RandomAccessData;
import org.springframework.boot.loader.jar.CentralDirectoryFileHeader;
import org.springframework.boot.loader.jar.JarEntryFilter;
import org.springframework.boot.loader.jar.AsciiBytes;
import java.util.Iterator;
import java.util.List;
import org.springframework.boot.loader.jar.CentralDirectoryVisitor;

class CentralDirectoryParser	// class@00054d from classes.dex
{
    private final List visitors;
    private static final int CENTRAL_DIRECTORY_HEADER_BASE_SIZE = 46;

    void CentralDirectoryParser(){
       super();
       this.visitors = new ArrayList();
    }
    private RandomAccessData getArchiveData(CentralDirectoryEndRecord endRecord,RandomAccessData data){
       long offset = endRecord.getStartOfArchive(data);
       if (offset) {
          data = data.getSubsection(offset, (data.getSize() - offset));
       }
       return data;
    }
    private void parseEntries(CentralDirectoryEndRecord endRecord,RandomAccessData centralDirectoryData){
       byte[] bytes = centralDirectoryData.read(0, centralDirectoryData.getSize());
       CentralDirectoryFileHeader fileHeader = new CentralDirectoryFileHeader();
       int dataOffset = 0;
       for (int i = 0; i < endRecord.getNumberOfRecords(); i++) {
          fileHeader.load(bytes, dataOffset, null, 0, null);
          this.visitFileHeader(dataOffset, fileHeader);
          int ix = fileHeader.getName().length() + 46;
          ix = ix + fileHeader.getComment().length();
          ix = ix + fileHeader.getExtra().length;
          dataOffset = dataOffset + ix;
       }
       return;
    }
    private void visitEnd(){
       Iterator iterator = this.visitors.iterator();
       while (iterator.hasNext()) {
          iterator.next().visitEnd();
       }
       return;
    }
    private void visitFileHeader(int dataOffset,CentralDirectoryFileHeader fileHeader){
       Iterator iterator = this.visitors.iterator();
       while (iterator.hasNext()) {
          iterator.next().visitFileHeader(fileHeader, dataOffset);
       }
       return;
    }
    private void visitStart(CentralDirectoryEndRecord endRecord,RandomAccessData centralDirectoryData){
       Iterator iterator = this.visitors.iterator();
       while (iterator.hasNext()) {
          iterator.next().visitStart(endRecord, centralDirectoryData);
       }
       return;
    }
    public CentralDirectoryVisitor addVisitor(CentralDirectoryVisitor visitor){
       this.visitors.add(visitor);
       return visitor;
    }
    public RandomAccessData parse(RandomAccessData data,boolean skipPrefixBytes){
       CentralDirectoryEndRecord endRecord = new CentralDirectoryEndRecord(data);
       if (skipPrefixBytes) {
          data = this.getArchiveData(endRecord, data);
       }
       RandomAccessData centralDirec = endRecord.getCentralDirectory(data);
       this.visitStart(endRecord, centralDirec);
       this.parseEntries(endRecord, centralDirec);
       this.visitEnd();
       return data;
    }
}
