package com.taikang.fly.check.mybatis.dao.MergeMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.dto.merge.MergeDTO;
import com.taikang.fly.check.vo.merge.MergeVO;
import java.util.List;
import com.taikang.fly.check.dto.merge.MergeDownloadDTO;
import com.taikang.fly.check.vo.merge.BillDateVO;
import java.util.LinkedHashMap;
import java.util.Map;

public interface abstract MergeMapper implements BaseMapper	// class@000202 from classes.dex
{

    void deleteMerge();
    void deleteMergeByNames(String p0);
    MergeVO getMergeTotalPrice(MergeDTO p0);
    int isAllNull(String p0,String p1);
    List queryDetailList(String p0,String p1);
    List queryMergeResult(MergeDownloadDTO p0);
    BillDateVO queryMinAndMaxBillDate(String p0);
    List queryNotDownLoadResult(MergeDownloadDTO p0);
    List queryPageMergeList(MergeDTO p0);
    List queryResultSumVOList();
    LinkedHashMap queryTotalPrice(MergeDownloadDTO p0);
    List selectAll();
    Map selectCountOfRule(String p0,String p1);
    String selectDistinctDatasource();
    List selectDistinctId(String p0);
    List selectDownloadMergeList(List p0,String p1);
    List selectExtractMergeList(MergeDownloadDTO p0);
    String selectRuleNameById(String p0);
}
