package com.taikang.fly.check.mybatis.dao.RoleMenuMapper;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.RoleMenu;

public interface abstract RoleMenuMapper	// class@00021c from classes.dex
{

    void deleteByRoleCode(Map p0);
    Integer deleteByRoleId(String p0);
    List menuIdListByRoleCode(String p0);
    List queryMenuIdListByParams(Map p0);
    Integer saveRoleMenu(RoleMenu p0);
}
