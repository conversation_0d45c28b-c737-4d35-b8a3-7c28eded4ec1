package com.taikang.fly.check.dto.plan.PlanRuleSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PlanRuleSearchDto implements Serializable	// class@0001a4 from classes.dex
{
    private String medicalCategory;
    private String planId;
    private String ruleName;
    private String ruleType;
    private static final long serialVersionUID = 0x1;

    public void PlanRuleSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanRuleSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof PlanRuleSearchDto) {
             b = false;
          }else {
             PlanRuleSearchDto planRuleSear = o;
             if (!planRuleSear.canEqual(this)) {
                b = false;
             }else {
                String planId = this.getPlanId();
                String planId1 = planRuleSear.getPlanId();
                if (planId == null) {
                   if (planId1 != null) {
                      b = false;
                   }
                }else if(planId.equals(planId1)){
                }
                String ruleType = this.getRuleType();
                String ruleType1 = planRuleSear.getRuleType();
                if (ruleType == null) {
                   if (ruleType1 != null) {
                      b = false;
                   }
                }else if(ruleType.equals(ruleType1)){
                }
                String medicalCateg = this.getMedicalCategory();
                String medicalCateg1 = planRuleSear.getMedicalCategory();
                if (medicalCateg == null) {
                   if (medicalCateg1 != null) {
                      b = false;
                   }
                }else if(medicalCateg.equals(medicalCateg1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = planRuleSear.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!ruleName.equals(ruleName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getMedicalCategory(){
       return this.medicalCategory;
    }
    public String getPlanId(){
       return this.planId;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public int hashCode(){
       String $planId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($planId = this.getPlanId()) == null)? i: $planId.hashCode();
       result = i1 + 59;
       String $ruleType = this.getRuleType();
       int i2 = result * 59;
       i1 = ($ruleType == null)? i: $ruleType.hashCode();
       result = i2 + i1;
       String $medicalCategory = this.getMedicalCategory();
       i2 = result * 59;
       i1 = ($medicalCategory == null)? i: $medicalCategory.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i1 = result * 59;
       if ($ruleName != null) {
          i = $ruleName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setMedicalCategory(String medicalCategory){
       this.medicalCategory = medicalCategory;
    }
    public void setPlanId(String planId){
       this.planId = planId;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public String toString(){
       return "PlanRuleSearchDto\(planId="+this.getPlanId()+", ruleType="+this.getRuleType()+", medicalCategory="+this.getMedicalCategory()+", ruleName="+this.getRuleName()+"\)";
    }
}
