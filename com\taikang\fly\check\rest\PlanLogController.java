package com.taikang.fly.check.rest.PlanLogController;
import java.lang.Object;
import com.taikang.fly.check.dto.planLog.AddPlanLogDto;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.dto.RmpResponse;
import java.lang.Integer;
import com.taikang.fly.check.service.PlanLogService;
import java.lang.String;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.dto.plan.PlanLogSearchDto;

public class PlanLogController	// class@0002a6 from classes.dex
{
    private PlanLogService planLogService;

    public void PlanLogController(){
       super();
    }
    public RmpResponse addPlanLog(AddPlanLogDto addPlanLogDto,HttpServletResponse response){
       return RmpResponse.success(this.planLogService.addPlanLog(addPlanLogDto, response));
    }
    public RmpResponse deletePlanLog(String id){
       return RmpResponse.success(this.planLogService.deleteById(id));
    }
    public void downloadResultsById(String id,HttpServletResponse response){
       this.planLogService.downloadResultsById(id, response);
    }
    public RmpResponse getErrorRules(Integer pageNum,Integer pageSize,String id){
       return RmpResponse.success(this.planLogService.getErrorRules(pageNum, pageSize, id));
    }
    public RmpResponse getFlyRule(Integer pageNum,Integer pageSize,String id){
       return RmpResponse.success(this.planLogService.getFlyRule(pageNum, pageSize, id));
    }
    public RmpResponse queryPlanLogListPage(PlanLogSearchDto planLogSearchDto,Integer pageNum,Integer pageSize){
       NativePage planLogListPage = this.planLogService.queryPlanLogListPage(planLogSearchDto, pageNum, pageSize);
       return RmpResponse.success(planLogListPage);
    }
}
