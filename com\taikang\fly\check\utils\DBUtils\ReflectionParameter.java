package com.taikang.fly.check.utils.DBUtils.ReflectionParameter;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class ReflectionParameter implements Serializable	// class@000338 from classes.dex
{
    private List columnAttributes;
    private String filePath;
    private String operType;
    private List rawDataHeaders;
    private String tableName;
    private static final long serialVersionUID = 0xcf1f28defc0b8f6c;

    public void ReflectionParameter(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ReflectionParameter;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ReflectionParameter) {
             b = false;
          }else {
             ReflectionParameter reflectionPa = o;
             if (!reflectionPa.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = reflectionPa.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                List columnAttrib = this.getColumnAttributes();
                List columnAttrib1 = reflectionPa.getColumnAttributes();
                if (columnAttrib == null) {
                   if (columnAttrib1 != null) {
                      b = false;
                   }
                }else if(columnAttrib.equals(columnAttrib1)){
                }
                String filePath = this.getFilePath();
                String filePath1 = reflectionPa.getFilePath();
                if (filePath == null) {
                   if (filePath1 != null) {
                      b = false;
                   }
                }else if(filePath.equals(filePath1)){
                }
                String operType = this.getOperType();
                String operType1 = reflectionPa.getOperType();
                if (operType == null) {
                   if (operType1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!operType.equals(operType1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getColumnAttributes(){
       return this.columnAttributes;
    }
    public String getFilePath(){
       return this.filePath;
    }
    public String getOperType(){
       return this.operType;
    }
    public List getRawDataHeaders(){
       return this.rawDataHeaders;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       List $columnAttributes = this.getColumnAttributes();
       int i2 = result * 59;
       i1 = ($columnAttributes == null)? i: $columnAttributes.hashCode();
       result = i2 + i1;
       String $filePath = this.getFilePath();
       i2 = result * 59;
       i1 = ($filePath == null)? i: $filePath.hashCode();
       result = i2 + i1;
       String $operType = this.getOperType();
       i1 = result * 59;
       if ($operType != null) {
          i = $operType.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumnAttributes(List columnAttributes){
       this.columnAttributes = columnAttributes;
    }
    public void setFilePath(String filePath){
       this.filePath = filePath;
    }
    public void setOperType(String operType){
       this.operType = operType;
    }
    public void setRawDataHeaders(List rawDataHeaders){
       this.rawDataHeaders = rawDataHeaders;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "ReflectionParameter\(tableName="+this.getTableName()+", columnAttributes="+this.getColumnAttributes()+", rawDataHeaders="+this.getRawDataHeaders()+", filePath="+this.getFilePath()+", operType="+this.getOperType()+"\)";
    }
}
